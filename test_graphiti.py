#!/usr/bin/env python3
"""
Test Graphiti Connection
"""
import asyncio
import os
from dotenv import load_dotenv
from graphiti_core import Graphiti

load_dotenv()

async def test_graphiti():
    """Test Graphiti initialization"""
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD", "00000000")
    
    print(f"Testing Graphiti with Neo4j at {uri}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(password)}")
    
    try:
        # Test basic Graphiti initialization
        client = Graphiti(
            uri=uri,
            user=user,
            password=password,
            llm_client=None,  # Use default OpenAI
            embedder=None,    # Use default OpenAI embedder
            store_raw_episode_content=True
        )
        
        print("✅ Graphiti client created")
        
        # Try to build indices (this often causes the auth error)
        await client.build_indices_and_constraints()
        print("✅ Indices and constraints built successfully")
        
        # Try a simple operation
        from graphiti_core.nodes import EpisodeType
        from datetime import datetime, timezone
        
        await client.add_episode(
            name="test_episode",
            episode_body="This is a test episode to verify Graphiti functionality",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="Graphiti test"
        )
        print("✅ Test episode added successfully")
        
        # Clean up test episode
        episodes = await client.search_episodes("test_episode")
        if episodes:
            # We could delete the episode here if needed
            print(f"✅ Found {len(episodes)} episodes in search")
        
        print("🎉 Graphiti is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Graphiti test failed: {e}")
        print(f"Error type: {type(e)}")
        
        # Try with a simpler initialization
        try:
            print("\nTrying simplified initialization...")
            client = Graphiti(
                uri=uri,
                user=user,
                password=password
            )
            print("✅ Simplified Graphiti client created")
            return True
            
        except Exception as e2:
            print(f"❌ Simplified initialization also failed: {e2}")
            return False

if __name__ == "__main__":
    asyncio.run(test_graphiti())