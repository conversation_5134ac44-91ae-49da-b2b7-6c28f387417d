import asyncio
import json
import os
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Optional, Any
from openai import Async<PERSON>penA<PERSON>
from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.edges import EntityEdge
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS

load_dotenv()

class GraphitiOpenAIChat:
    def __init__(self, user_id: str):
        # 初始化 OpenAI 客户端
        self.openai_client = AsyncOpenAI(
            base_url=os.getenv("OPENAI_API_BASE"),
            api_key=os.getenv("OPENAI_API_KEY"),
        )
        
        # 初始化 Graphiti 客户端
        self.graphiti_client = Graphiti(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "00000000")
        )
        
        self.user_id = user_id
        self.user_node_uuid = None
        self.conversation_history = []
        self.model_name = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")  # 添加模型名称参数
        
    async def initialize(self):
        """初始化用户和系统"""
        try:
            await self.graphiti_client.build_indices_and_constraints()
            await self._create_user_node()
            print(f"✅ 用户 {self.user_id} 初始化成功")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            
    async def _create_user_node(self):
        """创建或获取用户节点"""
        # 创建用户节点
        await self.graphiti_client.add_episode(
            name=f"用户_{self.user_id}_创建",
            episode_body=f"用户 {self.user_id} 开始使用系统",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="用户初始化"
        )
        
        # 等待一下让系统处理
        await asyncio.sleep(1)
        
        # 获取用户节点 UUID
        try:
            search_result = await self.graphiti_client._search(
                self.user_id, 
                NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            )
            if search_result.nodes:
                self.user_node_uuid = search_result.nodes[0].uuid
                print(f"✅ 用户节点 UUID: {self.user_node_uuid}")
        except Exception as e:
            print(f"❌ 获取用户节点失败: {e}")

    async def retrieve_memories(self, query: str, num_results: int = 5) -> str:
        """检索相关记忆"""
        try:
            if not self.user_node_uuid:
                return "暂无用户记忆信息"
                
            # 以用户节点为中心搜索相关记忆
            edge_results = await self.graphiti_client.search(
                query=query,
                center_node_uuid=self.user_node_uuid,
                num_results=num_results
            )
            
            if not edge_results:
                return "暂无相关记忆信息"
            
            # 格式化记忆信息
            memories = []
            for edge in edge_results:
                memories.append(f"• {edge.fact}")
            
            return "\n".join(memories)
            
        except Exception as e:
            print(f"❌ 记忆检索错误: {e}")
            return "记忆检索失败"
    
    async def save_conversation(self, user_message: str, assistant_reply: str):
        """保存对话到记忆图谱"""
        conversation_content = f"""
用户 {self.user_id}: {user_message}
助手: {assistant_reply}
        """.strip()
        
        try:
            await self.graphiti_client.add_episode(
                name=f"对话_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                episode_body=conversation_content,
                source=EpisodeType.message,
                reference_time=datetime.now(timezone.utc),
                source_description="用户对话"
            )
        except Exception as e:
            print(f"❌ 保存对话记忆错误: {e}")
    
    async def save_user_info(self, info_type: str, content: str):
        """保存用户信息"""
        try:
            await self.graphiti_client.add_episode(
                name=f"用户信息_{info_type}",
                episode_body=f"用户 {self.user_id} 的{info_type}: {content}",
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="用户信息记录"
            )
        except Exception as e:
            print(f"❌ 保存用户信息错误: {e}")

    async def analyze_user_intent(self, message: str) -> Dict[str, Any]:
        """使用 OpenAI 分析用户意图"""
        try:
            response = await self.openai_client.chat.completions.create(
                model=self.model_name,  # 使用配置的模型名称
                messages=[
                    {
                        "role": "system", 
                        "content": """分析用户消息的意图和关键信息。返回 JSON 格式：
                        {
                            "intent": "问询/购买/投诉/闲聊等",
                            "keywords": ["关键词1", "关键词2"],
                            "entities": {
                                "product": "产品名称",
                                "color": "颜色偏好",
                                "size": "尺寸",
                                "budget": "预算"
                            },
                            "sentiment": "positive/negative/neutral"
                        }"""
                    },
                    {"role": "user", "content": message}
                ],
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
            
        except Exception as e:
            print(f"❌ 意图分析错误: {e}")
            return {"intent": "unknown", "keywords": [], "entities": {}, "sentiment": "neutral"}
    
    async def extract_user_preferences(self, message: str) -> List[str]:
        """提取用户偏好信息"""
        intent_analysis = await self.analyze_user_intent(message)
        preferences = []
        
        entities = intent_analysis.get("entities", {})
        for key, value in entities.items():
            if value:
                preferences.append(f"{key}: {value}")
        
        return preferences

    async def chat(self, user_message: str, save_memory: bool = True) -> str:
        """增强记忆的对话方法"""
        try:
            # 1. 分析用户意图和提取偏好
            intent_data = await self.analyze_user_intent(user_message)
            preferences = await self.extract_user_preferences(user_message)
            
            # 2. 保存新发现的偏好
            if preferences and save_memory:
                for pref in preferences:
                    await self.save_user_info("偏好", pref)
            
            # 3. 检索相关记忆
            memory_query = f"{user_message} {' '.join(intent_data.get('keywords', []))}"
            relevant_memories = await self.retrieve_memories(memory_query)
            
            # 4. 构建上下文提示
            system_prompt = self._build_system_prompt(relevant_memories, intent_data)
            
            # 5. 构建消息历史
            messages = [{"role": "system", "content": system_prompt}]
            
            # 添加最近的对话历史（保留最近5轮）
            recent_history = self.conversation_history[-10:]  # 最近5轮对话（用户+助手）
            messages.extend(recent_history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})
            
            # 6. 调用 OpenAI API
            response = await self.openai_client.chat.completions.create(
                model=self.model_name,  # 使用配置的模型名称
                messages=messages,
                temperature=0.7,
                max_tokens=1000,
                presence_penalty=0.1,
                frequency_penalty=0.1
            )
            
            assistant_reply = response.choices[0].message.content
            
            # 7. 更新对话历史
            self.conversation_history.append({"role": "user", "content": user_message})
            self.conversation_history.append({"role": "assistant", "content": assistant_reply})
            
            # 8. 异步保存对话到记忆
            if save_memory:
                asyncio.create_task(self.save_conversation(user_message, assistant_reply))
            
            return assistant_reply
            
        except Exception as e:
            print(f"❌ 对话处理错误: {e}")
            return "抱歉，我现在无法回复您的消息，请稍后再试。"
    
    def _build_system_prompt(self, memories: str, intent_data: Dict[str, Any]) -> str:
        """构建系统提示"""
        intent = intent_data.get("intent", "未知")
        sentiment = intent_data.get("sentiment", "neutral")
        
        sentiment_map = {
            "positive": "用户情绪积极",
            "negative": "用户情绪消极，需要特别关注",
            "neutral": "用户情绪中性"
        }
        
        return f"""你是一个智能助手，具有记忆功能。

用户相关记忆信息：
{memories if memories else '暂无相关记忆'}

当前对话分析：
- 用户意图：{intent}
- 情绪状态：{sentiment_map.get(sentiment, sentiment)}

请基于以上信息进行回复：
1. 如果记忆中有相关信息，请适当引用并体现出你对用户的了解
2. 根据用户的情绪状态调整回复语调
3. 保持对话自然、友好且有用
4. 如果用户询问之前提到过的信息，请从记忆中回忆并回答
5. 如果发现新的用户偏好或重要信息，请在回复中体现出你已经记住了

回复要求：简洁明了，不超过200字。"""

    async def search_memories_by_type(self, memory_type: str, limit: int = 5) -> List[str]:
        """按类型搜索记忆"""
        try:
            search_query = f"{self.user_id} {memory_type}"
            edge_results = await self.graphiti_client.search(
                query=search_query,
                center_node_uuid=self.user_node_uuid,
                num_results=limit
            )
            
            return [edge.fact for edge in edge_results]
        except Exception as e:
            print(f"❌ 按类型搜索记忆错误: {e}")
            return []
    
    async def get_user_profile(self) -> Dict[str, Any]:
        """获取用户画像"""
        try:
            # 搜索不同类型的用户信息
            preferences = await self.search_memories_by_type("偏好")
            history = await self.search_memories_by_type("购买 历史")
            personal_info = await self.search_memories_by_type("个人信息")
            
            # 使用 OpenAI 生成用户画像总结
            profile_prompt = f"""
基于以下信息，生成用户画像总结：

偏好信息：
{chr(10).join(preferences[:3]) if preferences else '无偏好信息'}

历史记录：
{chr(10).join(history[:3]) if history else '无历史记录'}

个人信息：
{chr(10).join(personal_info[:3]) if personal_info else '无个人信息'}

请用 JSON 格式返回用户画像：
{{
    "summary": "用户总结",
    "key_preferences": ["偏好1", "偏好2"],
    "interaction_style": "互动风格描述"
}}
"""
            
            response = await self.openai_client.chat.completions.create(
                model=self.model_name,  # 使用配置的模型名称
                messages=[{"role": "user", "content": profile_prompt}],
                temperature=0.1
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            print(f"❌ 获取用户画像错误: {e}")
            return {"summary": "暂无用户信息", "key_preferences": [], "interaction_style": "标准"}

class EcommerceAssistant(GraphitiOpenAIChat):
    def __init__(self, user_id: str):
        super().__init__(user_id)
        self.product_categories = ["鞋子", "服装", "配饰"]
    
    async def handle_product_inquiry(self, message: str) -> str:
        """处理产品咨询"""
        # 检索用户购买历史和偏好
        purchase_history = await self.search_memories_by_type("购买")
        preferences = await self.search_memories_by_type("偏好")
        
        # 构建专门的产品咨询提示
        product_system_prompt = f"""你是专业的电商客服助手。

用户购买历史：
{chr(10).join(purchase_history[:3]) if purchase_history else '无购买记录'}

用户偏好：
{chr(10).join(preferences[:3]) if preferences else '无偏好记录'}

请基于用户的历史和偏好来回答产品咨询，提供个性化的建议。
如果用户询问的产品与其偏好不符，请适当提醒并推荐更合适的替代品。
"""
        
        # 使用定制的系统提示
        messages = [
            {"role": "system", "content": product_system_prompt},
            {"role": "user", "content": message}
        ]
        
        response = await self.openai_client.chat.completions.create(
            model=self.model_name,  # 使用配置的模型名称
            messages=messages,
            temperature=0.7
        )
        
        reply = response.choices[0].message.content
        await self.save_conversation(message, reply)
        
        return reply
    
    async def analyze_purchase_intent(self, message: str) -> Dict[str, Any]:
        """分析购买意图"""
        intent_prompt = f"""
分析用户消息的购买意图强度和相关信息：

用户消息: {message}

返回 JSON 格式：
{{
    "purchase_intent": "high/medium/low/none",
    "product_interest": "感兴趣的产品",
    "price_sensitivity": "high/medium/low",
    "urgency": "urgent/normal/low",
    "concerns": ["顾虑1", "顾虑2"]
}}
"""
        
        response = await self.openai_client.chat.completions.create(
            model=self.model_name,  # 使用配置的模型名称
            messages=[{"role": "user", "content": intent_prompt}],
            temperature=0.1
        )
        
        try:
            return json.loads(response.choices[0].message.content)
        except:
            return {"purchase_intent": "low", "product_interest": "", "price_sensitivity": "medium", "urgency": "normal", "concerns": []}

class PersonalAssistant(GraphitiOpenAIChat):
    async def handle_schedule_request(self, message: str) -> str:
        """处理日程相关请求"""
        schedule_memories = await self.search_memories_by_type("日程 会议 约会")
        
        schedule_prompt = f"""你是用户的个人助理。

用户的日程相关记忆：
{chr(10).join(schedule_memories[:5]) if schedule_memories else '无日程记录'}

请帮助用户管理日程，基于历史信息：
1. 避免时间冲突
2. 考虑用户的习惯和偏好
3. 提供合理的建议
"""
        
        messages = [
            {"role": "system", "content": schedule_prompt},
            {"role": "user", "content": message}
        ]
        
        response = await self.openai_client.chat.completions.create(
            model=self.model_name,  # 使用配置的模型名称
            messages=messages,
            temperature=0.7
        )
        
        reply = response.choices[0].message.content
        await self.save_conversation(message, reply)
        
        return reply
    
    async def save_schedule_info(self, event_info: str):
        """保存日程信息"""
        await self.save_user_info("日程", event_info)

async def main():
    # 创建聊天实例
    chat = GraphitiOpenAIChat("user_001")
    await chat.initialize()
    
    print("🤖 智能助手已启动，输入 'quit' 退出")
    
    while True:
        user_input = input("\n👤 您: ")
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        
        if user_input.strip():
            # 获取回复
            response = await chat.chat(user_input)
            print(f"🤖 助手: {response}")
            
            # 显示用户画像（可选）
            if user_input.lower() in ['profile', '画像']:
                profile = await chat.get_user_profile()
                print(f"\n📊 用户画像: {profile}")

async def demo_conversation():
    """演示对话流程"""
    chat = GraphitiOpenAIChat("demo_user")
    await chat.initialize()
    
    # 模拟对话序列
    conversations = [
        "你好，我叫张三",
        "我喜欢蓝色的鞋子，预算在500元以内",
        "有什么推荐的运动鞋吗？",
        "我上次是不是说过我喜欢蓝色？",
        "我的预算是多少来着？"
    ]
    
    for msg in conversations:
        print(f"\n👤 用户: {msg}")
        response = await chat.chat(msg)
        print(f"🤖 助手: {response}")
        print("-" * 50)

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
    
    # 或运行演示
    # asyncio.run(demo_conversation())