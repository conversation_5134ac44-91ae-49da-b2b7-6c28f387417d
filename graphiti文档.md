========================
CODE SNIPPETS
========================
TITLE: Graphiti Add Memory Tool (APIDOC)
DESCRIPTION: Tool for storing new or updated information into the knowledge graph. Used to capture requirements, preferences, procedures, and factual relationships. Best practices include splitting long requirements and being explicit about updates.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_snippet_1

LANGUAGE: APIDOC
CODE:
```
add_memory(type: str, content: str, category: str = None, update_of_uuid: str = None)
  - Adds or updates information in the knowledge graph.
  - Parameters:
    - type: The type of information being added (e.g., 'Requirement', 'Preference', 'Procedure', 'Fact').
    - content: The detailed information to store.
    - category: Optional. A clear label for the preference or procedure for better retrieval.
    - update_of_uuid: Optional. The UUID of the existing memory being updated. Use only if explicitly updating existing knowledge.
  - Returns: The UUID of the newly added or updated memory.
  - Usage:
    - Capture requirements and preferences immediately.
    - Split very long requirements into shorter, logical chunks.
    - Document procedures clearly when discovered.
    - Record factual relationships explicitly.
    - Be specific with categories for preferences and procedures.
```

----------------------------------------

TITLE: LangGraph Setup: StateGraph and MemorySaver
DESCRIPTION: Initializes the LangGraph state management with `StateGraph` and configures memory persistence using `MemorySaver`. This sets up the foundational components for building the agent's workflow.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_16

LANGUAGE: Python
CODE:
```
graph_builder = StateGraph(State)

memory = MemorySaver()
```

----------------------------------------

TITLE: LLM Concurrency Configuration
DESCRIPTION: Explains how to control the concurrency of Graphiti's ingestion pipelines using the SEMAPHORE_LIMIT environment variable. This helps manage rate limits from LLM providers.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_11

LANGUAGE: env
CODE:
```
# Set SEMAPHORE_LIMIT to control concurrent operations
# Default is 10 to prevent LLM provider 429 errors
# Increase for higher ingestion performance if provider allows
SEMAPHORE_LIMIT=20
```

----------------------------------------

TITLE: Graphiti Database Operations
DESCRIPTION: Provides functions for managing the Graphiti Neo4j database, including clearing all data and building necessary indices and constraints. Note: `clear_data` is destructive.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_5

LANGUAGE: python
CODE:
```
# Note: This will clear the database
await clear_data(client.driver)
await client.build_indices_and_constraints()
```

----------------------------------------

TITLE: Graphiti Search Tools (APIDOC)
DESCRIPTION: Tools for querying the knowledge graph to find relevant preferences, procedures, and facts. Supports filtering by entity type and centering searches around specific nodes for targeted results. Essential for understanding existing context before taking action.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_snippet_0

LANGUAGE: APIDOC
CODE:
```
search_nodes(query: str, entity_type: str = None, center_node_uuid: str = None)
  - Searches the knowledge graph for nodes matching the query.
  - Parameters:
    - query: The search term or phrase.
    - entity_type: Optional. Filters results to a specific type (e.g., 'Preference', 'Procedure', 'Requirement').
    - center_node_uuid: Optional. The UUID of a node to center the search around, prioritizing related information.
  - Returns: A list of matching nodes, including their details and relationships.
  - Usage:
    - Always search first to find relevant preferences and procedures.
    - Filter by entity type for targeted results.
    - Review all matches carefully.

search_facts(query: str, center_node_uuid: str = None)
  - Searches the knowledge graph for factual relationships matching the query.
  - Parameters:
    - query: The search term or phrase for facts.
    - center_node_uuid: Optional. The UUID of a node to center the search around.
  - Returns: A list of factual relationships.
  - Usage:
    - Search for facts to discover relationships and information relevant to the task.
    - Combine node and fact searches for complex tasks.
```

----------------------------------------

TITLE: Main Development Commands (Bash)
DESCRIPTION: Essential bash commands for managing the Graphiti project dependencies, code formatting, linting, testing, and running all checks from the project root.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Install dependencies
uv sync --extra dev

# Format code (ruff import sorting + formatting)
make format

# Lint code (ruff + pyright type checking)
make lint

# Run tests
make test

# Run all checks (format, lint, test)
make check
```

----------------------------------------

TITLE: Install uv and Project Dependencies
DESCRIPTION: Installs the `uv` package manager and then uses it to synchronize and install all project dependencies within a virtual environment. Requires Python 3.10+.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_3

LANGUAGE: bash
CODE:
```
# Install uv if you don't have it already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create a virtual environment and install dependencies in one step
uv sync
```

----------------------------------------

TITLE: Configure Neo4j with Custom Database Name
DESCRIPTION: Demonstrates how to instantiate a Neo4j driver with a custom database name and pass it to the Graphiti constructor. This allows for flexible database connection management.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_9

LANGUAGE: python
CODE:
```
from graphiti_core import Graphiti
from graphiti_core.driver.neo4j_driver import Neo4jDriver

# Create a Neo4j driver with custom database name
driver = Neo4jDriver(
    uri="bolt://localhost:7687",
    user="neo4j",
    password="password",
    database="my_custom_database"  # Custom database name
)

# Pass the driver to Graphiti
graphiti = Graphiti(graph_driver=driver)
```

----------------------------------------

TITLE: LangChain and LangGraph Core Imports
DESCRIPTION: Imports necessary components from LangChain and LangGraph libraries for building conversational agents and managing state, including message handling, tools, LLM integration, and graph construction.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_10

LANGUAGE: Python
CODE:
```
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.prebuilt import ToolNode
```

----------------------------------------

TITLE: Chatbot State Definition
DESCRIPTION: Defines the state structure for the chatbot using `TypedDict`. It includes a list of messages (annotated for accumulation), the user's name, and the user's node UUID in the Graphiti graph. This state is managed by LangGraph.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_14

LANGUAGE: Python
CODE:
```
class State(TypedDict):
    messages: Annotated[list, add_messages]
    user_name: str
    user_node_uuid: str
```

----------------------------------------

TITLE: Add Inventory Update Message
DESCRIPTION: Adds a specific inventory update message as a text episode to the Graphiti client. This simulates an update from an 'Inventory Management Bot' regarding product stock status.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_9

LANGUAGE: python
CODE:
```
await client.add_episode(
    name='Inventory management 0',
    episode_body=('All Tinybirds Wool Runners styles are out of stock until December 25th 2024'),
    source=EpisodeType.text,
    reference_time=datetime.now(timezone.utc),
    source_description='Inventory Management Bot',
)
```

----------------------------------------

TITLE: Run FalkorDB via Docker
DESCRIPTION: Starts a FalkorDB instance using Docker. FalkorDB serves as a potential embeddings storage backend for Graphiti, offering a quick way to set up the database.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_8

LANGUAGE: bash
CODE:
```
docker run -p 6379:6379 -p 3000:3000 -it --rm falkordb/falkordb:latest
```

----------------------------------------

TITLE: Graphiti MCP Server Tools
DESCRIPTION: This section lists the available tools exposed by the Graphiti MCP server for interacting with the knowledge graph. Each tool performs a specific operation like adding, searching, or deleting data.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_10

LANGUAGE: APIDOC
CODE:
```
add_episode: Add an episode to the knowledge graph (supports text, JSON, and message formats)
search_nodes: Search the knowledge graph for relevant node summaries
search_facts: Search the knowledge graph for relevant facts (edges between entities)
delete_entity_edge: Delete an entity edge from the knowledge graph
delete_episode: Delete an episode from the knowledge graph
get_entity_edge: Get an entity edge by its UUID
get_episodes: Get the most recent episodes for a specific group
clear_graph: Clear all data from the knowledge graph and rebuild indices
get_status: Get the status of the Graphiti MCP server and Neo4j connection
```

----------------------------------------

TITLE: Configure FalkorDB with Custom Database Name
DESCRIPTION: Illustrates how to create a FalkorDB driver specifying a custom database name and then integrate it with the Graphiti instance. This is useful for non-default FalkorDB setups.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_10

LANGUAGE: python
CODE:
```
from graphiti_core import Graphiti
from graphiti_core.driver.falkordb_driver import FalkorDriver

# Create a FalkorDB driver with custom database name
driver = FalkorDriver(
    host="localhost",
    port=6379,
    username="falkor_user",  # Optional
    password="falkor_password",  # Optional
    database="my_custom_graph"  # Custom database name
)

# Pass the driver to Graphiti
graphiti = Graphiti(graph_driver=driver)
```

----------------------------------------

TITLE: Graphiti Ollama Local LLM Setup
DESCRIPTION: Configure Graphiti to use Ollama for running local LLMs and embedding models. This leverages Ollama's OpenAI-compatible API and is suitable for privacy-focused applications. Ensure models are pulled via Ollama and the correct `base_url` is set.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_15

LANGUAGE: python
CODE:
```
from graphiti_core import Graphiti
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# Configure Ollama LLM client
llm_config = LLMConfig(
    api_key="abc",  # Ollama doesn't require a real API key
    model="deepseek-r1:7b",
    small_model="deepseek-r1:7b",
    base_url="http://localhost:11434/v1", # Ollama provides this port
)

llm_client = OpenAIClient(config=llm_config)

# Example for embedder and cross-encoder if using Ollama's OpenAI compatible API
# embedding_client = OpenAIClient(config=LLMConfig(api_key="abc", model="nomic-embed-text", base_url="http://localhost:11434/v1"))
# embedder = OpenAIEmbedder(config=OpenAIEmbedderConfig(embedding_model="nomic-embed-text"), client=embedding_client)
# cross_encoder_client = OpenAIClient(config=LLMConfig(api_key="abc", model="deepseek-r1:7b", base_url="http://localhost:11434/v1"))
# cross_encoder = OpenAIRerankerClient(llm_config=LLMConfig(model="deepseek-r1:7b"), client=cross_encoder_client)

# Initialize Graphiti with Ollama clients
# graphiti = Graphiti(
#     "bolt://localhost:7687",
#     "neo4j",
#     "password",
#     llm_client=llm_client,
#     embedder=embedder,
#     cross_encoder=cross_encoder
# )

```

----------------------------------------

TITLE: Install Server Dependencies
DESCRIPTION: Installs dependencies specifically for the server component using `uv sync --extra dev`. This prepares the server environment for development.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_5

LANGUAGE: bash
CODE:
```
cd server/
uv sync --extra dev
```

----------------------------------------

TITLE: Format, Lint, Test Server Code
DESCRIPTION: Applies formatting, linting, and testing to the server code using make commands. These commands ensure the server component meets quality standards.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_7

LANGUAGE: bash
CODE:
```
cd server/
make format
make lint
make test
```

----------------------------------------

TITLE: Disable Graphiti Telemetry via Environment Variable (Bash)
DESCRIPTION: Provides instructions on how to disable Graphiti's anonymous telemetry collection by setting the `GRAPHITI_TELEMETRY_ENABLED` environment variable to `false` in a bash shell. This can be done temporarily for the current session.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_17

LANGUAGE: bash
CODE:
```
export GRAPHITI_TELEMETRY_ENABLED=false
```

----------------------------------------

TITLE: Install Graphiti with FalkorDB Support
DESCRIPTION: Installs Graphiti along with the necessary dependencies to use FalkorDB as the graph database backend. This enables Graphiti's advanced features with FalkorDB.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
pip install graphiti-core[falkordb]
```

LANGUAGE: bash
CODE:
```
uv add graphiti-core[falkordb]
```

----------------------------------------

TITLE: LLM Configuration with Tools
DESCRIPTION: Initializes an OpenAI chat model (`ChatOpenAI`) with a specific model name and temperature, then binds the previously defined tools to it. This prepares the LLM to understand and utilize the available tools.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_12

LANGUAGE: Python
CODE:
```
llm = ChatOpenAI(model='gpt-4.1-mini', temperature=0).bind_tools(tools)
```

----------------------------------------

TITLE: Install MCP Server Dependencies
DESCRIPTION: Installs dependencies for the MCP server using `uv sync`. This command prepares the MCP server environment.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_8

LANGUAGE: bash
CODE:
```
cd mcp_server/
uv sync
```

----------------------------------------

TITLE: Run Linting Checks
DESCRIPTION: Command to perform linting checks on the code. This helps maintain code quality and consistency across the project.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_6

LANGUAGE: Shell
CODE:
```
make lint
```

----------------------------------------

TITLE: Initialize Graphiti with Ollama Clients
DESCRIPTION: Demonstrates the initialization of the Graphiti client, connecting to a Neo4j database and configuring LLM and embedder clients using Ollama. This setup requires pre-configured `llm_client` and `llm_config` objects.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_16

LANGUAGE: python
CODE:
```
graphiti = Graphiti(
    "bolt://localhost:7687",
    "neo4j",
    "password",
    llm_client=llm_client,
    embedder=OpenAIEmbedder(
        config=OpenAIEmbedderConfig(
            api_key="abc",
            embedding_model="nomic-embed-text",
            embedding_dim=768,
            base_url="http://localhost:11434/v1",
        )
    ),
    cross_encoder=OpenAIRerankerClient(client=llm_client, config=llm_config),
)
```

----------------------------------------

TITLE: Install Graphiti with Anthropic LLM Support
DESCRIPTION: Installs Graphiti with support for Anthropic's LLM services. This allows Graphiti to leverage Anthropic models for embedding and inference.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_3

LANGUAGE: bash
CODE:
```
pip install graphiti-core[anthropic]
```

----------------------------------------

TITLE: Install Graphiti with Multiple LLM Providers
DESCRIPTION: Installs Graphiti with support for multiple LLM providers simultaneously, such as Anthropic and Google Gemini. This provides flexibility in choosing LLM backends.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_6

LANGUAGE: bash
CODE:
```
pip install graphiti-core[anthropic,groq,google-genai]
```

----------------------------------------

TITLE: Utility Function for Formatting Graphiti Edges
DESCRIPTION: A helper function to format a list of EntityEdge objects into a multi-line string, suitable for display or passing to an LLM. Each fact is prefixed with a hyphen.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_9

LANGUAGE: Python
CODE:
```
def edges_to_facts_string(entities: list[EntityEdge]):
    return '-' + '\n- '.join([edge.fact for edge in entities])
```

----------------------------------------

TITLE: Disable Graphiti Telemetry in Python Session
DESCRIPTION: Demonstrates how to disable Graphiti's anonymous telemetry collection programmatically within a Python script or session by setting the `GRAPHITI_TELEMETRY_ENABLED` environment variable to `false` using the `os` module before initializing Graphiti.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_19

LANGUAGE: python
CODE:
```
import os
os.environ['GRAPHITI_TELEMETRY_ENABLED'] = 'false'

# Then initialize Graphiti as usual
from graphiti_core import Graphiti
graphiti = Graphiti(...)
```

----------------------------------------

TITLE: Tool Node Invocation Example
DESCRIPTION: Demonstrates how to invoke the `tool_node` asynchronously. It passes a dictionary containing a list of messages, where the last message is generated by the LLM after an initial query for 'wool shoes'. This tests the tool execution flow.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_13

LANGUAGE: Python
CODE:
```
# Test the tool node
await tool_node.ainvoke({'messages': [await llm.ainvoke('wool shoes')]})
```

----------------------------------------

TITLE: Disable Graphiti Telemetry
DESCRIPTION: Environment variable to disable anonymous telemetry collection in the Graphiti MCP server. Can be set directly or added to a `.env` file.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_17

LANGUAGE: bash
CODE:
```
export GRAPHITI_TELEMETRY_ENABLED=false
```

----------------------------------------

TITLE: Install Graphiti with Groq LLM Support
DESCRIPTION: Installs Graphiti with support for Groq's LLM services. This enables Graphiti to utilize Groq's fast inference capabilities.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_4

LANGUAGE: bash
CODE:
```
pip install graphiti-core[groq]
```

----------------------------------------

TITLE: Set Environment Variables
DESCRIPTION: Configures essential environment variables for Graphiti, including the OpenAI API key for LLM and embedding functionalities, and optional connection parameters for Neo4j or FalkorDB.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/quickstart/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
export OPENAI_API_KEY=your_openai_api_key

# Optional Neo4j connection parameters (defaults shown)
export NEO4J_URI=bolt://localhost:7687
export NEO4J_USER=neo4j
export NEO4J_PASSWORD=00000000

# Optional FalkorDB connection parameters (defaults shown)
export FALKORDB_URI=falkor://localhost:6379
```

----------------------------------------

TITLE: Chatbot Function Logic
DESCRIPTION: The core `chatbot` function orchestrates the conversational flow. It retrieves context from Graphiti based on the latest message and user ID, constructs a system message with this context, and invokes the LLM. It also asynchronously adds the interaction to Graphiti for persistence.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_15

LANGUAGE: Python
CODE:
```
async def chatbot(state: State):
    facts_string = None
    if len(state['messages']) > 0:
        last_message = state['messages'][-1]
        graphiti_query = f'{"SalesBot" if isinstance(last_message, AIMessage) else state["user_name"]}: {last_message.content}'
        # search graphiti using Jess's node uuid as the center node
        # graph edges (facts) further from the Jess node will be ranked lower
        edge_results = await client.search(
            graphiti_query, center_node_uuid=state['user_node_uuid'], num_results=5
        )
        facts_string = edges_to_facts_string(edge_results)

    system_message = SystemMessage(
        content=f"""You are a skillfull shoe salesperson working for ManyBirds. Review information about the user and their prior conversation below and respond accordingly.
        Keep responses short and concise. And remember, always be selling (and helpful!)

        Things you'll need to know about the user in order to close a sale:
        - the user's shoe size
        - any other shoe needs? maybe for wide feet?
        - the user's preferred colors and styles
        - their budget

        Ensure that you ask the user for the above if you don't already know.

        Facts about the user and their conversation:
        {facts_string or 'No facts about the user and their conversation'} """
    )

    messages = [system_message] + state['messages']

    response = await llm.ainvoke(messages)

    # add the response to the graphiti graph.
    # this will allow us to use the graphiti search later in the conversation
    # we're doing async here to avoid blocking the graph execution
    asyncio.create_task(
        client.add_episode(
            name='Chatbot Response',
            episode_body=f'{state["user_name"]}: {state["messages"][-1]}\nSalesBot: {response.content}',
            source=EpisodeType.message,
            reference_time=datetime.now(timezone.utc),
            source_description='Chatbot',
        )
    )

    return {'messages': [response]}
```

----------------------------------------

TITLE: Push Changes to Fork
DESCRIPTION: Command to push your committed changes from your local branch to your forked repository on GitHub.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_8

LANGUAGE: Git
CODE:
```
git push origin your-branch-name
```

----------------------------------------

TITLE: MCP Server Development (Bash)
DESCRIPTION: Instructions for setting up and running the Model Context Protocol (MCP) server, including dependency installation and deployment using Docker Compose.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_2

LANGUAGE: bash
CODE:
```
cd mcp_server/
# Install MCP server dependencies
uv sync

# Run with Docker Compose
docker-compose up
```

----------------------------------------

TITLE: Commit Changes
DESCRIPTION: Command to stage and commit your changes with a descriptive message. A clear commit message is crucial for tracking changes.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_7

LANGUAGE: Git
CODE:
```
git commit -m "Your detailed commit message"
```

----------------------------------------

TITLE: Graphiti Client Initialization and Node Retrieval
DESCRIPTION: Initializes a Graphiti client and retrieves a specific node's UUID, likely for context centering in subsequent searches. This sets up the environment for interacting with the Graphiti graph.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_8

LANGUAGE: Python
CODE:
```
nl = await client._search('ManyBirds', NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
manybirds_node_uuid = nl.nodes[0].uuid
```

----------------------------------------

TITLE: Format Code
DESCRIPTION: Command to format the codebase according to project standards. This typically involves running linters and auto-formatters.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_5

LANGUAGE: Shell
CODE:
```
make format
```

----------------------------------------

TITLE: Pyright Configuration for Graphiti
DESCRIPTION: Configuration snippet for Pyright, the static type checker, showing different type checking modes for the main project and the server components.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_3

LANGUAGE: json
CODE:
```
{
  "pyright": {
    "typeCheckingMode": "basic" // For main project
  }
}

// In server/pyproject.toml or similar:
{
  "pyright": {
    "typeCheckingMode": "standard" // For server components
  }
}
```

----------------------------------------

TITLE: Conditional Imports for Optional Dependencies
DESCRIPTION: Demonstrates a Python pattern for conditionally importing optional dependencies using `typing.TYPE_CHECKING` and error handling for `ImportError`. This ensures the core library remains lightweight and provides clear installation instructions.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_11

LANGUAGE: python
CODE:
```
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    import your_package
    from your_package import SomeType
else:
    try:
        import your_package
        from your_package import SomeType
    except ImportError:
        raise ImportError(
            'your-package is required for YourServiceClient. '
            'Install it with: pip install graphiti-core[your-service]'
        ) from None
```

----------------------------------------

TITLE: Disable Graphiti MCP Server Telemetry
DESCRIPTION: Shows how to disable anonymous telemetry collection in the Graphiti MCP server. This can be done by setting the `GRAPHITI_TELEMETRY_ENABLED` environment variable to `false`.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_18

LANGUAGE: Bash
CODE:
```
export GRAPHITI_TELEMETRY_ENABLED=false
```

LANGUAGE: Bash
CODE:
```
# Or add to your .env file:
# GRAPHITI_TELEMETRY_ENABLED=false
```

----------------------------------------

TITLE: Define Optional Dependencies in pyproject.toml
DESCRIPTION: Specifies optional dependencies for third-party integrations in the `pyproject.toml` file. It shows how to define an extra for a service and include it in the development dependencies.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_10

LANGUAGE: toml
CODE:
```
[project.optional-dependencies]
your-service = ["your-package>=1.0.0"]
dev = [
    # ... existing dev dependencies
    "your-package>=1.0.0",  # Include all optional extras here
    # ... other dependencies
]
```

----------------------------------------

TITLE: Define LangGraph Agent Logic and Build Graph
DESCRIPTION: Defines the `should_continue` function for conditional branching in the agent's execution flow and demonstrates building the LangGraph using nodes, edges, and conditional edges.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_20

LANGUAGE: Python
CODE:
```
async def should_continue(state, config):
    messages = state['messages']
    last_message = messages[-1]
    # If there is no function call, then we finish
    if not last_message.tool_calls:
        return 'end'
    # Otherwise if there is, we continue
    else:
        return 'continue'


graph_builder.add_node('agent', chatbot)
graph_builder.add_node('tools', tool_node)

graph_builder.add_edge(START, 'agent')
graph_builder.add_conditional_edges('agent', should_continue, {'continue': 'tools', 'end': END})
graph_builder.add_edge('tools', 'agent')

graph = graph_builder.compile(checkpointer=memory)
```

----------------------------------------

TITLE: Add Episode with JSON Source
DESCRIPTION: Demonstrates using the `add_episode` tool with `source="json"` to process structured JSON data, automatically extracting entities and relationships from CRM data.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_11

LANGUAGE: python
CODE:
```
add_episode(
name="Customer Profile",
episode_body="{\"company\": {\"name\": \"Acme Technologies\"}, \"products\": [{\"id\": \"P001\", \"name\": \"CloudSync\"}, {\"id\": \"P002\", \"name\": \"DataMiner\"}]}",
source="json",
source_description="CRM data"
)
```

----------------------------------------

TITLE: Install Graphiti with FalkorDB and LLM Providers
DESCRIPTION: Installs Graphiti with both FalkorDB support and additional LLM provider support (e.g., Anthropic, Google Gemini). This configuration provides a comprehensive setup for advanced RAG applications.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_7

LANGUAGE: bash
CODE:
```
pip install graphiti-core[falkordb,anthropic,google-genai]
```

----------------------------------------

TITLE: Server Development Commands (Bash)
DESCRIPTION: Commands for setting up and running the FastAPI server for Graphiti, including dependency installation, code formatting, linting, and testing specific to the server directory.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_1

LANGUAGE: bash
CODE:
```
cd server/
# Install server dependencies
uv sync --extra dev

# Run server in development mode
uvicorn graph_service.main:app --reload

# Format, lint, test server code
make format
make lint
make test
```

----------------------------------------

TITLE: Testing Commands (Bash)
DESCRIPTION: Bash commands for executing tests in the Graphiti project, including running all tests, specific files, methods, and filtering by integration or unit tests.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_4

LANGUAGE: bash
CODE:
```
# Run all tests
make test

# Run specific test files
pytest tests/test_specific_file.py

# Run specific test methods
pytest tests/test_file.py::test_method_name

# Run only integration tests
pytest tests/ -k "_int"

# Run only unit tests
pytest tests/ -k "not _int"
```

----------------------------------------

TITLE: Run Code Quality Checks
DESCRIPTION: Executes a Makefile target to format code, run linters, and execute tests using tools like Ruff and Pytest. This is a prerequisite before submitting pull requests.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_9

LANGUAGE: shell
CODE:
```
make check
```

----------------------------------------

TITLE: Required Environment Variables
DESCRIPTION: Environment variables that must be passed to the graph-service for proper initialization and connection to external services like OpenAI and Neo4j.

SOURCE: https://github.com/getzep/graphiti/blob/main/server/README.md#_snippet_1

LANGUAGE: env
CODE:
```
OPENAI_API_KEY=your_openai_api_key
NEO4J_USER=your_neo4j_user
NEO4J_PASSWORD=your_neo4j_password
NEO4J_PORT=your_neo4j_port
```

----------------------------------------

TITLE: Configure Environment Variables Directly
DESCRIPTION: This snippet demonstrates setting environment variables directly when running Docker Compose commands. This is an alternative to using a .env file for quick configuration or scripting.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_6

LANGUAGE: bash
CODE:
```
OPENAI_API_KEY=your_key MODEL_NAME=gpt-4.1-mini docker compose up
```

----------------------------------------

TITLE: Ingest Product Data into Graphiti
DESCRIPTION: Loads product data from a JSON file into the Graphiti graph. Each product is added as an episode with its details, source description, and reference time. This operation may take time and needs to be rerun if `clear_data` is used.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_6

LANGUAGE: python
CODE:
```
async def ingest_products_data(client: Graphiti):
    script_dir = Path.cwd().parent
    json_file_path = script_dir / 'data' / 'manybirds_products.json'

    with open(json_file_path) as file:
        products = json.load(file)['products']

    for i, product in enumerate(products):
        await client.add_episode(
            name=product.get('title', f'Product {i}'),
            episode_body=str({k: v for k, v in product.items() if k != 'images'}), # Exclude images from body
            source_description='ManyBirds products',
            source=EpisodeType.json,
            reference_time=datetime.now(timezone.utc),
        )


await ingest_products_data(client)
```

----------------------------------------

TITLE: Disable Graphiti Telemetry in Shell Profile (Bash)
DESCRIPTION: Explains how to permanently disable Graphiti's anonymous telemetry collection by adding an export command to shell profile files like `.bashrc` or `.zshrc`. This ensures telemetry is off for all new shell sessions.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_18

LANGUAGE: bash
CODE:
```
# For bash users (~/.bashrc or ~/.bash_profile)
echo 'export GRAPHITI_TELEMETRY_ENABLED=false' >> ~/.bashrc

# For zsh users (~/.zshrc)
echo 'export GRAPHITI_TELEMETRY_ENABLED=false' >> ~/.zshrc
```

----------------------------------------

TITLE: `get_shoe_data` Tool Definition
DESCRIPTION: Defines an asynchronous tool named `get_shoe_data` that searches the Graphiti graph for shoe-related information. It uses a provided query and centers the search around a specific node UUID (`manybirds_node_uuid`) to prioritize relevant results. The output is formatted using `edges_to_facts_string`.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_11

LANGUAGE: Python
CODE:
```
@tool
async def get_shoe_data(query: str) -> str:
    """Search the graphiti graph for information about shoes"""
    edge_results = await client.search(
        query,
        center_node_uuid=manybirds_node_uuid,
        num_results=10,
    )
    return edges_to_facts_string(edge_results)

tools = [get_shoe_data]
tool_node = ToolNode(tools)
```

----------------------------------------

TITLE: Start Graphiti MCP Server (SSE Transport)
DESCRIPTION: Command to start the Graphiti MCP server using the SSE transport. Optionally specify a `group_id` for namespacing graph data. Requires Python 3.10+.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_12

LANGUAGE: bash
CODE:
```
python graphiti_mcp_server.py --transport sse --use-custom-entities --group-id <your_group_id>
```

----------------------------------------

TITLE: Configure Cursor for Graphiti MCP Server
DESCRIPTION: JSON configuration snippet for the Cursor IDE to connect to a Graphiti MCP server instance using the SSE endpoint.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_14

LANGUAGE: json
CODE:
```
{
  "mcpServers": {
    "graphiti-memory": {
      "url": "http://localhost:8000/sse"
    }
  }
}
```

----------------------------------------

TITLE: Navigate to Graphiti Directory
DESCRIPTION: Commands to change the current directory to the Graphiti project root or the mcp_server subdirectory. Essential for executing subsequent setup and run commands.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
cd graphiti && pwd
```

LANGUAGE: bash
CODE:
```
cd graphiti/mcp_server
```

----------------------------------------

TITLE: Initialize Graph and Ingest Data
DESCRIPTION: Executes initial setup steps for the Graphiti graph. This includes clearing existing data, building necessary indices and constraints, and ingesting product data from the JSON file.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_8

LANGUAGE: python
CODE:
```
await clear_data(client.driver)
await client.build_indices_and_constraints()
await ingest_products_data(client)
```

----------------------------------------

TITLE: Run Project Tests
DESCRIPTION: Command to execute all project tests. This ensures that your changes have not introduced regressions and that the codebase remains stable.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_4

LANGUAGE: Shell
CODE:
```
make test
```

----------------------------------------

TITLE: LangChain Core Components API Reference
DESCRIPTION: This section outlines key classes and functions from the LangChain Core library used for LLM interactions and tool definitions. It includes message types, tool decorators, and LLM bindings.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_19

LANGUAGE: APIDOC
CODE:
```
AIMessage(content: str, **kwargs) -> AIMessage
  Represents a message from an AI assistant.

SystemMessage(content: str, **kwargs) -> SystemMessage
  Represents a system-level instruction or context message.

tool: Decorator to define a function as a LangChain tool.
  Usage: @tool

ChatOpenAI(model: str, temperature: float = 0, **kwargs) -> ChatOpenAI
  An interface for OpenAI's chat models.
  Parameters:
    model: The name of the OpenAI model to use (e.g., 'gpt-4.1-mini').
    temperature: Controls the randomness of the output (0 for deterministic).
  Methods:
    bind_tools(tools: list) -> ChatOpenAI
      Binds a list of tools to the chat model, enabling tool use.

BaseTool: Abstract base class for tools.
```

----------------------------------------

TITLE: Clone Graphiti Repository
DESCRIPTION: Demonstrates how to clone the Graphiti GitHub repository using either the standard Git command or the GitHub CLI. This is the first step in setting up the project locally.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
git clone https://github.com/getzep/graphiti.git
```

LANGUAGE: bash
CODE:
```
gh repo clone getzep/graphiti
```

----------------------------------------

TITLE: Run Specific Test Methods
DESCRIPTION: Executes a particular test method within a file using pytest. This is useful for debugging individual test cases.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_11

LANGUAGE: bash
CODE:
```
pytest tests/test_file.py::test_method_name
```

----------------------------------------

TITLE: Run Graphiti MCP Server
DESCRIPTION: Executes the Graphiti MCP server using the `uv` command. Allows overriding default configuration via command-line arguments for model, transport, and other settings.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_4

LANGUAGE: bash
CODE:
```
uv run graphiti_mcp_server.py
```

LANGUAGE: bash
CODE:
```
uv run graphiti_mcp_server.py --model gpt-4.1-mini --transport sse
```

----------------------------------------

TITLE: Graphiti Client API Reference
DESCRIPTION: This section outlines the core methods used to interact with the Graphiti graph. It includes methods for searching nodes, adding episodes (conversational turns or events), and retrieving node information. These methods are crucial for context retrieval and knowledge persistence.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_17

LANGUAGE: APIDOC
CODE:
```
GraphitiClient:
  _search(entity_type: str, search_type: str, **kwargs) -> SearchResult
    Searches the graph for entities of a specific type and search type.
    Parameters:
      entity_type: The type of entity to search for (e.g., 'ManyBirds').
      search_type: The type of search to perform (e.g., NODE_HYBRID_SEARCH_EPISODE_MENTIONS).
    Returns:
      A SearchResult object containing matching nodes.

  search(query: str, center_node_uuid: str = None, num_results: int = 10, **kwargs) -> list[EntityEdge]
    Performs a semantic search within the graph, optionally centering results around a specific node.
    Parameters:
      query: The search query string.
      center_node_uuid: The UUID of the node to use as the center for ranking results.
      num_results: The maximum number of results to return.
    Returns:
      A list of EntityEdge objects representing the search results.

  add_episode(name: str, episode_body: str, source: EpisodeType, reference_time: datetime, source_description: str = None, **kwargs)
    Adds a new episode (e.g., a conversation turn, an event) to the graph.
    Parameters:
      name: A descriptive name for the episode.
      episode_body: The content or body of the episode.
      source: The type of source for the episode (e.g., EpisodeType.message).
      reference_time: The timestamp for the episode.
      source_description: An optional description of the source.
    Returns:
      None (operation is asynchronous).

SearchResult:
  nodes: list[Node]
    A list of nodes found in the search result.

Node:
  uuid: str
    The unique identifier of the node.

EntityEdge:
  fact: str
    The factual content of the edge.

EpisodeType:
  message: Enum value for message-based episodes.
  event: Enum value for event-based episodes.
```

----------------------------------------

TITLE: Install Graphiti with Google Gemini Support
DESCRIPTION: Installs Graphiti with support for Google Gemini LLM services. This allows Graphiti to integrate with Google's generative AI models.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_5

LANGUAGE: bash
CODE:
```
pip install graphiti-core[google-genai]
```

----------------------------------------

TITLE: Visualize LangGraph Structure
DESCRIPTION: Shows how to render the compiled LangGraph as a Mermaid PNG image for visualization, allowing developers to inspect the graph's structure.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_21

LANGUAGE: Python
CODE:
```
with suppress(Exception):
    display(Image(graph.get_graph().draw_mermaid_png()))
```

----------------------------------------

TITLE: Display Pre-generated Graph Image
DESCRIPTION: Illustrates how to display a static image file, typically used for showing pre-rendered visualizations of the agent's graph or state.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_23

LANGUAGE: Python
CODE:
```
display(Image(filename='tinybirds-jess.png', width=850))
```

----------------------------------------

TITLE: Install Project Dependencies
DESCRIPTION: Command to install all necessary project dependencies using the provided Makefile. This ensures the development environment is correctly configured.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: Shell
CODE:
```
make install
```

----------------------------------------

TITLE: Set Up Integration Test Environment Variables
DESCRIPTION: Environment variables required for running integration tests, including API keys for services like OpenAI and Anthropic, and connection details for Neo4j. These variables must be exported before running tests.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_2

LANGUAGE: Shell
CODE:
```
export TEST_OPENAI_API_KEY=...
export TEST_OPENAI_MODEL=...
export TEST_ANTHROPIC_API_KEY=...

# For Neo4j
export TEST_URI=neo4j://...
export TEST_USER=...
export TEST_PASSWORD=...
```

----------------------------------------

TITLE: Import Core Libraries
DESCRIPTION: Imports essential Python libraries for data handling, logging, environment variables, and the Graphiti core components. It sets up the necessary tools for running the e-commerce runner.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_0

LANGUAGE: python
CODE:
```
import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path

from dotenv import load_dotenv
from rich.pretty import pprint

from graphiti_core import Graphiti
from graphiti_core.edges import EntityEdge
from graphiti_core.llm_client.anthropic_client import AnthropicClient
from graphiti_core.nodes import EpisodeType
from graphiti_core.utils.bulk_utils import RawEpisode
from graphiti_core.utils.maintenance.graph_data_operations import clear_data

load_dotenv()
```

----------------------------------------

TITLE: Add Messages to Graph
DESCRIPTION: An asynchronous function to add a list of messages as episodes to the Graphiti client. Each message is stored with a unique name, its content, source type, and a reference timestamp.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_4

LANGUAGE: python
CODE:
```
async def add_messages(client: Graphiti, messages: list[str], prefix: str = 'Message'):
    for i, message in enumerate(messages):
        await client.add_episode(
            name=f'{prefix}-{i}',
            episode_body=message,
            source=EpisodeType.message,
            reference_time=datetime.now(timezone.utc),
            source_description='Shoe conversation',
        )
```

----------------------------------------

TITLE: Configure LangSmith Tracing
DESCRIPTION: Sets environment variables for LangSmith tracing, enabling or disabling tracing and specifying the project name.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
os.environ['LANGCHAIN_TRACING_V2'] = 'false'
os.environ['LANGCHAIN_PROJECT'] = 'Graphiti LangGraph Tutorial'
```

----------------------------------------

TITLE: Configure MCP Client for Stdio Transport
DESCRIPTION: This JSON configuration shows how to set up an MCP client to communicate with the Graphiti MCP server using the 'stdio' transport. It specifies the path to the uv binary and project details.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_8

LANGUAGE: json
CODE:
```
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "/Users/<USER>/.local/bin/uv",
      "args": [
        "run",
        "--isolated",
        "--directory",
        "/Users/<USER>/dev/zep/graphiti/mcp_server",
        "--project",
        ".",
        "graphiti_mcp_server.py",
        "--transport",
        "stdio"
      ],
      "env": {
        "NEO4J_URI": "bolt://localhost:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "password",
        "OPENAI_API_KEY": "sk-XXXXXXXX",
        "MODEL_NAME": "gpt-4.1-mini"
      }
    }
  }
}
```

----------------------------------------

TITLE: Run Server in Development Mode
DESCRIPTION: Runs the FastAPI server in development mode using `uvicorn --reload`. This command starts the API service with automatic reloading on code changes.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_6

LANGUAGE: bash
CODE:
```
cd server/
uvicorn graph_service.main:app --reload
```

----------------------------------------

TITLE: Run Quickstart Example
DESCRIPTION: Executes the Graphiti quickstart example script. Use the appropriate command based on whether you are connecting to Neo4j or FalkorDB.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/quickstart/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
python quickstart_neo4j.py

# For FalkorDB
python quickstart_falkordb.py
```

----------------------------------------

TITLE: Clone and Setup Graphiti Repository
DESCRIPTION: Steps to clone the Graphiti repository from GitHub and navigate into the project directory. This is the initial setup required before proceeding with development.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: Git
CODE:
```
git clone https://github.com/getzep/graphiti
cd graphiti
```

----------------------------------------

TITLE: Start Graphiti MCP Server (Docker)
DESCRIPTION: Command to start the Graphiti MCP server using Docker Compose. This is the recommended method for running the server.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_13

LANGUAGE: bash
CODE:
```
docker compose up
```

----------------------------------------

TITLE: Install Graphiti with Google Gemini Support
DESCRIPTION: Install the `graphiti-core` library with Google Gemini capabilities. This can be done using either `uv` or `pip`, ensuring the necessary dependencies for Gemini are included.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_13

LANGUAGE: bash
CODE:
```
uv add "graphiti-core[google-genai]"

# or

pip install "graphiti-core[google-genai]"
```

----------------------------------------

TITLE: Run Graphiti MCP with Docker Compose
DESCRIPTION: These commands initiate the Graphiti MCP server and Neo4j database using Docker Compose. It ensures dependencies are installed via uv and connects the services.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_7

LANGUAGE: bash
CODE:
```
docker compose up
```

LANGUAGE: bash
CODE:
```
docker-compose up
```

----------------------------------------

TITLE: Install Dependencies
DESCRIPTION: Installs necessary Python packages for LangGraph and Graphiti, including graphiti-core, langchain-openai, and ipywidgets.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_0

LANGUAGE: shell
CODE:
```
pip install graphiti-core langchain-openai langgraph ipywidgets
```

----------------------------------------

TITLE: Graph Service Docker Compose Configuration
DESCRIPTION: Example Docker Compose setup for running the graph-service alongside a Neo4j instance. This configuration specifies the service images, ports, environment variables, and volumes required for operation.

SOURCE: https://github.com/getzep/graphiti/blob/main/server/README.md#_snippet_0

LANGUAGE: yaml
CODE:
```
version: '3.8'

services:
  graph:
    image: zepai/graphiti:latest
    ports:
      - "8000:8000"
    
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - NEO4J_URI=bolt://neo4j:${NEO4J_PORT}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
  neo4j:
    image: neo4j:5.22.0
    
    ports:
      - "7474:7474"  # HTTP
      - "${NEO4J_PORT}:${NEO4J_PORT}"  # Bolt
    volumes:
      - neo4j_data:/data
    environment:
      - NEO4J_AUTH=${NEO4J_USER}/${NEO4J_PASSWORD}

volumes:
  neo4j_data:
```

----------------------------------------

TITLE: Import Core Libraries
DESCRIPTION: Imports essential Python libraries for building the agent, including asyncio, json, logging, os, sys, uuid, contextlib, datetime, pathlib, typing, ipywidgets, dotenv, IPython.display, and typing_extensions.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_1

LANGUAGE: python
CODE:
```
import asyncio
import json
import logging
import os
import sys
import uuid
from contextlib import suppress
from datetime import datetime, timezone
from pathlib import Path
from typing import Annotated

import ipywidgets as widgets
from dotenv import load_dotenv
from IPython.display import Image, display
from typing_extensions import TypedDict

load_dotenv()
```

----------------------------------------

TITLE: Run LangGraph Agent with Single Invocation
DESCRIPTION: Demonstrates how to invoke the compiled LangGraph agent with initial user input and configuration, simulating a single interaction with the agent.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_22

LANGUAGE: Python
CODE:
```
await graph.ainvoke(
    {
        'messages': [
            {
                'role': 'user',
                'content': 'What sizes do the TinyBirds Wool Runners in Natural Black come in?',
            }
        ],
        'user_name': user_name,
        'user_node_uuid': user_node_uuid,
    },
    config={'configurable': {'thread_id': uuid.uuid4().hex}},
)
```

----------------------------------------

TITLE: Define Sample Conversations
DESCRIPTION: Provides two sample customer service conversations as lists of strings. These are used to simulate user interactions and test the Graphiti library's ability to process conversational data.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_3

LANGUAGE: python
CODE:
```
shoe_conversation_1 = [
    "SalesBot (2024-07-30T00:00:00Z): Hi, I'm ManyBirds Assistant! How can I help you today?",
    "John (2024-07-30T00:01:00Z): Hi, I'm looking for a new pair of shoes.",
    'SalesBot (2024-07-30T00:02:00Z): Of course! What kind of material are you looking for?',
    "John (2024-07-30T00:03:00Z): I'm allergic to wool. Also, I'm a size 10 if that helps?",
    "SalesBot (2024-07-30T00:04:00Z): We have just what you are looking for, how do you like our Men's Couriers. They have a retro silhouette look and from cotton. How about them in Basin Blue?",
    "John (2024-07-30T00:05:00Z): Blue is great! Love the look. I'll take them.",
]

shoe_conversation_2 = [
    'SalesBot (2024-08-20T00:00:00Z): Hi John, how can I assist you today?',
    "John (2024-08-20T00:01:00Z): Hi, I need to return the Men's Couriers I bought recently. They're too tight for my wide feet. Hahaha.",
    "SalesBot (2024-08-20T00:02:00Z): I'm sorry to hear that. We can process the return for you.",
]
```

----------------------------------------

TITLE: Install Graphiti Core with uv
DESCRIPTION: Installs the core Graphiti Python package using the 'uv' package installer. 'uv' is a fast, modern Python package installer and resolver.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
uv add graphiti-core
```

----------------------------------------

TITLE: Search for Product Compatibility
DESCRIPTION: Executes a search query to determine if John can wear ManyBirds Wool Runners, requesting up to 3 results. The output includes a separator and a header before printing the search results.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_14

LANGUAGE: python
CODE:
```
r = await client.search('Can John wear ManyBirds Wool Runners?', num_results=3)

print('-' * 100)
print('Standard Reciprocal Rank Fusion Reranking')
print('-' * 100)
for record in r:
    pretty_print(record)
```

----------------------------------------

TITLE: Configure Environment Variables with .env File
DESCRIPTION: This snippet shows how to set up environment variables for the Graphiti MCP server by copying an example .env file and editing it. It's the recommended method for configuring API keys and model names.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_5

LANGUAGE: bash
CODE:
```
cp .env.example .env
# Edit .env file:
# OPENAI_API_KEY=your_openai_api_key_here
# MODEL_NAME=gpt-4.1-mini
# OPENAI_BASE_URL=https://api.openai.com/v1 (optional)
```

----------------------------------------

TITLE: Run Specific Test Files
DESCRIPTION: Executes tests within a specific file using pytest. This allows for focused testing during development.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_10

LANGUAGE: bash
CODE:
```
pytest tests/test_specific_file.py
```

----------------------------------------

TITLE: Create a New Feature Branch
DESCRIPTION: Command to create a new Git branch for your changes. It's best practice to branch off the main development line before making modifications.

SOURCE: https://github.com/getzep/graphiti/blob/main/CONTRIBUTING.md#_snippet_3

LANGUAGE: Git
CODE:
```
git checkout -b your-branch-name
```

----------------------------------------

TITLE: Search for John's Shoe Size
DESCRIPTION: Queries the Graphiti client to find John's shoe size and displays the top 2 results using pretty printing. This demonstrates retrieving specific user attributes from the graph.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_12

LANGUAGE: python
CODE:
```
r = await client.search("What is John's shoe size?", num_results=2)

pretty_print(r)
```

----------------------------------------

TITLE: Install Graphiti Core
DESCRIPTION: Installs the core Graphiti Python package. This is the base installation for using Graphiti.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
pip install graphiti-core
```

----------------------------------------

TITLE: Pretty Print Entity Data
DESCRIPTION: A utility function to display Graphiti entities (nodes or edges) in a human-readable format using `rich.pretty.pprint`. It excludes sensitive or large embedding data for cleaner output.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_6

LANGUAGE: python
CODE:
```
def pretty_print(entity: EntityEdge | list[EntityEdge]):
    if isinstance(entity, EntityEdge):
        data = {k: v for k, v in entity.model_dump().items() if k != 'fact_embedding'}
    elif isinstance(entity, list):
        data = [{k: v for k, v in e.model_dump().items() if k != 'fact_embedding'} for e in entity]
    else:
        pprint(entity)
        return
    pprint(data)
```

----------------------------------------

TITLE: Graphiti Google Gemini Setup
DESCRIPTION: Integrate Graphiti with Google's Gemini models for LLM inference, embeddings, and cross-encoding. This requires providing your Google API key and specifying the desired Gemini model names.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_14

LANGUAGE: python
CODE:
```
from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.cross_encoder.gemini_reranker_client import GeminiRerankerClient

# Google API key configuration
api_key = "<your-google-api-key>"

# Initialize Graphiti with Gemini clients
graphiti = Graphiti(
    "bolt://localhost:7687",
    "neo4j",
    "password",
    llm_client=GeminiClient(
        config=LLMConfig(
            api_key=api_key,
            model="gemini-2.0-flash"
        )
    ),
    embedder=GeminiEmbedder(
        config=GeminiEmbedderConfig(
            api_key=api_key,
            embedding_model="embedding-001"
        )
    ),
    cross_encoder=GeminiRerankerClient(
        config=LLMConfig(
            api_key=api_key,
            model="gemini-2.5-flash-lite-preview-06-17"
        )
    )
)

# Now you can use Graphiti with Google Gemini for all components

```

----------------------------------------

TITLE: Install mcp-remote Globally
DESCRIPTION: Command to install the `mcp-remote` package globally using npm. This is an optional step if `npx` encounters issues.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_16

LANGUAGE: bash
CODE:
```
npm install -g mcp-remote
```

----------------------------------------

TITLE: Graphiti Azure OpenAI Setup
DESCRIPTION: Configure Graphiti to utilize Azure OpenAI services for LLM inference, embeddings, and cross-encoding. This setup requires distinct endpoints for LLM and embedding services and uses the `AsyncAzureOpenAI` client.

SOURCE: https://github.com/getzep/graphiti/blob/main/README.md#_snippet_12

LANGUAGE: python
CODE:
```
from openai import AsyncAzureOpenAI
from graphiti_core import Graphiti
from graphiti_core.llm_client import LLMConfig, OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# Azure OpenAI configuration - use separate endpoints for different services
api_key = "<your-api-key>"
api_version = "<your-api-version>"
llm_endpoint = "<your-llm-endpoint>"  # e.g., "https://your-llm-resource.openai.azure.com/"
embedding_endpoint = "<your-embedding-endpoint>"  # e.g., "https://your-embedding-resource.openai.azure.com/"

# Create separate Azure OpenAI clients for different services
llm_client_azure = AsyncAzureOpenAI(
    api_key=api_key,
    api_version=api_version,
    azure_endpoint=llm_endpoint
)

embedding_client_azure = AsyncAzureOpenAI(
    api_key=api_key,
    api_version=api_version,
    azure_endpoint=embedding_endpoint
)

# Create LLM Config with your Azure deployment names
azure_llm_config = LLMConfig(
    small_model="gpt-4.1-nano",
    model="gpt-4.1-mini",
)

# Initialize Graphiti with Azure OpenAI clients
graphiti = Graphiti(
    "bolt://localhost:7687",
    "neo4j",
    "password",
    llm_client=OpenAIClient(
        llm_config=azure_llm_config,
        client=llm_client_azure
    ),
    embedder=OpenAIEmbedder(
        config=OpenAIEmbedderConfig(
            embedding_model="text-embedding-3-small-deployment"  # Your Azure embedding deployment name
        ),
        client=embedding_client_azure
    ),
    cross_encoder=OpenAIRerankerClient(
        llm_config=LLMConfig(
            model=azure_llm_config.small_model  # Use small model for reranking
        ),
        client=llm_client_azure
    )
)

# Now you can use Graphiti with Azure OpenAI

```

----------------------------------------

TITLE: Initialize Graphiti Client
DESCRIPTION: Initializes the Graphiti client with the configured Neo4j connection details and an Anthropic LLM client. This sets up the core object for interacting with the graph and AI capabilities.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_7

LANGUAGE: python
CODE:
```
llm_client = AnthropicClient(cache=False)

client = Graphiti(
    neo4j_uri,
    neo4j_user,
    neo4j_password,
    llm_client=llm_client,
)
```

----------------------------------------

TITLE: Run Only Unit Tests
DESCRIPTION: Filters and runs only unit tests by using pytest's `-k` flag with a negation pattern. This command excludes integration tests from execution.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_13

LANGUAGE: bash
CODE:
```
pytest tests/ -k "not _int"
```

----------------------------------------

TITLE: Configure Graphiti Client
DESCRIPTION: Initializes the Graphiti client with Neo4j connection details retrieved from environment variables. It imports necessary Graphiti components like Graphiti, EntityEdge, EpisodeType, and clear_data.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_4

LANGUAGE: python
CODE:
```
# Configure Graphiti

from graphiti_core import Graphiti
from graphiti_core.edges import EntityEdge
from graphiti_core.nodes import EpisodeType
from graphiti_core.utils.maintenance.graph_data_operations import clear_data

neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'password')

client = Graphiti(
    neo4j_uri,
    neo4j_user,
    neo4j_password,
)
```

----------------------------------------

TITLE: Search for Out-of-Stock Products
DESCRIPTION: Performs a search query to find products that are out of stock. The result is then pretty-printed to display the relevant product information.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_10

LANGUAGE: python
CODE:
```
r = await client.search('Which products are out of stock?')

pretty_print(r[0])
```

----------------------------------------

TITLE: LangGraph Components API Reference
DESCRIPTION: This section details the essential components used for building stateful, multi-agent workflows with LangGraph. It covers state definition, graph construction, message accumulation, and pre-built nodes for tool execution.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_18

LANGUAGE: APIDOC
CODE:
```
StateGraph(state_definition: Type[TypedDict]) -> StateGraph
  Initializes a graph builder with a defined state structure.
  Parameters:
    state_definition: A TypedDict defining the structure of the graph's state.
  Returns:
    An instance of StateGraph.

State:
  messages: Annotated[list, add_messages]
    A list of messages, where `add_messages` ensures new messages are appended.
  user_name: str
    The name of the user interacting with the system.
  user_node_uuid: str
    The UUID of the user's node in the Graphiti graph.

add_messages: Decorator/utility for appending messages to a list in the state.

ToolNode(tools: list[BaseTool]) -> ToolNode
  A pre-built LangGraph node that executes a list of tools.
  Parameters:
    tools: A list of LangChain BaseTool objects.
  Returns:
    An instance of ToolNode.

END: Special node indicating the termination of a graph branch.
START: Special node indicating the entry point of a graph branch.
```

----------------------------------------

TITLE: Setup Logging
DESCRIPTION: Configures a basic logger to output informational messages to the console. It sets the logging level to INFO and defines a standard log message format.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
def setup_logging():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    return logger


logger = setup_logging()
```

----------------------------------------

TITLE: Run MCP Server with Docker Compose
DESCRIPTION: Starts the MCP server using Docker Compose. This command orchestrates the containerized deployment of the MCP server and its dependencies.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_9

LANGUAGE: bash
CODE:
```
cd mcp_server/
docker-compose up
```

----------------------------------------

TITLE: Perform Client Search and Print Results
DESCRIPTION: Demonstrates how to use the client.search method to query for information, specifying the center node and number of results. The results are then printed using a pretty_print function.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_15

LANGUAGE: python
CODE:
```
r = await client.search(
    'Can John wear ManyBirds Wool Runners?', center_node_uuid=john_uuid, num_results=3
)

print('-' * 100)
print("Node Distance Reranking from 'John' node")
print('-' * 100)
for record in r:
    print(record.fact)
```

LANGUAGE: python
CODE:
```
r = await client.search('What shoes has John purchased?', center_node_uuid=john_uuid, num_results=3)

pretty_print(r)
```

LANGUAGE: python
CODE:
```
r = await client.search('What shoes has John purchased?', center_node_uuid=john_uuid, num_results=5)

pretty_print(r)
```

LANGUAGE: python
CODE:
```
r = await client.search('Who is John?', num_results=5)

pretty_print(r)
```

LANGUAGE: python
CODE:
```
r = await client.search(
    'What did John do about his discomfort with the Mens Couriers shoes', num_results=5
)

pretty_print(r)
```

----------------------------------------

TITLE: Configure Claude Desktop for Graphiti MCP Server
DESCRIPTION: JSON configuration for Claude Desktop to connect to the Graphiti MCP server via `mcp-remote` gateway. It specifies the command and arguments to run the remote server.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_15

LANGUAGE: json
CODE:
```
{
  "mcpServers": {
    "graphiti-memory": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8000/sse"
      ]
    }
  }
}
```

----------------------------------------

TITLE: Setup Logging Configuration
DESCRIPTION: Configures the root logger to display INFO level messages to standard output, with a specific formatter.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_2

LANGUAGE: python
CODE:
```
def setup_logging():
    logger = logging.getLogger()
    logger.setLevel(logging.ERROR)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    return logger


logger = setup_logging()
```

----------------------------------------

TITLE: Add Messages to Client
DESCRIPTION: Shows how to add a sequence of messages to the client, likely for conversational context or history. It uses the add_messages function with a specific conversation identifier.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_16

LANGUAGE: python
CODE:
```
await add_messages(client, shoe_conversation_2, prefix='conversation-2')
```

----------------------------------------

TITLE: Run Only Integration Tests
DESCRIPTION: Filters and runs only integration tests by using pytest's `-k` flag with a pattern. This command isolates integration test execution.

SOURCE: https://github.com/getzep/graphiti/blob/main/CLAUDE.md#_snippet_12

LANGUAGE: bash
CODE:
```
pytest tests/ -k "_int"
```

----------------------------------------

TITLE: Ingest Product Data
DESCRIPTION: Asynchronously ingests product data from a JSON file into the Graphiti client. It reads product information, formats it into `RawEpisode` objects, and adds them in bulk to the graph.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_5

LANGUAGE: python
CODE:
```
async def ingest_products_data(client: Graphiti):
    script_dir = Path.cwd().parent
    json_file_path = script_dir / 'data' / 'manybirds_products.json'

    with open(json_file_path) as file:
        products = json.load(file)['products']

    episodes: list[RawEpisode] = [
        RawEpisode(
            name=product.get('title', f'Product {i}'),
            content=str({k: v for k, v in product.items() if k != 'images'}), # Exclude images for simplicity
            source_description='ManyBirds products',
            source=EpisodeType.json,
            reference_time=datetime.now(timezone.utc),
        )
        for i, product in enumerate(products)
    ]

    await client.add_episode_bulk(episodes)
```

----------------------------------------

TITLE: Create User Node and Retrieve UUID
DESCRIPTION: Adds a user to the Graphiti graph and retrieves their node UUID. This is typically done once a user is identified. It uses `client._search` with `NODE_HYBRID_SEARCH_EPISODE_MENTIONS` to find the user's node.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_7

LANGUAGE: python
CODE:
```
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS

user_name = 'jess'

await client.add_episode(
    name='User Creation',
    episode_body=(f'{user_name} is interested in buying a pair of shoes'),
    source=EpisodeType.text,
    reference_time=datetime.now(timezone.utc),
    source_description='SalesBot',
)

# let's get Jess's node uuid
nl = await client._search(user_name, NODE_HYBRID_SEARCH_EPISODE_MENTIONS)

user_node_uuid = nl.nodes[0].uuid
```

----------------------------------------

TITLE: Hybrid Search for John's Profile
DESCRIPTION: Performs a hybrid search using a specific recipe (`NODE_HYBRID_SEARCH_RRF`) to find John's profile. The result is pretty-printed, and John's UUID is extracted for potential further use.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_13

LANGUAGE: python
CODE:
```
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF

nl = await client._search('John', NODE_HYBRID_SEARCH_RRF)

pretty_print(nl[0])

john_uuid = nl[0].uuid
```

----------------------------------------

TITLE: Start MCP Server with Docker Compose
DESCRIPTION: Starts the Graphiti MCP server using Docker Compose. This method is recommended for clients that require an SSE (Server-Sent Events) transport endpoint.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
docker compose up
```

----------------------------------------

TITLE: Configure Neo4j Connection
DESCRIPTION: Retrieves Neo4j connection details (URI, username, password) from environment variables, providing default values for local development. These are used to establish a connection to the Neo4j graph database.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_1

LANGUAGE: python
CODE:
```
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'password')
```

----------------------------------------

TITLE: Add Shoe Conversation 1
DESCRIPTION: Adds the first sample shoe conversation to the Graphiti client using the `add_messages` function. This populates the graph with customer-product interaction data.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/ecommerce/runner.ipynb#_snippet_11

LANGUAGE: python
CODE:
```
await add_messages(client, shoe_conversation_1, prefix='conversation-1')
```

----------------------------------------

TITLE: Specify Database in Neo4jDriver
DESCRIPTION: Demonstrates how to modify the Neo4j driver constructor to connect to a specific database name, addressing potential 'Graph not found' errors.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/quickstart/README.md#_snippet_3

LANGUAGE: python
CODE:
```
# In quickstart_neo4j.py, change:
driver = Neo4jDriver(uri=neo4j_uri, user=neo4j_user, password=neo4j_password)

# To specify a different database:
driver = Neo4jDriver(uri=neo4j_uri, user=neo4j_user, password=neo4j_password, database="your_db_name")
```

----------------------------------------

TITLE: Install Graphiti Core
DESCRIPTION: Installs the Graphiti core library using pip. This is a prerequisite for running the example and requires Python 3.9+.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/quickstart/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
pip install graphiti-core
```

----------------------------------------

TITLE: Run LangGraph Agent Interactively
DESCRIPTION: Implements an interactive chat interface using Jupyter widgets to send user input to the LangGraph agent and display the conversation, including asynchronous streaming of responses.

SOURCE: https://github.com/getzep/graphiti/blob/main/examples/langgraph-agent/agent.ipynb#_snippet_24

LANGUAGE: Python
CODE:
```
conversation_output = widgets.Output()
config = {'configurable': {'thread_id': uuid.uuid4().hex}}
user_state = {'user_name': user_name, 'user_node_uuid': user_node_uuid}


async def process_input(user_state: State, user_input: str):
    conversation_output.append_stdout(f'\nUser: {user_input}\n')
    conversation_output.append_stdout('\nAssistant: ')

    graph_state = {
        'messages': [{'role': 'user', 'content': user_input}],
        'user_name': user_state['user_name'],
        'user_node_uuid': user_state['user_node_uuid'],
    }

    try:
        async for event in graph.astream(
            graph_state,
            config=config,
        ):
            for value in event.values():
                if 'messages' in value:
                    last_message = value['messages'][-1]
                    if isinstance(last_message, AIMessage) and isinstance(
                        last_message.content, str
                    ):
                        conversation_output.append_stdout(last_message.content)
    except Exception as e:
        conversation_output.append_stdout(f'Error: {e}')


def on_submit(b):
    user_input = input_box.value
    input_box.value = ''
    asyncio.create_task(process_input(user_state, user_input))


input_box = widgets.Text(placeholder='Type your message here...')
submit_button = widgets.Button(description='Send')
submit_button.on_click(on_submit)

conversation_output.append_stdout('Asssistant: Hello, how can I help you find shoes today?')

display(widgets.VBox([input_box, submit_button, conversation_output]))
```

----------------------------------------

TITLE: Configure MCP Client for SSE Transport
DESCRIPTION: This JSON configuration details how to connect an MCP client to the Graphiti MCP server using the 'sse' (Server-Sent Events) transport, specifying the server URL.

SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_snippet_9

LANGUAGE: json
CODE:
```
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "sse",
      "url": "http://localhost:8000/sse"
    }
  }
}
```

========================
QUESTIONS AND ANSWERS
========================
TOPIC: Graphiti MCP Server Documentation
Q: What are the key functions exposed by the Graphiti MCP server?
A: The Graphiti MCP server exposes functions for Episode Management (adding, retrieving, deleting episodes), Entity Management (searching and managing nodes and relationships), Search Capabilities (finding facts and node summaries), Group Management (organizing data by group_id), and Graph Maintenance (clearing the graph and rebuilding indices).


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_qa_2

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: How should updates to existing knowledge be handled in Graphiti's MCP Tools?
A: When updating existing knowledge, be explicit that it is an update and only add what has changed or is new to the graph.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_5

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: How should preferences and procedures be categorized when saving them in Graphiti's MCP Tools?
A: Preferences and procedures should be labeled with clear categories to ensure better retrieval of this information later.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_8

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: How should Graphiti's MCP Tools handle specificity when retrieving information?
A: More specific information should always take precedence over general information when retrieving data from the knowledge graph.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_14

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: What is a key best practice for making recommendations with Graphiti's MCP Tools?
A: A key best practice is to always search for established knowledge before making any suggestions or recommendations.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_11

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: When should new or updated information be saved using Graphiti's MCP Tools?
A: New or updated information, especially user requirements and preferences, should be captured and stored immediately using the `add_memory` tool. Procedures and factual relationships should also be documented as they are discovered.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_3

----------------------------------------

TOPIC: Graphiti MCP Server Documentation
Q: How can data be organized and filtered within Graphiti?
A: Data can be organized and managed using Group Management features, allowing filtering of related data by a `group_id`.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_qa_12

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: What is the best practice for storing long requirements in Graphiti's MCP Tools?
A: For very long requirements, the best practice is to split them into shorter, logical chunks before storing them using the `add_memory` tool.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_4

----------------------------------------

TOPIC: Graphiti MCP Server Documentation
Q: What types of data can be managed as episodes in Graphiti?
A: Episodes in Graphiti can consist of text, messages, or JSON data.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/README.md#_qa_13

----------------------------------------

TOPIC: Graphiti MCP Tools for Agent Memory
Q: What is the role of 'facts' in Graphiti's MCP Tools?
A: Facts represent factual relationships between entities. They should be recorded when learned to inform decisions and recommendations.


SOURCE: https://github.com/getzep/graphiti/blob/main/mcp_server/cursor_rules.md#_qa_7