#!/usr/bin/env python3
"""
Test Neo4j Connection and Setup
"""
from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

load_dotenv()

def test_neo4j_connection():
    """Test Neo4j connection and setup"""
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD", "neo4j")
    
    print(f"Testing Neo4j connection to {uri}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(password)}")
    
    try:
        # Try to connect with default password
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j' as message")
            record = result.single()
            print(f"✅ Connection successful: {record['message']}")
            
            # Test creating a simple node
            session.run("CREATE (n:Test {name: 'connection_test', timestamp: datetime()}) RETURN n")
            print("✅ Node creation test successful")
            
            # Clean up test node
            session.run("MATCH (n:Test {name: 'connection_test'}) DELETE n")
            print("✅ Node cleanup successful")
            
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        
        # Try with no authentication (fresh Neo4j instance)
        try:
            print("Trying without authentication...")
            driver = GraphDatabase.driver(uri, auth=None)
            with driver.session() as session:
                result = session.run("RETURN 'Hello Neo4j' as message")
                record = result.single()
                print(f"✅ No-auth connection successful: {record['message']}")
            driver.close()
            return True
        except Exception as e2:
            print(f"❌ No-auth connection also failed: {e2}")
            
            # Try with empty password (sometimes needed for fresh instances)
            try:
                print("Trying with empty password...")
                driver = GraphDatabase.driver(uri, auth=(user, ""))
                with driver.session() as session:
                    result = session.run("RETURN 'Hello Neo4j' as message")
                    record = result.single()
                    print(f"✅ Empty password connection successful: {record['message']}")
                driver.close()
                return True
            except Exception as e3:
                print(f"❌ Empty password connection failed: {e3}")
                print("\n💡 Solutions:")
                print("1. Access http://localhost:7474 in your browser to set up Neo4j")
                print("2. Or restart Neo4j container with: NEO4J_AUTH=none")
                print("3. Or set NEO4J_AUTH=neo4j/yourpassword environment variable")
                return False

if __name__ == "__main__":
    test_neo4j_connection()