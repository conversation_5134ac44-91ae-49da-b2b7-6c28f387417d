# Advanced RAG System 🧠

高级RAG系统 - 基于MCP架构的智能检索增强生成系统

## 系统架构

```
用户输入 → Graphiti上下文检索 → 超级智能处理 → 有向图执行 → 智能循环控制 → Graphiti记忆更新
```

## 核心组件

### 🔥 核心特性

- **🧠 超级智能处理**: 单一LLM实现完整的智能分析流水线
- **📊 有向图执行**: DAG-based任务编排与并行执行
- **🔄 智能循环控制**: 预测性终止与自适应优化
- **💾 Graphiti记忆**: 时间感知的知识图谱记忆系统
- **🔌 MCP集成**: 标准化的工具发现与执行协议
- **🎯 向量检索**: 多provider嵌入模型与Milvus集成

### 📁 项目结构

```
graphiti_rag/
├── core/                           # 核心模块
│   ├── llm_client.py              # LLM流式客户端 (OpenAI/Anthropic/Groq)
│   ├── embedding_client.py        # 嵌入模型客户端
│   ├── mcp_client.py              # 增强MCP客户端
│   ├── memory_manager.py          # Graphiti记忆管理
│   ├── super_intelligence.py      # 超级智能处理器
│   ├── graph_execution_engine.py  # 有向图执行引擎
│   └── intelligent_loop_controller.py # 智能循环控制器
├── servers/                       # MCP服务器
│   └── milvus_server.py          # Milvus向量数据库服务器
├── advanced_rag_system.py        # 主系统集成
├── demo.py                       # 使用演示
├── requirements.txt              # 依赖列表
├── .env.example                 # 环境变量模板
└── CLAUDE.md                    # Claude Code配置
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository_url>
cd graphiti_rag

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，添加必要的API密钥
```

### 2. 环境变量配置

```bash
# OpenAI API
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic API (可选)
ANTHROPIC_API_KEY=your_anthropic_api_key

# Groq API (可选，作为备用)
GROQ_API_KEY=your_groq_api_key

# HuggingFace API (可选)
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Neo4j配置 (Graphiti记忆系统)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Milvus配置 (向量数据库)
MILVUS_URI=http://localhost:19530
MILVUS_USER=
MILVUS_PASSWORD=

# 系统配置
MAX_CONCURRENT_TASKS=3
DEFAULT_TEMPERATURE=0.7
LOG_LEVEL=INFO
```

### 3. 运行演示

```bash
# 基础演示
python demo.py

# 或者直接使用系统
python -c "
import asyncio
from advanced_rag_system import create_rag_system

async def test():
    rag = create_rag_system()
    await rag.initialize()
    
    result = await rag.process_user_input(
        '帮我分析当前的AI发展趋势',
        'test_user'
    )
    
    print(f'质量分数: {result[\"quality_score\"]:.3f}')
    print(f'响应: {result[\"response\"]}')
    
    await rag.cleanup()

asyncio.run(test())
"
```

## 💡 核心概念

### 智能循环处理流程

1. **用户输入预处理** - 清理和标准化输入
2. **Graphiti上下文检索** - 从知识图谱检索相关记忆
3. **超级智能分析** - 完整的任务规划和风险评估
4. **有向图执行** - 并行化任务执行和依赖管理
5. **质量评估** - 多维度的结果质量分析
6. **循环决策** - 智能判断是否需要优化迭代
7. **记忆更新** - 存储执行模式和用户偏好

### 执行策略

- **SEQUENTIAL**: 顺序执行，适合依赖性强的任务
- **PARALLEL**: 并行执行，最大化资源利用
- **HYBRID**: 混合策略，智能选择执行方式 (推荐)
- **PRIORITY_BASED**: 基于优先级的执行
- **CRITICAL_PATH**: 关键路径优化

### 循环策略

- **ADAPTIVE**: 自适应策略，根据执行情况动态调整
- **AGGRESSIVE**: 激进优化，追求最佳质量
- **CONSERVATIVE**: 保守策略，快速获得可接受结果
- **QUALITY_FOCUSED**: 质量优先，不断优化直到达标
- **SPEED_FOCUSED**: 速度优先，快速响应

## 🔧 API使用指南

### 基础用法

```python
import asyncio
from advanced_rag_system import AdvancedRAGSystem, RAGSystemConfig
from core.intelligent_loop_controller import LoopStrategy

async def basic_usage():
    # 配置系统
    config = RAGSystemConfig(
        llm_provider="openai",
        llm_model="gpt-4-turbo-preview",
        max_iterations=3,
        quality_threshold=0.8
    )
    
    # 创建系统实例
    rag_system = AdvancedRAGSystem(config)
    
    try:
        # 初始化
        await rag_system.initialize()
        
        # 处理用户输入
        result = await rag_system.process_user_input(
            user_input="解释机器学习的基本概念",
            user_id="user123",
            strategy=LoopStrategy.ADAPTIVE
        )
        
        # 获取结果
        print(f"成功: {result['success']}")
        print(f"响应: {result['response']}")
        print(f"质量分数: {result['quality_score']:.3f}")
        
    finally:
        await rag_system.cleanup()

# 运行
asyncio.run(basic_usage())
```

### 高级配置

```python
# 自定义配置
config = RAGSystemConfig(
    # LLM配置
    llm_provider="openai",
    llm_model="gpt-4-turbo-preview",
    llm_temperature=0.7,
    
    # 嵌入模型配置
    embedding_provider="openai",
    embedding_model="text-embedding-3-large",
    
    # Graphiti记忆配置
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    
    # 执行配置
    max_concurrent=5,
    max_iterations=10,
    quality_threshold=0.9,
    
    # RAG配置
    top_k_retrieval=10,
    similarity_threshold=0.75,
    chunk_size=1500,
    chunk_overlap=300
)

rag_system = AdvancedRAGSystem(config)
```

### 添加MCP服务器

```python
# 添加Milvus服务器
milvus_config = {
    "transport": "stdio",
    "command": "python",
    "args": ["servers/milvus_server.py"],
    "env": {"MILVUS_URI": "http://localhost:19530"}
}

await rag_system.add_mcp_server("milvus", milvus_config)

# 添加自定义服务器
custom_config = {
    "transport": "http",
    "url": "http://localhost:8080/mcp",
    "headers": {"Authorization": "Bearer token"}
}

await rag_system.add_mcp_server("custom_server", custom_config)
```

## 🛠 扩展开发

### 添加新的LLM Provider

```python
from core.llm_client import BaseLLMClient

class CustomLLMClient(BaseLLMClient):
    async def generate(self, messages, **kwargs):
        # 实现您的LLM调用逻辑
        pass
    
    async def generate_stream(self, messages, **kwargs):
        # 实现流式生成
        pass

# 注册到工厂
LLMClientFactory.register("custom_provider", CustomLLMClient)
```

### 添加新的嵌入Provider

```python
from core.embedding_client import BaseEmbeddingClient

class CustomEmbeddingClient(BaseEmbeddingClient):
    async def encode(self, texts, **kwargs):
        # 实现嵌入生成逻辑
        pass

# 注册到工厂
EmbeddingClientFactory.register("custom_embedding", CustomEmbeddingClient)
```

### 创建MCP服务器

```python
from servers.base_server import BaseMCPServer

class MyMCPServer(BaseMCPServer):
    def __init__(self):
        super().__init__("my_server", "1.0.0")
    
    async def handle_tool_call(self, name: str, arguments: dict):
        # 实现工具处理逻辑
        if name == "my_tool":
            return {"result": "success"}
        
        raise ValueError(f"Unknown tool: {name}")

# 运行服务器
if __name__ == "__main__":
    server = MyMCPServer()
    asyncio.run(server.run())
```

## 🔍 监控和调试

### 日志配置

```python
from loguru import logger

# 配置详细日志
logger.add(
    "logs/rag_system_{time}.log",
    rotation="100 MB",
    retention="7 days",
    level="DEBUG",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)
```

### 性能监控

```python
# 获取执行统计
result = await rag_system.process_user_input(...)

print(f"处理时间: {result['processing_time']:.2f}秒")
print(f"循环次数: {result['loop_iterations']}")
print(f"质量分数: {result['quality_score']:.3f}")
print(f"收敛率: {result['execution_summary']['convergence_rate']:.3f}")

# 获取会话统计
stats = await rag_system.get_session_statistics(session_id)
print(f"会话交互次数: {stats['interaction_count']}")
```

## 📊 系统指标

### 质量评估指标

- **质量分数** (0.0-1.0): 综合执行质量评估
- **成功率**: 任务执行成功的百分比
- **收敛率**: 循环优化的收敛效果
- **改进率**: 迭代间的质量改进程度

### 性能指标

- **处理时间**: 端到端的响应时间
- **并发度**: 同时执行的任务数量
- **资源利用率**: 计算资源的使用效率
- **缓存命中率**: 记忆系统的缓存效果

## 🚨 故障排除

### 常见问题

1. **API密钥错误**
   ```
   解决：检查 .env 文件中的API密钥配置
   ```

2. **Neo4j连接失败**
   ```
   解决：确保Neo4j服务运行，或使用内存存储模式
   ```

3. **Milvus连接错误**
   ```
   解决：检查Milvus服务状态，或禁用向量搜索功能
   ```

4. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   # 或安装可选依赖
   pip install graphiti-core pymilvus neo4j
   ```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
config.log_level = "DEBUG"
```

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [Model Context Protocol (MCP)](https://github.com/anthropic/mcp) - 标准化工具协议
- [Graphiti](https://github.com/getzep/graphiti) - 时间感知知识图谱
- [Milvus](https://milvus.io/) - 向量数据库
- [OpenAI](https://openai.com/) - GPT模型API
- [Anthropic](https://anthropic.com/) - Claude模型支持

---

**Built with ❤️ for the AI Community**