#!/usr/bin/env python3
"""
Simple RAG System Test
简化的RAG系统测试 - 不依赖Neo4j和复杂组件
"""

import asyncio
import os
from pathlib import Path

# 确保项目根目录在Python路径中
import sys
sys.path.insert(0, str(Path(__file__).parent))

from dotenv import load_dotenv
from loguru import logger

from core.llm_client import LLMManager, LLMClientFactory
from core.embedding_client import EmbeddingManager, EmbeddingClientFactory


class SimpleRAGSystem:
    """简化的RAG系统，不依赖Graphiti"""
    
    def __init__(self):
        self.llm_manager = None
        self.embedding_manager = None
        self.initialized = False
    
    async def initialize(self):
        """初始化系统"""
        if self.initialized:
            return
        
        logger.info("Initializing Simple RAG System...")
        
        # 初始化LLM管理器
        self.llm_manager = LLMManager()
        
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        
        if api_key:
            llm_client = LLMClientFactory.create_client(
                provider="openai",
                api_key=api_key,
                model="glm-4-flash",
                base_url=base_url
            )
            await self.llm_manager.add_client("primary", llm_client)
        
        # 初始化嵌入管理器
        self.embedding_manager = EmbeddingManager()
        
        # 添加中文嵌入客户端
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-base-zh",  # 使用base版本更快
            provider="sentence_transformers"
        )
        await self.embedding_manager.add_client("chinese", chinese_client)
        await self.embedding_manager.set_primary("chinese")
        
        self.initialized = True
        logger.info("Simple RAG System initialized successfully")
    
    async def process_query(self, query: str, context_docs: list = None):
        """处理查询"""
        if not self.initialized:
            await self.initialize()
        
        result = {
            "query": query,
            "query_embedding": None,
            "response": None,
            "context_similarity": []
        }
        
        try:
            # 1. 编码查询
            query_embedding = await self.embedding_manager.encode_single(query)
            result["query_embedding"] = f"编码完成，维度: {len(query_embedding)}"
            
            # 2. 如果有上下文文档，计算相似度
            if context_docs:
                similarities = []
                for doc in context_docs:
                    similarity = await self.embedding_manager.compare_similarity(
                        query, doc, "chinese"
                    )
                    similarities.append({
                        "document": doc[:100] + "..." if len(doc) > 100 else doc,
                        "similarity": f"{similarity:.3f}"
                    })
                
                # 按相似度排序
                similarities.sort(key=lambda x: float(x["similarity"]), reverse=True)
                result["context_similarity"] = similarities[:3]  # 取前3个
            
            # 3. 构建增强的提示
            enhanced_prompt = self._build_enhanced_prompt(query, context_docs, result["context_similarity"])
            
            # 4. 生成回答
            from core.llm_client import LLMMessage
            messages = [LLMMessage(role="user", content=enhanced_prompt)]
            response = await self.llm_manager.generate(messages)
            result["response"] = response.content
            
            return result
            
        except Exception as e:
            logger.error(f"Query processing error: {e}")
            result["error"] = str(e)
            return result
    
    def _build_enhanced_prompt(self, query: str, context_docs: list, similarities: list):
        """构建增强提示"""
        if not context_docs or not similarities:
            return f"请回答以下问题：{query}"
        
        # 使用最相似的文档作为上下文
        relevant_context = []
        for sim in similarities:
            if float(sim["similarity"]) > 0.3:  # 相似度阈值
                relevant_context.append(sim["document"])
        
        if relevant_context:
            context_text = "\n".join(relevant_context)
            prompt = f"""
基于以下上下文信息回答问题：

上下文：
{context_text}

问题：{query}

请基于上下文信息给出准确、详细的回答。如果上下文信息不足以回答问题，请说明并提供你的一般性知识。
"""
        else:
            prompt = f"请回答以下问题：{query}"
        
        return prompt
    
    async def cleanup(self):
        """清理资源"""
        if self.llm_manager:
            await self.llm_manager.cleanup()
        if self.embedding_manager:
            await self.embedding_manager.cleanup()
        logger.info("Simple RAG System cleaned up")


async def test_simple_rag():
    """测试简化RAG系统"""
    print("🎯 简化RAG系统测试")
    print("=" * 50)
    
    rag_system = SimpleRAGSystem()
    
    try:
        # 初始化系统
        await rag_system.initialize()
        print("✅ RAG系统初始化完成")
        
        # 测试文档
        context_docs = [
            "人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
            "机器学习是人工智能的核心，是使计算机具有智能的根本途径，其应用遍及人工智能的各个领域。",
            "深度学习是机器学习的一个子集，它试图模仿人脑的工作方式，通过构建人工神经网络来学习数据的内在规律和表示层次。",
            "自然语言处理是人工智能领域的一个重要方向，它研究能实现人与计算机之间用自然语言进行有效通信的各种理论和方法。",
            "今天天气很好，适合出去散步。阳光明媚，温度适宜。"
        ]
        
        # 测试查询
        queries = [
            "什么是人工智能？",
            "机器学习和深度学习的关系是什么？",
            "今天天气怎么样？"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n📝 查询 {i}: {query}")
            print("-" * 40)
            
            result = await rag_system.process_query(query, context_docs)
            
            if "error" in result:
                print(f"❌ 处理失败: {result['error']}")
                continue
            
            print(f"🔍 查询编码: {result['query_embedding']}")
            
            if result["context_similarity"]:
                print("📊 最相关的文档:")
                for j, sim in enumerate(result["context_similarity"], 1):
                    print(f"  {j}. [相似度: {sim['similarity']}] {sim['document']}")
            
            print(f"\n🤖 系统回答:")
            print(result["response"])
            print()
        
        print("🎉 RAG系统测试完成！")
        
    except Exception as e:
        print(f"❌ RAG系统测试失败: {e}")
        logger.error(f"RAG test error: {e}")
        
    finally:
        await rag_system.cleanup()


async def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()
    
    # 配置日志
    logger.add(
        "logs/simple_rag_test_{time}.log",
        rotation="10 MB",
        retention="3 days",
        level="INFO"
    )
    
    try:
        await test_simple_rag()
    except KeyboardInterrupt:
        print("\n⛔ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    # 确保logs目录存在
    import os
    os.makedirs("logs", exist_ok=True)
    
    asyncio.run(main())