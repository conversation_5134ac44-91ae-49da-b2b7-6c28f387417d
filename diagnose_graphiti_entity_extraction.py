#!/usr/bin/env python3
"""
诊断 Graphiti 实体提取过程
找出为什么实体和关系没有被创建
"""
import asyncio
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

async def diagnose_entity_extraction():
    """诊断实体提取过程"""
    print("🔍 诊断 Graphiti 实体提取过程")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 步骤1: 初始化 Graphiti 和组件
        print("📋 步骤1: 初始化 Graphiti...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.graphiti_llm import GraphitiLLM
        from core.graphiti_embedder import GraphitiEmbedder
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.3,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器
        embedding_manager = EmbeddingManager()
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general"
        )
        await embedding_manager.add_client("chinese", chinese_client)
        
        # Graphiti 包装器
        graphiti_llm = GraphitiLLM(llm_manager)
        primary_embedder = embedding_manager.get_primary_client()
        graphiti_embedder = GraphitiEmbedder(primary_embedder)
        
        print("✅ 组件初始化完成")
        
        # 步骤2: 初始化 Graphiti
        print("\n📋 步骤2: 创建 Graphiti 客户端...")
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j", 
            password="password",
            llm_client=graphiti_llm,
            embedder=graphiti_embedder,
            store_raw_episode_content=True
        )
        
        await client.build_indices_and_constraints()
        print("✅ Graphiti 客户端初始化成功")
        
        # 步骤3: 测试单个简单 Episode，监控整个过程
        print("\n📋 步骤3: 添加简单 Episode 并监控处理过程...")
        
        simple_episode = "苹果公司的CEO蒂姆·库克宣布了新的财务策略"
        print(f"  测试 Episode: {simple_episode}")
        
        try:
            # 添加 Episode，这应该会触发实体提取
            await client.add_episode(
                name="测试简单实体提取",
                episode_body=simple_episode,
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="实体提取测试"
            )
            print("  ✅ Episode 添加成功")
        except Exception as e:
            print(f"  ❌ Episode 添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 步骤4: 立即检查数据库状态
        print("\n📋 步骤4: 检查数据库状态 (Episode 添加后)...")
        
        from neo4j import AsyncGraphDatabase
        driver = AsyncGraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'password'))
        
        async with driver.session() as session:
            # 检查 Episodic 节点
            result = await session.run('MATCH (n:Episodic) RETURN count(n) as episodic_count')
            record = await result.single()
            print(f"  Episodic 节点数量: {record['episodic_count']}")
            
            # 检查是否有 Entity 节点
            result = await session.run('MATCH (n:Entity) RETURN count(n) as entity_count')
            record = await result.single()
            entity_count = record['entity_count'] if record else 0
            print(f"  Entity 节点数量: {entity_count}")
            
            # 检查是否有关系
            result = await session.run('MATCH ()-[r]->() RETURN count(r) as rel_count')
            record = await result.single()
            rel_count = record['rel_count'] if record else 0
            print(f"  关系数量: {rel_count}")
        
        await driver.close()
        
        # 步骤5: 等待并再次检查
        print("\n📋 步骤5: 等待处理并再次检查...")
        print("  ⏳ 等待 10 秒...")
        await asyncio.sleep(10)
        
        driver = AsyncGraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'password'))
        async with driver.session() as session:
            # 再次检查
            result = await session.run('MATCH (n:Entity) RETURN count(n) as entity_count')
            record = await result.single()
            entity_count = record['entity_count'] if record else 0
            print(f"  等待后 Entity 节点数量: {entity_count}")
            
            if entity_count > 0:
                # 查看具体的实体
                result = await session.run('MATCH (n:Entity) RETURN n.name as name, keys(n) as properties LIMIT 3')
                records = await result.data()
                print("  发现的实体:")
                for record in records:
                    print(f"    实体: {record['name']}, 属性: {record['properties']}")
            else:
                print("  ❌ 等待后仍然没有 Entity 节点被创建")
        
        await driver.close()
        
        # 步骤6: 测试 LLM 直接调用实体提取
        print("\n📋 步骤6: 测试 LLM 直接实体提取...")
        
        try:
            from core.llm_client import LLMMessage
            
            entity_extraction_prompt = f"""
请从以下文本中提取实体和它们之间的关系：

文本: {simple_episode}

请以JSON格式返回实体和关系:
{{
    "entities": [
        {{"name": "实体名", "type": "实体类型"}},
        ...
    ],
    "relationships": [
        {{"source": "实体1", "target": "实体2", "relation": "关系类型"}},
        ...
    ]
}}
"""
            
            messages = [LLMMessage(role="user", content=entity_extraction_prompt)]
            response = await llm_manager.generate(messages=messages, temperature=0.1)
            
            print(f"  LLM 实体提取响应: {response.content[:200]}...")
            
            # 尝试解析 JSON
            import json
            try:
                extracted_data = json.loads(response.content)
                entities = extracted_data.get("entities", [])
                relationships = extracted_data.get("relationships", [])
                
                print(f"  ✅ 提取到 {len(entities)} 个实体, {len(relationships)} 个关系")
                for entity in entities[:3]:
                    print(f"    实体: {entity}")
                for rel in relationships[:3]:
                    print(f"    关系: {rel}")
                    
            except json.JSONDecodeError:
                print("  ❌ LLM 响应不是有效的 JSON 格式")
                
        except Exception as e:
            print(f"  ❌ 直接 LLM 实体提取失败: {e}")
        
        # 步骤7: 诊断总结
        print("\n📋 步骤7: 诊断总结...")
        
        if entity_count == 0:
            print("  🚨 关键问题: Graphiti 实体提取过程完全失败")
            print("  🔧 可能原因:")
            print("    1. LLM 包装器的响应格式不符合 Graphiti 预期")
            print("    2. 嵌入器在实体提取阶段调用失败")
            print("    3. Graphiti 内部的实体提取 pipeline 有配置问题")
            print("    4. Neo4j 权限或连接问题")
            
            print("  💡 建议修复:")
            print("    1. 检查 Graphiti 配置参数")
            print("    2. 调试 GraphitiLLM 包装器的 structured response")
            print("    3. 确认 Neo4j 的写入权限")
            print("    4. 查看 Graphiti 内部日志")
        else:
            print("  ✅ 实体提取过程正常工作")
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        
        return entity_count > 0
        
    except Exception as e:
        print(f"❌ 诊断过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 Graphiti 实体提取诊断")
    print("🎯 找出关系连接失败的根本原因")
    print("=" * 60)
    
    success = await diagnose_entity_extraction()
    
    print("\n" + "=" * 60)
    print("📊 诊断结果:")
    
    if success:
        print("🎉 实体提取功能正常!")
        print("✅ 关系连接问题可能在其他环节")
    else:
        print("❌ 实体提取过程存在问题")
        print("🔧 需要修复实体提取过程才能建立关系")

if __name__ == "__main__":
    asyncio.run(main())