#!/usr/bin/env python3
"""
金融财务专业RAG系统演示
Financial Analysis RAG System Demo
"""
import asyncio
from advanced_rag_system import create_financial_rag_system, LoopStrategy


async def financial_rag_demo():
    """金融财务RAG系统专业演示"""
    print("🏦 金融财务智能分析系统")
    print("📊 Financial Intelligence RAG System")
    print("=" * 80)
    print("💼 专业财务分析 | 实时推理展示 | 智能决策支持")
    print("=" * 80)
    
    # 创建金融专业RAG系统
    financial_rag = create_financial_rag_system()
    
    try:
        # 初始化系统
        print("\n🔧 正在初始化金融分析系统...")
        await financial_rag.initialize()
        print("✅ 金融RAG系统就绪\n")
        
        # 专业金融分析测试案例
        financial_cases = [
            {
                "query": "腾讯控股2023年第三季度财报显示，营收1546亿元人民币，同比增长10%，净利润432亿元，同比增长39%。请分析其盈利能力和成长性。",
                "analyst_id": "senior_equity_analyst",
                "case_type": "📈 港股科技股财报分析"
            },
            {
                "query": "某A股制造业上市公司：总资产100亿，净资产35亿，营收80亿，净利润6亿，经营现金流净额8亿，自由现金流5亿。评估其投资价值。",
                "analyst_id": "value_investor",
                "case_type": "🏭 制造业投资价值评估"
            },
            {
                "query": "新能源汽车行业PE估值普遍在30-50倍，某新势力车企PE达到80倍，但营收增长率200%，毛利率15%，现金流为负20亿。如何看待其估值？",
                "analyst_id": "growth_investment_manager",
                "case_type": "🚗 新能源汽车估值分析"
            },
            {
                "query": "某房地产公司资产负债率85%，流动比率0.8，速动比率0.3，净负债率150%，在当前地产调控政策下的风险如何？",
                "analyst_id": "credit_risk_analyst", 
                "case_type": "🏘️ 房地产信用风险评估"
            }
        ]
        
        for i, case in enumerate(financial_cases, 1):
            print(f"\n{'='*60}")
            print(f"📋 案例 {i}: {case['case_type']}")
            print(f"👤 分析师: {case['analyst_id']}")
            print(f"💭 查询: {case['query'][:60]}...")
            print("─" * 60)
            
            # 流式财务分析
            response_text = ""
            async for update in financial_rag.stream_process_user_input(
                user_input=case["query"],
                user_id=case["analyst_id"],
                strategy=LoopStrategy.ADAPTIVE
            ):
                update_type = update.get("type", "")
                message = update.get("message", "")
                
                if update_type == "status":
                    print(f"⏳ {message}")
                elif update_type == "result":
                    print(f"📊 {message}")
                elif update_type == "loop_update":
                    data = update.get("data", {})
                    if data.get("type") == "iteration_result":
                        print(f"🔄 {message}")
                elif update_type == "response_chunk":
                    chunk = update.get("data", {}).get("chunk", "")
                    response_text += chunk
                    print(chunk, end="", flush=True)
                elif update_type == "final":
                    print(f"\n\n✅ {message}")
                    result_data = update.get("data", {})
                    print(f"📊 分析耗时: {result_data.get('processing_time', 0):.1f}秒")
                    print(f"🎯 质量评分: {result_data.get('quality_score', 0):.2f}")
                    print(f"🔁 优化轮次: {result_data.get('loop_iterations', 0)}")
                
            # 在案例间暂停
            if i < len(financial_cases):
                await asyncio.sleep(2)
        
        # 系统性能总结
        print(f"\n{'='*80}")
        print("📈 金融RAG系统性能总结:")
        print("─" * 40)
        
        for session_id, stats in financial_rag.session_contexts.items():
            user_id = stats.get('user_id', 'unknown')
            print(f"👤 {user_id}:")
            print(f"   📊 分析次数: {stats.get('interaction_count', 0)}")
            print(f"   🎯 质量评分: {stats.get('last_quality_score', 0):.3f}")
            print(f"   ⏰ 最后分析: {stats.get('last_interaction', 'N/A')}")
        
        print(f"\n🎉 金融财务RAG系统演示完成!")
        print("💡 系统特色: 专业术语、风险评估、投资建议、实时分析")
        
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        
    finally:
        print("\n🔧 正在清理系统资源...")
        await financial_rag.cleanup()
        print("✅ 资源清理完成")


async def interactive_financial_analysis():
    """交互式金融分析"""
    print("💼 交互式金融分析系统")
    print("输入财务问题获得专业分析 | 输入 'quit' 退出")
    print("=" * 60)
    
    financial_rag = create_financial_rag_system()
    
    try:
        await financial_rag.initialize()
        print("✅ 金融分析系统就绪，请输入您的财务问题:\n")
        
        analyst_id = "interactive_financial_analyst"
        query_count = 0
        
        # 提供一些示例问题
        print("💡 示例问题:")
        examples = [
            "分析某公司ROE持续下降但ROA稳定的原因",
            "如何评估一家科技公司的现金流质量",
            "房地产行业债务风险的关键指标有哪些",
            "新能源汽车股票的估值方法和风险点"
        ]
        
        for i, example in enumerate(examples, 1):
            print(f"  {i}. {example}")
        print()
        
        while True:
            try:
                user_query = input("💰 请输入财务问题: ").strip()
                
                if user_query.lower() in ['quit', 'exit', '退出', 'q']:
                    break
                    
                if not user_query:
                    print("❌ 请输入有效的财务问题")
                    continue
                    
                query_count += 1
                print(f"\n🔍 开始分析第 {query_count} 个财务问题...")
                print("─" * 50)
                
                # 实时财务分析
                async for update in financial_rag.stream_process_user_input(
                    user_input=user_query,
                    user_id=analyst_id
                ):
                    update_type = update.get("type", "")
                    message = update.get("message", "")
                    
                    if update_type == "status":
                        print(f"⏳ {message}")
                    elif update_type == "result":
                        print(f"📊 {message}")
                    elif update_type == "response_chunk":
                        chunk = update.get("data", {}).get("chunk", "")
                        print(chunk, end="", flush=True)
                    elif update_type == "final":
                        print(f"\n\n✅ 分析完成!")
                        
                print("\n" + "─" * 50)
                
            except KeyboardInterrupt:
                print("\n👋 收到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"❌ 分析错误: {e}")
                
    finally:
        await financial_rag.cleanup()
        print("👋 感谢使用金融分析系统！")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        asyncio.run(interactive_financial_analysis())
    else:
        asyncio.run(financial_rag_demo())