# LLM Provider Configuration
OPENAI_API_KEY=f33deda4aeda4d2aab2a47b67ce589bd.PvyeajSvGL8Nlmk7
GROQ_API_KEY=your_groq_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Custom API Base URLs (支持自定义服务器)
# OpenAI 兼容服务器URL (如: vLLM, Text Generation WebUI, LocalAI等)
OPENAI_BASE_URL=https://open.bigmodel.cn/api/paas/v4/
# OPENAI_BASE_URL=http://localhost:8000/v1  # vLLM本地服务器
# OPENAI_BASE_URL=https://your-custom-openai-api.com/v1  # 自定义服务器

# Anthropic自定义URL (如果支持)
ANTHROPIC_BASE_URL=https://api.anthropic.com
# ANTHROPIC_BASE_URL=https://your-custom-anthropic-api.com

# Groq自定义URL (如果支持)
GROQ_BASE_URL=https://api.groq.com/openai/v1
# GROQ_BASE_URL=https://your-custom-groq-api.com/v1

# 自定义请求头 (JSON格式)
# CUSTOM_HEADERS={"User-Agent": "MyRAGSystem/1.0", "X-Custom-Header": "value"}

# Default LLM Settings
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=glm-4-flash
DEFAULT_EMBEDDING_MODEL=BAAI/bge-large-zh
DEFAULT_TEMPERATURE=0.7
DEFAULT_MAX_TOKENS=4096

# Graphiti Configuration
NEO4J_URI=neo4j://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=00000000
GRAPHITI_DATABASE=new

# Milvus Configuration
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_DB_NAME=mcp_rag
MILVUS_COLLECTION_NAME=documents

# System Configuration
SEMAPHORE_LIMIT=10
MAX_RETRIES=3
TIMEOUT_SECONDS=60
LOG_LEVEL=INFO

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000
MCP_TRANSPORT_TYPE=http

# RAG Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
TOP_K_RETRIEVAL=5
SIMILARITY_THRESHOLD=0.7