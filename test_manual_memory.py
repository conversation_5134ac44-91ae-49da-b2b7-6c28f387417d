#!/usr/bin/env python3
"""
手动添加记忆测试脚本
独立测试Graphiti记忆功能，逐步排错
"""
import asyncio
import os
import json
from datetime import datetime, timezone
from dotenv import load_dotenv
from loguru import logger

# 设置详细日志
logger.remove()
logger.add(lambda msg: print(msg, end=""), level="DEBUG")


async def test_direct_graphiti():
    """直接测试Graphiti核心功能"""
    print("🔬 直接测试Graphiti核心功能")
    print("=" * 60)
    
    load_dotenv()
    
    try:
        # 步骤1: 直接导入和初始化Graphiti
        print("📋 步骤1: 直接初始化Graphiti...")
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
        
        # 使用文档中的方式初始化
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j", 
            password="password"
        )
        print("✅ Graphiti客户端创建成功")
        
        # 步骤2: 构建索引
        print("\n📋 步骤2: 构建数据库索引...")
        try:
            await client.build_indices_and_constraints()
            print("✅ 索引构建成功")
        except Exception as e:
            print(f"⚠️ 索引构建失败: {e}")
            print("继续测试...")
        
        # 步骤3: 手动添加简单Episode
        print("\n📋 步骤3: 手动添加Episode...")
        test_episodes = [
            {
                "name": "用户张三初始化",
                "body": "用户张三开始使用财务分析系统，偏好分析科技股",
                "description": "用户初始化"
            },
            {
                "name": "财务分析对话1", 
                "body": "用户张三: 请分析苹果公司的财务状况\n助手: 苹果公司现金流充裕，ROE约30%",
                "description": "财务对话"
            },
            {
                "name": "用户偏好记录",
                "body": "张三偏好: 科技股、蓝筹股、关注ROE和现金流指标",
                "description": "偏好记录"
            }
        ]
        
        for i, episode_data in enumerate(test_episodes, 1):
            try:
                print(f"  添加Episode {i}: {episode_data['name']}")
                await client.add_episode(
                    name=episode_data["name"],
                    episode_body=episode_data["body"],
                    source=EpisodeType.text,
                    reference_time=datetime.now(timezone.utc),
                    source_description=episode_data["description"]
                )
                print(f"  ✅ Episode {i} 添加成功")
            except Exception as e:
                print(f"  ❌ Episode {i} 添加失败: {e}")
                logger.exception(f"Episode {i} error")
        
        # 步骤4: 等待处理
        print("\n⏳ 等待Graphiti处理Episode...")
        await asyncio.sleep(5)
        
        # 步骤5: 测试搜索功能
        print("\n📋 步骤4: 测试搜索功能...")
        search_queries = [
            "张三",
            "苹果公司", 
            "财务分析",
            "ROE",
            "科技股"
        ]
        
        for query in search_queries:
            try:
                print(f"  搜索: '{query}'")
                
                # 方法1: 使用_search
                try:
                    search_result = await client._search(
                        query,
                        NODE_HYBRID_SEARCH_EPISODE_MENTIONS
                    )
                    print(f"    _search结果: {len(search_result.nodes)} 个节点")
                    
                    if search_result.nodes:
                        for j, node in enumerate(search_result.nodes[:2], 1):
                            print(f"      节点{j}: {node.uuid}")
                            print(f"      名称: {getattr(node, 'name', 'N/A')}")
                            
                except Exception as e:
                    print(f"    _search失败: {e}")
                
                # 方法2: 使用search (如果有节点的话)
                try:
                    if hasattr(client, 'search'):
                        edge_results = await client.search(
                            query=query,
                            num_results=3
                        )
                        print(f"    search结果: {len(edge_results)} 条边")
                        
                        for k, edge in enumerate(edge_results[:2], 1):
                            if hasattr(edge, 'fact'):
                                print(f"      边{k}: {edge.fact[:50]}...")
                except Exception as e:
                    print(f"    search失败: {e}")
                    
            except Exception as e:
                print(f"  ❌ 搜索'{query}'失败: {e}")
        
        # 步骤6: 测试以用户为中心的搜索
        print("\n📋 步骤5: 测试以用户为中心的搜索...")
        try:
            # 先找到张三的节点
            user_search = await client._search("张三", NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
            
            if user_search.nodes:
                user_node_uuid = user_search.nodes[0].uuid
                print(f"  找到用户节点: {user_node_uuid}")
                
                # 以用户节点为中心搜索
                center_queries = ["财务分析", "苹果公司", "偏好"]
                for query in center_queries:
                    try:
                        print(f"  以用户为中心搜索: '{query}'")
                        edge_results = await client.search(
                            query=query,
                            center_node_uuid=user_node_uuid,
                            num_results=3
                        )
                        print(f"    结果: {len(edge_results)} 条相关边")
                        
                        for edge in edge_results[:2]:
                            if hasattr(edge, 'fact'):
                                print(f"      事实: {edge.fact[:60]}...")
                    except Exception as e:
                        print(f"    中心搜索失败: {e}")
            else:
                print("  ⚠️ 未找到用户节点")
                
        except Exception as e:
            print(f"  ❌ 中心搜索测试失败: {e}")
        
        # 步骤7: 检查数据库状态
        print("\n📋 步骤6: 检查数据库状态...")
        try:
            # 这里可以添加更多的数据库状态检查
            print("  数据库连接正常")
            
            # 尝试获取一些基本统计
            print("  Graphiti客户端状态检查完成")
        except Exception as e:
            print(f"  ❌ 数据库状态检查失败: {e}")
        
        print("\n🎉 直接Graphiti测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 直接Graphiti测试失败: {e}")
        logger.exception("Direct graphiti test error")
        return False


async def test_with_custom_llm():
    """使用自定义LLM测试Graphiti"""
    print("\n" + "=" * 60)
    print("🤖 使用自定义LLM测试Graphiti")
    print("=" * 60)
    
    try:
        # 步骤1: 初始化LLM组件
        print("📋 步骤1: 初始化LLM组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.graphiti_llm import GraphitiLLM
        from core.graphiti_embedder import GraphitiEmbedder
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 未找到API密钥")
            return False
            
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.3,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器
        embedding_manager = EmbeddingManager()
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general"
        )
        await embedding_manager.add_client("chinese", chinese_client)
        
        print("✅ LLM和嵌入组件初始化完成")
        
        # 步骤2: 创建Graphiti包装器
        print("\n📋 步骤2: 创建Graphiti包装器...")
        graphiti_llm = GraphitiLLM(llm_manager)
        graphiti_embedder = GraphitiEmbedder(embedding_manager.get_client("chinese"))
        
        print("✅ Graphiti包装器创建完成")
        
        # 步骤3: 测试包装器功能
        print("\n📋 步骤3: 测试LLM包装器...")
        try:
            test_messages = ["测试LLM包装器的响应能力"]
            llm_response = await graphiti_llm._generate_response(test_messages)
            print(f"  LLM响应格式: {type(llm_response)}")
            print(f"  响应内容: {llm_response.get('content', 'N/A')[:50]}...")
            print("  ✅ LLM包装器工作正常")
        except Exception as e:
            print(f"  ❌ LLM包装器测试失败: {e}")
        
        # 步骤4: 测试嵌入包装器
        print("\n📋 步骤4: 测试嵌入包装器...")
        try:
            test_text = "这是一个测试文本用于嵌入"
            embedding_result = await graphiti_embedder.embed(test_text)
            print(f"  嵌入维度: {len(embedding_result) if embedding_result else 'N/A'}")
            print("  ✅ 嵌入包装器工作正常")
        except Exception as e:
            print(f"  ❌ 嵌入包装器测试失败: {e}")
        
        # 步骤5: 使用自定义LLM初始化Graphiti
        print("\n📋 步骤5: 使用自定义LLM初始化Graphiti...")
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="password",
            llm_client=graphiti_llm,
            embedder=graphiti_embedder,
            store_raw_episode_content=True
        )
        
        await client.build_indices_and_constraints()
        print("✅ 自定义LLM的Graphiti初始化完成")
        
        # 步骤6: 测试添加Episode
        print("\n📋 步骤6: 测试添加Episode...")
        try:
            await client.add_episode(
                name="自定义LLM测试",
                episode_body="使用自定义LLM和嵌入模型测试Graphiti功能",
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="自定义LLM测试"
            )
            print("✅ Episode添加成功")
        except Exception as e:
            print(f"❌ Episode添加失败: {e}")
            logger.exception("Custom LLM episode error")
        
        # 步骤7: 等待并测试搜索
        print("\n⏳ 等待处理...")
        await asyncio.sleep(3)
        
        print("\n📋 步骤7: 测试搜索...")
        try:
            from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            search_result = await client._search(
                "自定义LLM",
                NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            )
            print(f"搜索结果: {len(search_result.nodes)} 个节点")
            print("✅ 搜索功能正常")
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
        
        print("\n🎉 自定义LLM测试完成!")
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义LLM测试失败: {e}")
        logger.exception("Custom LLM test error")
        return False


async def test_memory_operations():
    """测试记忆操作的详细流程"""
    print("\n" + "=" * 60)
    print("💾 测试记忆操作详细流程")
    print("=" * 60)
    
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
        
        # 简单初始化
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="password"
        )
        
        await client.build_indices_and_constraints()
        
        # 测试一个完整的记忆添加和检索流程
        print("📋 测试完整记忆流程...")
        
        # 1. 创建用户
        user_id = "test_user_memory"
        print(f"  创建用户: {user_id}")
        
        await client.add_episode(
            name=f"用户{user_id}创建",
            episode_body=f"用户{user_id}开始使用系统，关注金融分析",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="用户创建"
        )
        
        # 2. 添加用户对话
        conversations = [
            "用户问: 腾讯的财务状况如何？",
            "助手答: 腾讯现金流充裕，营收增长稳定",
            "用户问: ROE是多少？", 
            "助手答: 腾讯的ROE约为15%左右"
        ]
        
        for i, conv in enumerate(conversations, 1):
            await client.add_episode(
                name=f"对话{i}",
                episode_body=f"{user_id}: {conv}",
                source=EpisodeType.message,
                reference_time=datetime.now(timezone.utc),
                source_description="用户对话"
            )
            print(f"  添加对话{i}: {conv[:30]}...")
        
        # 3. 添加用户偏好
        preferences = [
            "偏好分析科技股",
            "关注ROE和现金流指标",
            "投资风格偏保守"
        ]
        
        for i, pref in enumerate(preferences, 1):
            await client.add_episode(
                name=f"偏好{i}",
                episode_body=f"{user_id}的偏好: {pref}",
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="用户偏好"
            )
            print(f"  添加偏好{i}: {pref}")
        
        # 4. 等待处理
        print("\n⏳ 等待Graphiti处理所有Episode...")
        await asyncio.sleep(8)  # 给更多时间处理
        
        # 5. 测试各种搜索
        print("\n📋 测试多种搜索方式...")
        
        search_tests = [
            {"query": user_id, "desc": "用户ID搜索"},
            {"query": "腾讯", "desc": "公司名搜索"},
            {"query": "ROE", "desc": "财务指标搜索"}, 
            {"query": "科技股", "desc": "偏好搜索"},
            {"query": "财务状况", "desc": "问题搜索"}
        ]
        
        user_node_uuid = None
        
        for test in search_tests:
            try:
                print(f"\n  {test['desc']}: '{test['query']}'")
                
                # 节点搜索
                node_result = await client._search(
                    test['query'],
                    NODE_HYBRID_SEARCH_EPISODE_MENTIONS
                )
                print(f"    节点结果: {len(node_result.nodes)} 个")
                
                if node_result.nodes and not user_node_uuid and user_id in test['query']:
                    user_node_uuid = node_result.nodes[0].uuid
                    print(f"    找到用户节点: {user_node_uuid}")
                
                # 边搜索
                try:
                    edge_result = await client.search(
                        query=test['query'],
                        num_results=3
                    )
                    print(f"    边结果: {len(edge_result)} 条")
                    
                    for edge in edge_result[:1]:
                        if hasattr(edge, 'fact'):
                            print(f"      事实: {edge.fact[:50]}...")
                            
                except Exception as e:
                    print(f"    边搜索失败: {e}")
                    
            except Exception as e:
                print(f"    搜索失败: {e}")
        
        # 6. 如果找到用户节点，测试中心搜索
        if user_node_uuid:
            print(f"\n📋 测试以用户为中心的搜索 (UUID: {user_node_uuid[:8]}...)...")
            
            center_tests = ["财务", "腾讯", "偏好", "ROE"]
            for query in center_tests:
                try:
                    edge_result = await client.search(
                        query=query,
                        center_node_uuid=user_node_uuid,
                        num_results=3
                    )
                    print(f"  '{query}': {len(edge_result)} 条相关记忆")
                    
                    for edge in edge_result[:1]:
                        if hasattr(edge, 'fact'):
                            print(f"    {edge.fact[:40]}...")
                            
                except Exception as e:
                    print(f"  中心搜索'{query}'失败: {e}")
        else:
            print("\n⚠️ 未找到用户节点，跳过中心搜索测试")
        
        print("\n🎉 记忆操作测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 记忆操作测试失败: {e}")
        logger.exception("Memory operations test error")
        return False


async def main():
    """主测试函数"""
    print("🧪 Graphiti记忆模块手动测试")
    print("🔧 问题排错和功能验证")
    print("=" * 80)
    
    test_results = []
    
    # 测试1: 直接Graphiti功能
    result1 = await test_direct_graphiti()
    test_results.append(("直接Graphiti测试", result1))
    
    # 测试2: 自定义LLM
    result2 = await test_with_custom_llm()
    test_results.append(("自定义LLM测试", result2))
    
    # 测试3: 记忆操作
    result3 = await test_memory_operations()
    test_results.append(("记忆操作测试", result3))
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Graphiti记忆功能正常工作")
    else:
        print("⚠️ 部分测试失败，需要进一步排错")
        print("\n🔧 排错建议:")
        print("1. 检查Neo4j数据库是否正常运行")
        print("2. 确认API密钥配置正确")
        print("3. 检查网络连接和防火墙设置")
        print("4. 查看详细错误日志进行针对性修复")


if __name__ == "__main__":
    asyncio.run(main())