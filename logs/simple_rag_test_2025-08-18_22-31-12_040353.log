2025-08-18 22:31:12.043 | INFO     | __main__:initialize:35 - Initializing Simple RAG System...
2025-08-18 22:31:12.043 | INFO     | core.llm_client:initialize:107 - Using custom OpenAI base URL: https://open.bigmodel.cn/api/paas/v4/
2025-08-18 22:31:12.107 | INFO     | core.llm_client:initialize:118 - Initialized OpenAI client with model: glm-4-flash
2025-08-18 22:31:12.107 | INFO     | core.llm_client:add_client:411 - Added LLM client: primary
2025-08-18 22:31:20.323 | INFO     | core.embedding_client:initialize:154 - Initialized SentenceTransformer client with model: BAAI/bge-base-zh
2025-08-18 22:31:20.323 | INFO     | core.embedding_client:initialize:354 - Initialized Chinese embedding client: BGE基础中文模型，平衡性能和速度
2025-08-18 22:31:20.323 | INFO     | core.embedding_client:add_client:508 - Added embedding client: chinese
2025-08-18 22:31:20.323 | INFO     | core.embedding_client:set_primary:515 - Set primary embedding client: chinese
2025-08-18 22:31:20.323 | INFO     | __main__:initialize:64 - Simple RAG System initialized successfully
2025-08-18 22:31:20.642 | INFO     | core.llm_client:generate:449 - Attempting generation with client: primary
2025-08-18 22:31:24.574 | INFO     | core.llm_client:generate:449 - Attempting generation with client: primary
2025-08-18 22:31:29.716 | INFO     | core.llm_client:generate:449 - Attempting generation with client: primary
2025-08-18 22:31:30.552 | INFO     | core.llm_client:cleanup:494 - Cleaned up all LLM clients
2025-08-18 22:31:30.552 | INFO     | core.embedding_client:cleanup:619 - Cleaned up all embedding clients
2025-08-18 22:31:30.556 | INFO     | __main__:cleanup:149 - Simple RAG System cleaned up
