2025-08-18 22:49:27.505 | INFO     | __main__:initialize:35 - Initializing Simple RAG System...
2025-08-18 22:49:27.505 | INFO     | core.llm_client:initialize:107 - Using custom OpenAI base URL: https://open.bigmodel.cn/api/paas/v4/
2025-08-18 22:49:27.582 | INFO     | core.llm_client:initialize:118 - Initialized OpenAI client with model: glm-4-flash
2025-08-18 22:49:27.582 | INFO     | core.llm_client:add_client:429 - Added LLM client: primary
2025-08-18 22:49:38.062 | INFO     | core.embedding_client:initialize:154 - Initialized SentenceTransformer client with model: BAAI/bge-base-zh
2025-08-18 22:49:38.062 | INFO     | core.embedding_client:initialize:354 - Initialized Chinese embedding client: BGE基础中文模型，平衡性能和速度
2025-08-18 22:49:38.062 | INFO     | core.embedding_client:add_client:508 - Added embedding client: chinese
2025-08-18 22:49:38.062 | INFO     | core.embedding_client:set_primary:515 - Set primary embedding client: chinese
2025-08-18 22:49:38.062 | INFO     | __main__:initialize:64 - Simple RAG System initialized successfully
2025-08-18 22:49:38.550 | INFO     | core.llm_client:generate:467 - Attempting generation with client: primary
2025-08-18 22:49:44.101 | INFO     | core.llm_client:generate:467 - Attempting generation with client: primary
2025-08-18 22:49:48.866 | INFO     | core.llm_client:generate:467 - Attempting generation with client: primary
2025-08-18 22:49:49.541 | INFO     | core.llm_client:cleanup:512 - Cleaned up all LLM clients
2025-08-18 22:49:49.542 | INFO     | core.embedding_client:cleanup:619 - Cleaned up all embedding clients
2025-08-18 22:49:49.542 | INFO     | __main__:cleanup:149 - Simple RAG System cleaned up
