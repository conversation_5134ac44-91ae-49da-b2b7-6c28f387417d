#!/usr/bin/env python3
"""
System Test - 快速验证核心功能
不使用任何模拟或虚假实现，直接测试真实组件
"""

import asyncio
import os
from pathlib import Path

# 确保项目根目录在Python路径中
import sys
sys.path.insert(0, str(Path(__file__).parent))

from dotenv import load_dotenv
from loguru import logger

# 只导入必要的核心组件
from core.llm_client import LLMClientFactory, LLMMessage
from core.embedding_client import EmbeddingClientFactory


async def test_llm_client():
    """测试LLM客户端"""
    print("🔧 测试LLM客户端...")
    
    try:
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        
        if not api_key:
            print("❌ 缺少OPENAI_API_KEY")
            return False
        
        client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            base_url=base_url
        )
        
        await client.initialize()
        print("✅ LLM客户端初始化成功")
        
        # 测试简单生成
        messages = [LLMMessage(role="user", content="你好，请简单介绍一下你自己")]
        response = await client.generate(messages)
        
        print(f"🤖 LLM响应: {response.content[:100]}...")
        
        await client.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        return False


async def test_embedding_client():
    """测试嵌入客户端"""
    print("\n🔧 测试嵌入客户端...")
    
    try:
        # 使用中文优化模型
        client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general",
            provider="sentence_transformers"
        )
        
        await client.initialize()
        print("✅ 中文嵌入客户端初始化成功")
        
        # 测试编码
        test_texts = [
            "人工智能是计算机科学的分支",
            "机器学习是AI的重要组成部分"
        ]
        
        response = await client.encode(test_texts)
        embeddings = response.embeddings
        
        print(f"📊 编码成功: {len(embeddings)}个向量，维度: {len(embeddings[0])}")
        
        # 测试相似度
        import numpy as np
        vec1 = np.array(embeddings[0])
        vec2 = np.array(embeddings[1])
        similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
        print(f"🎯 相似度: {similarity:.3f}")
        
        await client.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ 嵌入测试失败: {e}")
        return False


async def test_basic_integration():
    """测试基础集成功能"""
    print("\n🎯 测试基础集成...")
    
    try:
        # 测试LLM + 嵌入的简单组合
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL")
        
        # LLM客户端
        llm_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            base_url=base_url
        )
        
        # 嵌入客户端
        embedding_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-base-zh",  # 使用较小模型加快测试
            provider="sentence_transformers"
        )
        
        await llm_client.initialize()
        await embedding_client.initialize()
        
        print("✅ 双客户端初始化成功")
        
        # 模拟简单的问答流程
        user_question = "什么是深度学习？"
        
        # 1. 编码问题
        question_embedding = await embedding_client.encode(user_question)
        print(f"📝 问题编码完成，维度: {len(question_embedding.embeddings[0])}")
        
        # 2. 生成回答
        messages = [LLMMessage(role="user", content=user_question)]
        llm_response = await llm_client.generate(messages)
        print(f"🤖 LLM回答: {llm_response.content[:100]}...")
        
        # 3. 编码回答
        answer_embedding = await embedding_client.encode(llm_response.content)
        print("✅ 回答编码完成")
        
        await llm_client.cleanup()
        await embedding_client.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


async def test_memory_optional():
    """测试记忆组件（可选，如果Neo4j可用）"""
    print("\n🧠 测试记忆组件（可选）...")
    
    try:
        # 只有在Neo4j可用时才测试
        neo4j_uri = os.getenv("NEO4J_URI")
        neo4j_user = os.getenv("NEO4J_USER") 
        neo4j_password = os.getenv("NEO4J_PASSWORD")
        
        if not all([neo4j_uri, neo4j_user, neo4j_password]):
            print("⚠️  Neo4j配置不完整，跳过记忆测试")
            return True
        
        # 简单连接测试
        from graphiti_core import Graphiti
        
        client = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password
        )
        
        # 测试基本连接
        # await client.build_indices_and_constraints()
        print("✅ Graphiti连接成功")
        
        # 这里不进行实际操作，只验证连接
        return True
        
    except ImportError:
        print("⚠️  graphiti_core未安装，跳过记忆测试")
        return True
    except Exception as e:
        print(f"⚠️  记忆测试失败: {e}")
        return True  # 记忆组件失败不影响核心功能


async def main():
    """主测试函数"""
    print("🌟 系统核心功能测试")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 配置日志
    logger.add(
        "logs/system_test_{time}.log",
        rotation="10 MB",
        retention="3 days",
        level="INFO"
    )
    
    test_results = []
    
    try:
        # 1. 测试LLM
        llm_ok = await test_llm_client()
        test_results.append(("LLM客户端", llm_ok))
        
        # 2. 测试嵌入
        embedding_ok = await test_embedding_client()
        test_results.append(("嵌入客户端", embedding_ok))
        
        # 3. 测试基础集成
        if llm_ok and embedding_ok:
            integration_ok = await test_basic_integration()
            test_results.append(("基础集成", integration_ok))
        else:
            test_results.append(("基础集成", False))
        
        # 4. 测试记忆（可选）
        memory_ok = await test_memory_optional()
        test_results.append(("记忆组件", memory_ok))
        
        # 显示测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总")
        print("=" * 50)
        
        success_count = 0
        for test_name, success in test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{test_name:<12}: {status}")
            if success:
                success_count += 1
        
        print(f"\n总体结果: {success_count}/{len(test_results)} 项测试通过")
        
        # 核心功能必须都通过
        core_tests = test_results[:3]  # LLM, 嵌入, 集成
        core_success = all(result[1] for result in core_tests)
        
        if core_success:
            print("🎉 核心功能测试全部通过！")
            print("系统已准备就绪，可以进行下一步开发。")
            return True
        else:
            print("⚠️  核心功能测试存在问题，请检查配置。")
            return False
        
    except KeyboardInterrupt:
        print("\n⛔ 测试被用户中断")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生严重错误: {e}")
        logger.error(f"System test error: {e}")
        return False


if __name__ == "__main__":
    # 确保logs目录存在
    import os
    os.makedirs("logs", exist_ok=True)
    
    success = asyncio.run(main())
    exit(0 if success else 1)