#!/usr/bin/env python3
"""
测试JSON格式精确匹配问题的修复
验证实体提取和关系创建的JSON格式是否正确
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from core.memory_manager import GraphitiMemoryManager
from core.graphiti_llm import GraphitiLLM
from core.llm_client import LLMManager
from graphiti_core.nodes import EpisodeType

async def test_entity_extraction_json_format():
    """测试实体提取的JSON格式"""
    print("=== 测试实体提取JSON格式 ===")
    
    # 创建LLM管理器
    llm_manager = LLMManager()
    graphiti_llm = GraphitiLLM(llm_manager)
    
    # 创建内存管理器
    memory_manager = GraphitiMemoryManager(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j", 
        neo4j_password="00000000"
    )
    
    await memory_manager.initialize()
    
    try:
        # 测试1：添加包含实体和关系的记忆
        test_content = "张三在北京大学工作，他是一名计算机科学教授。"
        
        print(f"测试内容: {test_content}")
        
        # 添加记忆
        result = await memory_manager.add_memory(
            user_id="test_user_001",
            content=test_content,
            memory_type="fact"
        )
        
        print(f"添加记忆结果: {result}")
        
        # 测试2：搜索实体
        entities = await memory_manager.search_nodes(
            query="张三",
            limit=5
        )
        print(f"搜索到的实体: {entities}")
        
        # 测试3：搜索关系
        facts = await memory_manager.search_facts(
            query="张三 北京大学"
        )
        print(f"搜索到的事实: {facts}")
        
        # 测试4：获取所有实体和关系
        status = await memory_manager.get_status()
        print(f"系统状态: {status}")
        
        # 验证实体和关系是否成功创建
        if entities and len(entities) > 0:
            print("✅ 实体提取成功 - JSON格式匹配正确")
        else:
            print("❌ 实体提取失败 - 检查JSON格式")
            
        if facts and len(facts) > 0:
            print("✅ 关系创建成功 - JSON格式匹配正确")  
        else:
            print("❌ 关系创建失败 - 检查JSON格式")
            
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await memory_manager.close()

async def test_conversation_management():
    """测试对话管理范式"""
    print("\n=== 测试对话管理范式 ===")
    
    llm_manager = LLMManager()
    graphiti_llm = GraphitiLLM(llm_manager)
    
    memory_manager = GraphitiMemoryManager(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password", 
        llm_client=graphiti_llm
    )
    
    await memory_manager.initialize()
    
    try:
        # 创建用户节点
        user_id = "user_test_001"
        await memory_manager.create_user_node(user_id, "测试用户")
        
        # 添加对话轮次
        conversations = [
            "你好，我是测试用户",
            "我想了解关于人工智能的知识",
            "特别是机器学习方面的内容",
            "我在北京大学学习计算机科学"
        ]
        
        for i, content in enumerate(conversations):
            await memory_manager.add_conversation_episode(
                user_id=user_id,
                content=content,
                episode_type=EpisodeType.text
            )
            print(f"添加对话 {i+1}: {content}")
        
        # 搜索用户相关记忆
        user_context = await memory_manager.search_user_context(
            user_id=user_id,
            query="机器学习"
        )
        print(f"用户上下文搜索结果: {user_context}")
        
        # 获取用户所有对话
        episodes = await memory_manager.get_episodes(user_id=user_id)
        print(f"用户对话记录: {len(episodes)} 条")
        
        return True
        
    except Exception as e:
        print(f"对话管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await memory_manager.close()

async def main():
    """主测试函数"""
    print("开始测试JSON格式精确匹配问题修复...")
    
    # 测试1：实体提取
    entity_test = await test_entity_extraction_json_format()
    
    # 测试2：对话管理
    conversation_test = await test_conversation_management()
    
    if entity_test and conversation_test:
        print("\n🎉 所有测试通过！JSON格式精确匹配问题已解决")
        return True
    else:
        print("\n❌ 测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    asyncio.run(main())