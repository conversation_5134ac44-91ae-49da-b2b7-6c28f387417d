#!/usr/bin/env python3
"""
测试 Graphiti 关系连接功能
验证实体之间是否建立了正确的关系
"""
import asyncio
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

async def test_graphiti_relationships():
    """测试 Graphiti 实体关系功能"""
    print("🔗 测试 Graphiti 实体关系连接")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 步骤1: 初始化组件
        print("📋 步骤1: 初始化 Graphiti 组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.graphiti_llm import GraphitiLLM
        from core.graphiti_embedder import GraphitiEmbedder
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.3,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器
        embedding_manager = EmbeddingManager()
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general"
        )
        await embedding_manager.add_client("chinese", chinese_client)
        
        # Graphiti 包装器
        graphiti_llm = GraphitiLLM(llm_manager)
        primary_embedder = embedding_manager.get_primary_client()
        graphiti_embedder = GraphitiEmbedder(primary_embedder)
        
        print("✅ 组件初始化完成")
        
        # 步骤2: 初始化 Graphiti
        print("\n📋 步骤2: 初始化 Graphiti...")
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j", 
            password="00000000",
            llm_client=graphiti_llm,
            embedder=graphiti_embedder,
            store_raw_episode_content=True
        )
        
        await client.build_indices_and_constraints()
        print("✅ Graphiti 初始化成功")
        
        # 步骤3: 添加包含明确实体关系的 Episode
        print("\n📋 步骤3: 添加包含实体关系的 Episode...")
        
        # 关系性 Episode 1: 用户和公司关系
        await client.add_episode(
            name="用户关注苹果公司",
            episode_body="投资者张三特别关注苹果公司(Apple Inc.)的财务表现，他认为苹果是科技行业的领导者",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="用户投资偏好"
        )
        print("  ✅ Episode 1: 用户-公司关系")
        
        # 关系性 Episode 2: 公司和财务指标关系
        await client.add_episode(
            name="苹果财务分析",
            episode_body="苹果公司2023年第四季度的ROE达到30%，净利润率为25%，现金流非常充裕达到1000亿美元",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="财务数据分析"
        )
        print("  ✅ Episode 2: 公司-财务指标关系")
        
        # 关系性 Episode 3: 用户决策和分析师意见
        await client.add_episode(
            name="分析师建议",
            episode_body="高盛分析师约翰·史密斯建议张三买入苹果股票，因为其强劲的财务表现和创新能力",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="投资建议"
        )
        print("  ✅ Episode 3: 分析师-用户-投资建议关系")
        
        # 关系性 Episode 4: 对话形式的复杂关系
        conversation = """
对话记录:
张三: 我想了解苹果公司和微软公司的对比分析
财务顾问: 苹果公司的ROE为30%，而微软的ROE为35%。从现金流角度看，苹果为1000亿美元，微软为800亿美元
张三: 那我应该选择哪个？
财务顾问: 基于您的风险偏好和投资目标，我建议投资组合中苹果占60%，微软占40%
        """
        await client.add_episode(
            name="投资对话咨询",
            episode_body=conversation,
            source=EpisodeType.message,
            reference_time=datetime.now(timezone.utc),
            source_description="投资咨询对话"
        )
        print("  ✅ Episode 4: 复杂对话关系")
        
        # 步骤4: 等待 Graphiti 处理和建立关系
        print("\n📋 步骤4: 等待 Graphiti 处理并建立关系...")
        print("  ⏳ 处理中...")
        await asyncio.sleep(10)  # 给足够时间处理
        
        # 步骤5: 验证实体提取
        print("\n📋 步骤5: 验证实体和关系提取...")
        
        # 搜索实体
        from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
        
        entities_to_search = [
            "张三",
            "苹果公司", 
            "Apple",
            "微软",
            "ROE",
            "约翰·史密斯",
            "高盛"
        ]
        
        found_entities = {}
        
        for entity in entities_to_search:
            try:
                search_result = await client._search(entity, NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
                if search_result.nodes:
                    found_entities[entity] = search_result.nodes[0].uuid
                    print(f"  ✅ 找到实体: {entity} (UUID: {search_result.nodes[0].uuid[:8]}...)")
                else:
                    print(f"  ❌ 未找到实体: {entity}")
            except Exception as e:
                print(f"  ❌ 搜索实体 {entity} 失败: {e}")
        
        # 步骤6: 测试关系查询
        print(f"\n📋 步骤6: 测试实体关系查询...")
        
        if "张三" in found_entities:
            user_uuid = found_entities["张三"]
            print(f"  以用户张三为中心查询关系 (UUID: {user_uuid[:8]}...)")
            
            # 查询与张三相关的关系
            related_queries = [
                "苹果公司",
                "投资",
                "财务分析", 
                "ROE",
                "建议"
            ]
            
            for query in related_queries:
                try:
                    edge_results = await client.search(
                        query=query,
                        center_node_uuid=user_uuid,
                        num_results=5
                    )
                    
                    if edge_results:
                        print(f"    ✅ '{query}' 相关关系: {len(edge_results)} 条")
                        for i, edge in enumerate(edge_results[:2], 1):
                            if hasattr(edge, 'fact'):
                                print(f"      关系{i}: {edge.fact[:60]}...")
                    else:
                        print(f"    ❌ '{query}' 未找到相关关系")
                        
                except Exception as e:
                    print(f"    ❌ 查询 '{query}' 关系失败: {e}")
        else:
            print("  ❌ 未找到用户张三，无法进行关系查询")
        
        # 步骤7: 验证公司间对比关系
        print(f"\n📋 步骤7: 验证公司间对比关系...")
        
        if "苹果公司" in found_entities or "Apple" in found_entities:
            apple_uuid = found_entities.get("苹果公司") or found_entities.get("Apple")
            print(f"  以苹果公司为中心查询对比关系 (UUID: {apple_uuid[:8]}...)")
            
            comparison_queries = [
                "微软",
                "对比",
                "ROE", 
                "现金流",
                "投资建议"
            ]
            
            for query in comparison_queries:
                try:
                    edge_results = await client.search(
                        query=query,
                        center_node_uuid=apple_uuid,
                        num_results=3
                    )
                    
                    if edge_results:
                        print(f"    ✅ '{query}' 对比关系: {len(edge_results)} 条")
                        for edge in edge_results[:1]:
                            if hasattr(edge, 'fact'):
                                print(f"      对比信息: {edge.fact[:80]}...")
                    else:
                        print(f"    ❌ '{query}' 未找到对比关系")
                        
                except Exception as e:
                    print(f"    ❌ 查询对比关系 '{query}' 失败: {e}")
        
        # 步骤8: 测试复杂关系推理
        print(f"\n📋 步骤8: 测试复杂关系推理...")
        
        complex_queries = [
            "张三对苹果公司的投资决策",
            "高盛分析师的建议对张三的影响",
            "苹果和微软的财务对比分析",
            "投资组合建议中的比例分配"
        ]
        
        for query in complex_queries:
            try:
                # 全局搜索复杂关系
                edge_results = await client.search(
                    query=query,
                    num_results=3
                )
                
                if edge_results:
                    print(f"  ✅ 复杂关系查询 '{query}': {len(edge_results)} 条结果")
                    for edge in edge_results[:1]:
                        if hasattr(edge, 'fact'):
                            print(f"    推理结果: {edge.fact[:100]}...")
                else:
                    print(f"  ❌ 复杂关系查询 '{query}': 未找到结果")
                    
            except Exception as e:
                print(f"  ❌ 复杂关系查询失败: {e}")
        
        # 步骤9: 关系连接总结
        print(f"\n📋 步骤9: 关系连接状态总结...")
        
        total_entities = len(found_entities)
        if total_entities >= 4:
            print(f"  ✅ 实体提取成功: {total_entities}/7 个实体被识别")
            print(f"  ✅ 关系图谱基础建立成功")
            
            if total_entities >= 6:
                print(f"  🎉 关系连接功能表现优秀!")
                relationship_status = "excellent"
            else:
                print(f"  ✅ 关系连接功能正常工作")
                relationship_status = "good"
        else:
            print(f"  ⚠️ 实体提取不足: {total_entities}/7")
            print(f"  ⚠️ 关系连接需要改进")
            relationship_status = "needs_improvement"
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        
        return relationship_status
        
    except Exception as e:
        print(f"❌ 关系测试失败: {e}")
        import traceback
        traceback.print_exc()
        return "failed"

async def main():
    """主函数"""
    print("🔗 Graphiti 实体关系连接测试")
    print("🎯 验证实体提取和关系建立功能")
    print("=" * 60)
    
    result = await test_graphiti_relationships()
    
    print("\n" + "=" * 60)
    print("📊 关系连接测试结果:")
    
    if result == "excellent":
        print("🎉 关系连接功能优秀!")
        print("✅ 实体提取准确")
        print("✅ 关系建立成功") 
        print("✅ 复杂查询正常工作")
    elif result == "good":
        print("✅ 关系连接功能正常!")
        print("✅ 基本实体和关系识别成功")
        print("⚠️ 可进一步优化复杂关系推理")
    elif result == "needs_improvement":
        print("⚠️ 关系连接需要改进")
        print("🔧 建议:")
        print("  1. 增加 Episode 处理等待时间")
        print("  2. 优化实体提取的 prompt")
        print("  3. 检查嵌入模型的相似度阈值")
    else:
        print("❌ 关系连接测试失败")
        print("🔧 需要排查技术问题")

if __name__ == "__main__":
    asyncio.run(main())