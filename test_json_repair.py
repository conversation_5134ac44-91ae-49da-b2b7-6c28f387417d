#!/usr/bin/env python3
"""
测试JSON格式修复功能
"""
import asyncio
import json
from core.graphiti_llm import GraphitiLLM
from core.llm_client import LLMManager

# 模拟Graphiti模型结构
class MockExtractedEntities:
    model_fields = {
        'extracted_entities': {'annotation': list}
    }
    
    def __init__(self, extracted_entities=None):
        self.extracted_entities = extracted_entities or []
    
    def model_dump_json(self):
        return json.dumps({"extracted_entities": self.extracted_entities})

class MockExtractedEdges:
    model_fields = {
        'edges': {'annotation': list}
    }
    
    def __init__(self, edges=None):
        self.edges = edges or []
    
    def model_dump_json(self):
        return json.dumps({"edges": self.edges})

async def test_json_format_repair():
    """测试JSON格式修复功能"""
    print("=== 测试JSON格式修复功能 ===")
    
    # 创建GraphitiLLM实例
    llm_manager = LLMManager()
    graphiti_llm = GraphitiLLM(llm_manager)
    
    # 测试用例
    test_cases = [
        {
            "name": "标准实体格式",
            "input": {"extracted_entities": [{"name": "张三", "entity_type_id": 1, "summary": "测试", "attributes": {}}]},
            "model": MockExtractedEntities,
            "expected_success": True
        },
        {
            "name": "旧格式实体",
            "input": {"entities": [{"name": "张三"}]},
            "model": MockExtractedEntities,
            "expected_success": True
        },
        {
            "name": "空实体",
            "input": {},
            "model": MockExtractedEntities,
            "expected_success": True
        },
        {
            "name": "错误字段实体",
            "input": {"wrong_field": [{"name": "测试"}]},
            "model": MockExtractedEntities,
            "expected_success": True
        },
        {
            "name": "标准关系格式",
            "input": {
                "edges": [{
                    "source_node_name": "张三",
                    "target_node_name": "公司",
                    "relation_name": "works_at",
                    "relation_type": "RELATES_TO",
                    "source_entity_id": 1,
                    "target_entity_id": 2,
                    "fact": "张三在公司工作",
                    "summary": "雇佣关系",
                    "attributes": {}
                }]
            },
            "model": MockExtractedEdges,
            "expected_success": True
        },
        {
            "name": "旧格式关系",
            "input": {"relationships": [{"source": "张三", "target": "公司"}]},
            "model": MockExtractedEdges,
            "expected_success": True
        },
        {
            "name": "空关系",
            "input": {},
            "model": MockExtractedEdges,
            "expected_success": True
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        print(f"输入: {test_case['input']}")
        
        try:
            # 使用预处理方法
            processed = graphiti_llm._preprocess_json_for_model(
                test_case['input'],
                test_case['model'].__name__
            )
            
            print(f"处理结果: {json.dumps(processed, ensure_ascii=False, indent=2)}")
            
            # 验证结果
            is_valid = True
            if test_case['model'].__name__ == "MockExtractedEntities":
                has_entities = "extracted_entities" in processed
                entities_list = processed.get("extracted_entities", [])
                is_valid = has_entities and isinstance(entities_list, list)
                
            elif test_case['model'].__name__ == "MockExtractedEdges":
                has_edges = "edges" in processed
                edges_list = processed.get("edges", [])
                is_valid = has_edges and isinstance(edges_list, list)
            
            result = {
                "test": test_case['name'],
                "success": is_valid,
                "input": test_case['input'],
                "output": processed
            }
            results.append(result)
            
            print(f"✅ 成功" if is_valid else "❌ 失败")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
            results.append({
                "test": test_case['name'],
                "success": False,
                "error": str(e)
            })
    
    return results

async def test_field_mapping():
    """测试字段映射功能"""
    print("\n=== 测试字段映射功能 ===")
    
    llm_manager = LLMManager()
    graphiti_llm = GraphitiLLM(llm_manager)
    
    # 测试字段映射
    mapping_tests = [
        {
            "name": "实体字段映射",
            "input": {"entities": [{"name": "张三", "description": "一个人"}]},
            "expected": {
                "extracted_entities": [{
                    "name": "张三",
                    "entity_type_id": 1,
                    "summary": "一个人",
                    "attributes": {}
                }]
            }
        },
        {
            "name": "关系字段映射",
            "input": {
                "relationships": [{
                    "source": "张三",
                    "target": "公司",
                    "relation": "works_at"
                }]
            },
            "expected": {
                "edges": [{
                    "source_node_name": "张三",
                    "target_node_name": "公司",
                    "relation_name": "works_at",
                    "relation_type": "RELATES_TO",
                    "source_entity_id": 1,
                    "target_entity_id": 2,
                    "fact": "张三 works_at 公司",
                    "summary": "张三与公司的关系",
                    "attributes": {}
                }]
            }
        }
    ]
    
    for test in mapping_tests:
        print(f"\n--- {test['name']} ---")
        
        model_name = "ExtractedEntities" if "entities" in test['input'] else "ExtractedEdges"
        result = graphiti_llm._preprocess_json_for_model(test['input'], model_name)
        
        print(f"输入: {test['input']}")
        print(f"输出: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 验证关键字段
        success = True
        for key, expected_value in test['expected'].items():
            if key not in result:
                success = False
                break
            
            if len(result[key]) != len(expected_value):
                success = False
                break
        
        print(f"✅ 映射成功" if success else "❌ 映射失败")

async def main():
    """主测试函数"""
    print("🧪 开始JSON格式修复测试...")
    
    try:
        # 测试JSON格式修复
        repair_results = await test_json_format_repair()
        
        # 测试字段映射
        await test_field_mapping()
        
        # 汇总结果
        total_tests = len(repair_results)
        passed_tests = sum(1 for r in repair_results if r.get('success', False))
        
        print(f"\n" + "="*50)
        print("📊 测试结果汇总")
        print("="*50)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 所有JSON格式修复功能正常工作")
            return True
        else:
            print("\n⚠️ 部分功能需要检查")
            for r in repair_results:
                if not r.get('success', False):
                    print(f"   - {r['test']}: {r.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(main())