========================

CODE SNIPPETS

========================

TITLE: Install MCP Python SDK

DESCRIPTION: Installs the necessary dependencies for the MCP Python SDK client by synchronizing packages. This command should be run within the client's directory.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_snippet_0



LANGUAGE: bash

CODE:

```

cd examples/clients/simple-auth-client

uv sync --reinstall

```



----------------------------------------



TITLE: MCP Auth Client Interaction Example

DESCRIPTION: Demonstrates an interactive session with the MCP simple authentication client. It shows the connection process, authorization URL, listing available tools, calling the 'echo' tool, and exiting the client.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_snippet_3



LANGUAGE: markdown

CODE:

```

🔐 Simple MCP Auth Client

Connecting to: http://localhost:3001



Please visit the following URL to authorize the application:

http://localhost:3001/authorize?response_type=code&client_id=...



✅ Connected to MCP server at http://localhost:3001



mcp> list

📋 Available tools:

1. echo - Echo back the input text



mcp> call echo {"text": "Hello, world!"}

🔧 Tool 'echo' result:

Hello, world!



mcp> quit

👋 Goodbye!

```



----------------------------------------



TITLE: MCP Client Configuration Variables

DESCRIPTION: Environment variables used to configure the MCP client's connection parameters. These variables allow users to specify the server's port and the desired transport type for communication.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_snippet_4



LANGUAGE: APIDOC

CODE:

```

MCP_SERVER_PORT - Server URL (default: 8000)

MCP_TRANSPORT_TYPE - Transport type: streamable_http (default) or sse

```



----------------------------------------



TITLE: Completion Client Example

DESCRIPTION: Demonstrates how to use the completion client to interact with a server, list resource templates and prompts, and complete arguments with and without context.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_18



LANGUAGE: python

CODE:

```

import asyncio

import os

from aiohttp import ClientSession



from modelcontextprotocol.client.completion import (  # Assuming these imports are correct based on context

    stdio_client,

    ResourceTemplateReference,

    PromptReference,

)

from modelcontextprotocol.client.parameters import StdioServerParameters





# Create server parameters for stdio connection

server_params = StdioServerParameters(

    command="uv",  # Using uv to run the server

    args=["run", "server", "completion", "stdio"],  # Server with completion support

    env={"UV_INDEX": os.environ.get("UV_INDEX", "")},

)





async def run():

    """Run the completion client example."""

    async with stdio_client(server_params) as (read, write):

        async with ClientSession(read, write) as session:

            # Initialize the connection

            await session.initialize()



            # List available resource templates

            templates = await session.list_resource_templates()

            print("Available resource templates:")

            for template in templates.resourceTemplates:

                print(f"  - {template.uriTemplate}")



            # List available prompts

            prompts = await session.list_prompts()

            print("\nAvailable prompts:")

            for prompt in prompts.prompts:

                print(f"  - {prompt.name}")



            # Complete resource template arguments

            if templates.resourceTemplates:

                template = templates.resourceTemplates[0]

                print(f"\nCompleting arguments for resource template: {template.uriTemplate}")



                # Complete without context

                result = await session.complete(

                    ref=ResourceTemplateReference(type="ref/resource", uri=template.uriTemplate),

                    argument={"name": "owner", "value": "model"},

                )

                print(f"Completions for 'owner' starting with 'model': {result.completion.values}")



                # Complete with context - repo suggestions based on owner

                result = await session.complete(

                    ref=ResourceTemplateReference(type="ref/resource", uri=template.uriTemplate),

                    argument={"name": "repo", "value": ""},

                    context_arguments={"owner": "modelcontextprotocol"},

                )

                print(f"Completions for 'repo' with owner='modelcontextprotocol': {result.completion.values}")



            # Complete prompt arguments

            if prompts.prompts:

                prompt_name = prompts.prompts[0].name

                print(f"\nCompleting arguments for prompt: {prompt_name}")



                result = await session.complete(

                    ref=PromptReference(type="ref/prompt", name=prompt_name),

                    argument={"name": "style", "value": ""},

                )

                print(f"Completions for 'style' argument: {result.completion.values}")





def main():

    """Entry point for the completion client."""

    asyncio.run(run())





if __name__ == "__main__":

    main()



```



----------------------------------------



TITLE: STDIO Client Example

DESCRIPTION: Illustrates how to create an MCP client that connects to an MCP server using the STDIO transport. It sets up server parameters and initiates a client session.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_38



LANGUAGE: python

CODE:

```

import asyncio

import os



from pydantic import AnyUrl



from mcp import ClientSession, StdioServerParameters, types

from mcp.client.stdio import stdio_client

from mcp.shared.context import RequestContext



# Create server parameters for stdio connection

server_params = StdioServerParameters(

    command="uv",  # Using uv to run the server

    args=["run", "server", "fastmcp_quickstart", "stdio"],  # We're already in snippets dir

    env={"UV_INDEX": os.environ.get("UV_INDEX", "")},

)



```



----------------------------------------



TITLE: Run MCP Auth Client

DESCRIPTION: Executes the MCP simple authentication client. It can be run directly or with custom environment variables to specify the server port or transport type (SSE).



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_snippet_2



LANGUAGE: bash

CODE:

```

uv run mcp-simple-auth-client



# Or with custom server URL

MCP_SERVER_PORT=3001 uv run mcp-simple-auth-client



# Use SSE transport

MCP_TRANSPORT_TYPE=sse uv run mcp-simple-auth-client

```



----------------------------------------



TITLE: Streamable HTTP Client Example

DESCRIPTION: Demonstrates connecting to an MCP service using streamable HTTP transport. It initializes the client and lists available tools.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_40



LANGUAGE: python

CODE:

```

"""

Run from the repository root:

    uv run examples/snippets/clients/streamable_basic.py

"""



import asyncio



from mcp import ClientSession

from mcp.client.streamable_http import streamablehttp_client





async def main():

    # Connect to a streamable HTTP server

    async with streamablehttp_client("http://localhost:8000/mcp") as (

        read_stream,

        write_stream,

        _,

    ):

        # Create a session using the client streams

        async with ClientSession(read_stream, write_stream) as session:

            # Initialize the connection

            await session.initialize()

            # List available tools

            tools = await session.list_tools()

            print(f"Available tools: {[tool.name for tool in tools.tools]}")





if __name__ == "__main__":

    asyncio.run(main())



```



----------------------------------------



TITLE: Client Completion Suggestions

DESCRIPTION: Illustrates client-side usage for obtaining completion suggestions, leveraging the context parameter to provide completions based on previously resolved values. This is useful for prompt arguments and resource template parameters.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_17



LANGUAGE: python

CODE:

```

"""

cd to the `examples/snippets` directory and run:

    uv run completion-client

"""



import asyncio

import os



from mcp import ClientSession, StdioServerParameters

from mcp.client.stdio import stdio_client

from mcp.types import PromptReference, ResourceTemplateReference



```



----------------------------------------



TITLE: OAuth Client Authentication Example

DESCRIPTION: Demonstrates how to set up and use an OAuth client provider for authentication with MCP servers. It includes a custom in-memory token storage, handlers for authorization redirects and callbacks, and basic interaction with the MCP server to list tools and resources.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_44



LANGUAGE: python

CODE:

```

import asyncio

from urllib.parse import parse_qs, urlparse



from pydantic import AnyUrl



from mcp import ClientSession

from mcp.client.auth import OAuthClientProvider, TokenStorage

from mcp.client.streamable_http import streamablehttp_client

from mcp.shared.auth import OAuthClientInformationFull, OAuthClientMetadata, OAuthToken





class InMemoryTokenStorage(TokenStorage):

    """Demo In-memory token storage implementation."""



    def __init__(self):

        self.tokens: OAuthToken | None = None

        self.client_info: OAuthClientInformationFull | None = None



    async def get_tokens(self) -> OAuthToken | None:

        """Get stored tokens."""

        return self.tokens



    async def set_tokens(self, tokens: OAuthToken) -> None:

        """Store tokens."""

        self.tokens = tokens



    async def get_client_info(self) -> OAuthClientInformationFull | None:

        """Get stored client information."""

        return self.client_info



    async def set_client_info(self, client_info: OAuthClientInformationFull) -> None:

        """Store client information."""

        self.client_info = client_info





async def handle_redirect(auth_url: str) -> None:

    print(f"Visit: {auth_url}")





async def handle_callback() -> tuple[str, str | None]:

    callback_url = input("Paste callback URL: ")

    params = parse_qs(urlparse(callback_url).query)

    return params["code"][0], params.get("state", [None])[0]





async def main():

    """Run the OAuth client example."""

    oauth_auth = OAuthClientProvider(

        server_url="http://localhost:8001",

        client_metadata=OAuthClientMetadata(

            client_name="Example MCP Client",

            redirect_uris=[AnyUrl("http://localhost:3000/callback")],

            grant_types=["authorization_code", "refresh_token"],

            response_types=["code"],

            scope="user",

        ),

        storage=InMemoryTokenStorage(),

        redirect_handler=handle_redirect,

        callback_handler=handle_callback,

    )



    async with streamablehttp_client("http://localhost:8001/mcp", auth=oauth_auth) as (read, write, _):

        async with ClientSession(read, write) as session:

            await session.initialize()



            tools = await session.list_tools()

            print(f"Available tools: {[tool.name for tool in tools.tools]}")



            resources = await session.list_resources()

            print(f"Available resources: {[r.uri for r in resources.resources]}")





def run():

    asyncio.run(main())





if __name__ == "__main__":

    run()



```



----------------------------------------



TITLE: Stdio Client Example

DESCRIPTION: Demonstrates connecting to an MCP service using stdio transport, initializing the client, listing prompts, resources, and tools, reading a resource, and calling a tool. Includes an optional sampling callback.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_39



LANGUAGE: python

CODE:

```

import asyncio



from mcp import ClientSession

from mcp.client.stdio import stdio_client

from mcp.shared import types

from mcp.shared.url import AnyUrl



# Optional: create a sampling callback

async def handle_sampling_message(

    context: types.RequestContext, params: types.CreateMessageRequestParams

) -> types.CreateMessageResult:

    print(f"Sampling request: {params.messages}")

    return types.CreateMessageResult(

        role="assistant",

        content=types.TextContent(

            type="text",

            text="Hello, world! from model",

        ),

        model="gpt-3.5-turbo",

        stopReason="endTurn",

    )





async def run():

    # Assuming server_params is defined elsewhere and configured for stdio

    # For example: server_params = StdioServerParameters()

    server_params = None # Placeholder, replace with actual server_params

    async with stdio_client(server_params) as (read, write):

        async with ClientSession(read, write, sampling_callback=handle_sampling_message) as session:

            # Initialize the connection

            await session.initialize()



            # List available prompts

            prompts = await session.list_prompts()

            print(f"Available prompts: {[p.name for p in prompts.prompts]}")



            # Get a prompt (greet_user prompt from fastmcp_quickstart)

            if prompts.prompts:

                prompt = await session.get_prompt("greet_user", arguments={"name": "Alice", "style": "friendly"})

                print(f"Prompt result: {prompt.messages[0].content}")



            # List available resources

            resources = await session.list_resources()

            print(f"Available resources: {[r.uri for r in resources.resources]}")



            # List available tools

            tools = await session.list_tools()

            print(f"Available tools: {[t.name for t in tools.tools]}")



            # Read a resource (greeting resource from fastmcp_quickstart)

            resource_content = await session.read_resource(AnyUrl("greeting://World"))

            content_block = resource_content.contents[0]

            if isinstance(content_block, types.TextContent):

                print(f"Resource content: {content_block.text}")



            # Call a tool (add tool from fastmcp_quickstart)

            result = await session.call_tool("add", arguments={"a": 5, "b": 3})

            result_unstructured = result.content[0]

            if isinstance(result_unstructured, types.TextContent):

                print(f"Tool result: {result_unstructured.text}")

            result_structured = result.structuredContent

            print(f"Structured tool result: {result_structured}")



def main():

    """Entry point for the client script."""

    asyncio.run(run())





if __name__ == "__main__":

    main()



```



----------------------------------------



TITLE: Python Client Interaction with MCP Fetch Tool

DESCRIPTION: This Python example illustrates how to connect to the MCP server using the `mcp.client` SDK. It shows how to initialize a client session, list available tools, and call the 'fetch' tool with a URL argument to retrieve website content.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-tool/README.md#_snippet_2



LANGUAGE: python

CODE:

```

import asyncio

from mcp.client.session import ClientSession

from mcp.client.stdio import StdioServerParameters, stdio_client





async def main():

    async with stdio_client(

        StdioServerParameters(command="uv", args=["run", "mcp-simple-tool"])

    ) as (read, write):

        async with ClientSession(read, write) as session:

            await session.initialize()



            # List available tools

            tools = await session.list_tools()

            print(tools)



            # Call the fetch tool

            result = await session.call_tool("fetch", {"url": "https://example.com"})

            print(result)





asyncio.run(main())

```



----------------------------------------



TITLE: MCP Client Interaction with Simple Prompt

DESCRIPTION: An example of using the MCP client library in Python to interact with the Simple Prompt server. It shows how to initialize the client, list available prompts, and retrieve a specific prompt with arguments.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-prompt/README.md#_snippet_1



LANGUAGE: python

CODE:

```

import asyncio

from mcp.client.session import ClientSession

from mcp.client.stdio import StdioServerParameters, stdio_client





async def main():

    async with stdio_client(

        StdioServerParameters(command="uv", args=["run", "mcp-simple-prompt"])

    ) as (read, write):

        async with ClientSession(read, write) as session:

            await session.initialize()



            # List available prompts

            prompts = await session.list_prompts()

            print(prompts)



            # Get the prompt with arguments

            prompt = await session.get_prompt(

                "simple",

                {

                    "context": "User is a software developer",

                    "topic": "Python async programming",

                },

            )

            print(prompt)





asyncio.run(main())

```



----------------------------------------



TITLE: Client Display Utilities

DESCRIPTION: Provides utilities for displaying human-readable names for tools, resources, and prompts within MCP clients.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_41



LANGUAGE: python

CODE:

```

"""

cd to the `examples/snippets` directory and run:

    uv run display-utilities-client

"""



import asyncio

import os



from mcp import ClientSession, StdioServerParameters

from mcp.client.stdio import stdio_client

from mcp.shared.metadata_utils import get_display_name



```



----------------------------------------



TITLE: Interact with MCP Simple Tool using Python Client

DESCRIPTION: An example Python script demonstrating how to use the MCP client to connect to the simple tool server (via stdio), list available tools, and call the 'fetch' tool to retrieve website content.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-tool/README.md#_snippet_1



LANGUAGE: python

CODE:

```

import asyncio

from mcp.client.session import ClientSession

from mcp.client.stdio import StdioServerParameters, stdio_client





async def main():

    async with stdio_client(

        StdioServerParameters(command="uv", args=["run", "mcp-simple-tool"])

    ) as (read, write):

        async with ClientSession(read, write) as session:

            await session.initialize()



            # List available tools

            tools = await session.list_tools()

            print(tools)



            # Call the fetch tool

            result = await session.call_tool("fetch", {"url": "https://example.com"})

            print(result)





asyncio.run(main())

```



----------------------------------------



TITLE: Start Client

DESCRIPTION: Starts the client application to test authentication with the MCP server. It automatically handles transport type configuration.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-auth/README.md#_snippet_2



LANGUAGE: bash

CODE:

```

cd examples/clients/simple-auth-client

MCP_SERVER_PORT=8001 MCP_TRANSPORT_TYPE=streamable_http uv run mcp-simple-auth-client

```



----------------------------------------



TITLE: Display Tools and Resources using Python SDK

DESCRIPTION: This snippet demonstrates initializing a client session, listing available tools with their display names and descriptions, and listing resources with their display names and URIs. It utilizes the `get_display_name` helper function to ensure the most user-friendly names are displayed.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_42



LANGUAGE: python

CODE:

```

import asyncio

import os



from fastmcp.client import ClientSession, stdio_client

from fastmcp.client.parameters import StdioServerParameters





def get_display_name(obj):

    """Get the display name for an object, prioritizing title."""

    if hasattr(obj, "title") and obj.title:

        return obj.title

    if hasattr(obj, "name") and obj.name:

        return obj.name

    return "Unnamed"





async def display_tools(session: ClientSession):

    """Display available tools with human-readable names"""

    tools_response = await session.list_tools()



    for tool in tools_response.tools:

        # get_display_name() returns the title if available, otherwise the name

        display_name = get_display_name(tool)

        print(f"Tool: {display_name}")

        if tool.description:

            print(f"   {tool.description}")





async def display_resources(session: ClientSession):

    """Display available resources with human-readable names"""

    resources_response = await session.list_resources()



    for resource in resources_response.resources:

        display_name = get_display_name(resource)

        print(f"Resource: {display_name} ({resource.uri})")



    templates_response = await session.list_resource_templates()

    for template in templates_response.resourceTemplates:

        display_name = get_display_name(template)

        print(f"Resource Template: {display_name}")





async def run():

    """Run the display utilities example."""

    # Create server parameters for stdio connection

    server_params = StdioServerParameters(

        command="uv",  # Using uv to run the server

        args=["run", "server", "fastmcp_quickstart", "stdio"],

        env={"UV_INDEX": os.environ.get("UV_INDEX", "")},

    )

    async with stdio_client(server_params) as (read, write):

        async with ClientSession(read, write) as session:

            # Initialize the connection

            await session.initialize()



            print("=== Available Tools ===")

            await display_tools(session)



            print("\n=== Available Resources ===")

            await display_resources(session)



def main():

    """Entry point for the display utilities client."""

    asyncio.run(run())





if __name__ == "__main__":

    main()



```



----------------------------------------



TITLE: MCP Client Example - Read Resource

DESCRIPTION: An example Python script using the MCP client to connect to the Simple Resource server via stdio transport, list available resources, and read a specific resource ('greeting.txt'). It utilizes asyncio for asynchronous operations and the mcp.client library.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-resource/README.md#_snippet_1



LANGUAGE: python

CODE:

```

import asyncio

from mcp.types import AnyUrl

from mcp.client.session import ClientSession

from mcp.client.stdio import StdioServerParameters, stdio_client





async def main():

    async with stdio_client(

        StdioServerParameters(command="uv", args=["run", "mcp-simple-resource"])

    ) as (read, write):

        async with ClientSession(read, write) as session:

            await session.initialize()



            # List available resources

            resources = await session.list_resources()

            print(resources)



            # Get a specific resource

            resource = await session.read_resource(AnyUrl("file:///greeting.txt"))

            print(resource)





asyncio.run(main())

```



----------------------------------------



TITLE: Run MCP Chatbot Client

DESCRIPTION: Executes the main Python script to start the MCP chatbot client.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-chatbot/README.MD#_snippet_4



LANGUAGE: bash

CODE:

```

python main.py

```



----------------------------------------



TITLE: Start MCP Server with OAuth

DESCRIPTION: Starts an MCP server with OAuth support using the 'mcp-simple-auth' application. It specifies the transport type as 'streamable-http' and the port as 3001. This is a prerequisite for running the client.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_snippet_1



LANGUAGE: bash

CODE:

```

# Example with mcp-simple-auth

cd path/to/mcp-simple-auth

uv run mcp-simple-auth --transport streamable-http --port 3001

```



----------------------------------------



TITLE: Notify Data Update and Resource Changes

DESCRIPTION: Updates a specific resource and notifies connected clients about the change. It first sends a notification for the individual resource update and then, if the change affects the overall resource list, it sends a broader notification for the resource list change. This utilizes methods from the `ctx.session` object.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_24



LANGUAGE: python

CODE:

```

import mcp

from mcp.context import Context

from mcp.url import AnyUrl



@mcp.tool()

async def notify_data_update(resource_uri: str, ctx: Context) -> str:

    """Update data and notify clients of the change."""

    # Perform data update logic here

    

    # Notify clients that this specific resource changed

    await ctx.session.send_resource_updated(AnyUrl(resource_uri))

    

    # If this affects the overall resource list, notify about that too

    await ctx.session.send_resource_list_changed()

    

    return f"Updated {resource_uri} and notified clients"

```



----------------------------------------



TITLE: OAuth 2.1 Resource Server Authentication with Python

DESCRIPTION: Demonstrates setting up an MCP server as a Resource Server (RS) implementing OAuth 2.1 authentication. It includes a `SimpleTokenVerifier` for token validation and `AuthSettings` for AS discovery and scope requirements.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_22



LANGUAGE: python

CODE:

```

"""

Run from the repository root:

    uv run examples/snippets/servers/oauth_server.py

"""



from pydantic import AnyHttpUrl



from mcp.server.auth.provider import AccessToken, TokenVerifier

from mcp.server.auth.settings import AuthSettings

from mcp.server.fastmcp import FastMCP





class SimpleTokenVerifier(TokenVerifier):

    """Simple token verifier for demonstration."""



    async def verify_token(self, token: str) -> AccessToken | None:

        pass  # This is where you would implement actual token validation





# Create FastMCP instance as a Resource Server

mcp = FastMCP(

    "Weather Service",

    # Token verifier for authentication

    token_verifier=SimpleTokenVerifier(),

    # Auth settings for RFC 9728 Protected Resource Metadata

    auth=AuthSettings(

        issuer_url=AnyHttpUrl("https://auth.example.com"),  # Authorization Server URL

        resource_server_url=AnyHttpUrl("http://localhost:3001"),  # This server's URL

        required_scopes=["user"],

    ),

)





@mcp.tool()

async def get_weather(city: str = "London") -> dict[str, str]:

    """Get weather data for a city"""

    return {

        "city": city,

        "temperature": "22",

        "condition": "Partly cloudy",

        "humidity": "65%",

    }





if __name__ == "__main__":

    mcp.run(transport="streamable-http")

```



----------------------------------------



TITLE: Structured Output Server Example

DESCRIPTION: Demonstrates how to create an MCP server that supports structured output for tools. It defines tools with input and output schemas and handles tool calls, returning validated structured data.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_37



LANGUAGE: python

CODE:

```

import asyncio

from typing import Any



import mcp.server.stdio

import mcp.types as types

from mcp.server.lowlevel import NotificationOptions, Server

from mcp.server.models import InitializationOptions



server = Server("example-server")





@server.list_tools()

async def list_tools() -> list[types.Tool]:

    """List available tools with structured output schemas."""

    return [

        types.Tool(

            name="get_weather",

            description="Get current weather for a city",

            inputSchema={

                "type": "object",

                "properties": {"city": {"type": "string", "description": "City name"}},

                "required": ["city"],

            },

            outputSchema={

                "type": "object",

                "properties": {

                    "temperature": {"type": "number", "description": "Temperature in Celsius"},

                    "condition": {"type": "string", "description": "Weather condition"},

                    "humidity": {"type": "number", "description": "Humidity percentage"},

                    "city": {"type": "string", "description": "City name"},

                },

                "required": ["temperature", "condition", "humidity", "city"],

            },

        )

    ]





@server. பயன்பாடு.call_tool()

async def call_tool(name: str, arguments: dict[str, Any]) -> dict[str, Any]:

    """Handle tool calls with structured output."""

    if name == "get_weather":

        city = arguments["city"]



        # Simulated weather data - in production, call a weather API

        weather_data = {

            "temperature": 22.5,

            "condition": "partly cloudy",

            "humidity": 65,

            "city": city,  # Include the requested city

        }



        # low-level server will validate structured output against the tool's

        # output schema, and additionally serialize it into a TextContent block

        # for backwards compatibility with pre-2025-06-18 clients.

        return weather_data

    else:

        raise ValueError(f"Unknown tool: {name}")





async def run():

    """Run the structured output server."""

    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):

        await server.run(

            read_stream,

            write_stream,

            InitializationOptions(

                server_name="structured-output-example",

                server_version="0.1.0",

                capabilities=server.get_capabilities(

                    notification_options=NotificationOptions(),

                    experimental_capabilities={},

                ),

            ),

        )





if __name__ == "__main__":

    asyncio.run(run())



```



----------------------------------------



TITLE: Low-Level Server with Lifespan Management

DESCRIPTION: Demonstrates how to create a low-level server with lifespan management. This allows for initializing resources when the server starts and cleaning them up when it stops, with access to these resources via the request context in handlers. It includes examples of listing tools and handling tool calls, accessing the lifespan context to retrieve a database connection.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_35



LANGUAGE: python

CODE:

```

import asyncio



import mcp.server.stdio

import mcp.types as types

from mcp.server.lowlevel import NotificationOptions, Server

from mcp.server.models import InitializationOptions





# Mock lifespan context for demonstration

class MockLifespanContext:

    def __init__(self):

        self.db = "mock_db_connection"



server_lifespan = MockLifespanContext()



# Create a server instance with lifespan

server = Server("example-server", lifespan=server_lifespan)





@server.list_tools()

async def handle_list_tools() -> list[types.Tool]:

    """List available tools."""

    return [

        types.Tool(

            name="query_db",

            description="Query the database",

            inputSchema={

                "type": "object",

                "properties": {"query": {"type": "string", "description": "SQL query to execute"}},

                "required": ["query"],

            },

        )

    ]





@server.call_tool()

async def query_db(name: str, arguments: dict) -> list[types.TextContent]:

    """Handle database query tool call."""

    if name != "query_db":

        raise ValueError(f"Unknown tool: {name}")



    # Access lifespan context

    ctx = server.request_context

    db = ctx.lifespan_context["db"]



    # Execute query

    # In a real scenario, you would use the 'db' object here

    results = f"Results for query: {arguments['query']}"



    return [types.TextContent(type="text", text=f"Query results: {results}")]





async def run():

    """Run the server with lifespan management."""

    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):

        await server.run(

            read_stream,

            write_stream,

            InitializationOptions(

                server_name="example-server",

                server_version="0.1.0",

                capabilities=server.get_capabilities(

                    notification_options=NotificationOptions(),

                    experimental_capabilities={},

                ),

            ),

        )





if __name__ == "__main__":

    import asyncio



    asyncio.run(run())



```



----------------------------------------



TITLE: Structured Output with Pydantic, TypedDict, and Dict

DESCRIPTION: Illustrates how tools can return structured data using Pydantic models, TypedDicts, and dictionaries. The SDK automatically serializes these types into structured results, with primitive types wrapped in a 'result' field.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_10



LANGUAGE: python

CODE:

```

from typing import TypedDict



from pydantic import BaseModel, Field



from mcp.server.fastmcp import FastMCP



mcp = FastMCP("Structured Output Example")





# Using Pydantic models for rich structured data

class WeatherData(BaseModel):

    """Weather information structure."""



    temperature: float = Field(description="Temperature in Celsius")

    humidity: float = Field(description="Humidity percentage")

    condition: str

    wind_speed: float





@mcp.tool()

def get_weather(city: str) -> WeatherData:

    """Get weather for a city - returns structured data."""

    # Simulated weather data

    return WeatherData(

        temperature=72.5,

        humidity=45.0,

        condition="sunny",

        wind_speed=5.2,

    )





# Using TypedDict for simpler structures

class LocationInfo(TypedDict):

    latitude: float

    longitude: float

    name: str





@mcp.tool()

def get_location(address: str) -> LocationInfo:

    """Get location coordinates"""

    return LocationInfo(latitude=51.5074, longitude=-0.1278, name="London, UK")





# Using dict[str, Any] for flexible schemas

@mcp.tool()

def get_statistics(data_type: str) -> dict[str, float]:

    """Get various statistics"""

    return {"mean": 42.5, "median": 40.0, "std_dev": 5.2}

```



----------------------------------------



TITLE: Context Object Properties and Methods

DESCRIPTION: Details the properties and asynchronous methods available on the Context object for server-side operations. This includes accessing request information, interacting with the FastMCP server, managing sessions, and performing various logging and progress reporting actions.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_15



LANGUAGE: APIDOC

CODE:

```

Context Object:

  Properties:

    - request_id: Unique ID for the current request

    - client_id: Client ID if available

    - fastmcp: Access to the FastMCP server instance

    - session: Access to the underlying session for advanced communication

    - request_context: Access to request-specific data and lifespan resources

  Methods:

    - debug(message: str): Send debug log message

    - info(message: str): Send info log message

    - warning(message: str): Send warning log message

    - error(message: str): Send error log message

    - log(level: str, message: str, logger_name: str = None): Send log with custom level

    - report_progress(progress: float, total: float = None, message: str = None): Report operation progress

    - read_resource(uri: str): Read a resource by URI

    - elicit(message: str, schema: dict): Request additional information from user with validation

```



----------------------------------------



TITLE: CI Failure Resolution Order

DESCRIPTION: The recommended order for fixing Continuous Integration (CI) failures, prioritizing formatting, then type errors, and finally linting. It also provides guidance on resolving type errors.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/CLAUDE.md#_snippet_5



LANGUAGE: bash

CODE:

```

# Fix order:

# 1. Formatting

# 2. Type errors

# 3. Linting

```



----------------------------------------



TITLE: Basic Low-Level Server for Prompt Handling

DESCRIPTION: Illustrates a basic low-level server setup for handling prompts. This example shows how to define handlers for listing prompts and retrieving specific prompt details, including arguments. It demonstrates the server's ability to manage prompt-related interactions.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_36



LANGUAGE: python

CODE:

```

import asyncio



import mcp.server.stdio

import mcp.types as types

from mcp.server.lowlevel import NotificationOptions, Server

from mcp.server.models import InitializationOptions



# Create a server instance

server = Server("example-server")





@server.list_prompts()

async def handle_list_prompts() -> list[types.Prompt]:

    """List available prompts."""

    return [

        types.Prompt(

            name="example-prompt",

            description="An example prompt template",

            arguments=[types.PromptArgument(name="arg1", description="Example argument", required=True)],

        )

    ]





@server.get_prompt()

async def handle_get_prompt(name: str, arguments: dict[str, str] | None) -> types.GetPromptResult:

    """Get a specific prompt by name."""

    if name != "example-prompt":

        raise ValueError(f"Unknown prompt: {name}")



    arg1_value = (arguments or {}).get("arg1", "default")



    return types.GetPromptResult(

        description="Example prompt",

        messages=[

            types.PromptMessage(

                role="user",

                content=types.TextContent(type="text", text=f"Example prompt text with argument: {arg1_value}"),

            )

        ],

    )





async def run():

    """Run the basic low-level server."""

    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):

        await server.run(

            read_stream,

            write_stream,

            InitializationOptions(

                server_name="example",

                server_version="0.1.0",

                capabilities=server.get_capabilities(

                    notification_options=NotificationOptions(),

                    experimental_capabilities={},

                ),

            ),

        )





if __name__ == "__main__":

    asyncio.run(run())



```



----------------------------------------



TITLE: LLMClient Configuration Note

DESCRIPTION: A note regarding the configuration of the LLMClient, specifically mentioning the default Groq API endpoint and model, and how to change it for other providers.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-chatbot/README.MD#_snippet_5



LANGUAGE: plaintext

CODE:

```

Note: The current implementation is configured to use the Groq API endpoint (https://api.groq.com/openai/v1/chat/completions) with the llama-3.2-90b-vision-preview model. If you plan to use a different LLM provider, you'll need to modify the LLMClient class in main.py to use the appropriate endpoint URL and model parameters.

```



----------------------------------------



TITLE: Python SDK Development Best Practices

DESCRIPTION: A summary of best practices for Python SDK development, including version control, code quality checks, and documentation.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/CLAUDE.md#_snippet_8



LANGUAGE: APIDOC

CODE:

```

Python SDK Development Best Practices:



1.  **Version Control**: Always check `git status` before committing. Keep changes minimal.

2.  **Code Quality**: Ensure type hints for all code. Public APIs must have docstrings. Functions should be focused and small. Adhere strictly to existing patterns. Maintain a maximum line length of 120 characters.

3.  **Tooling Workflow**: Run formatters before type checks. Use `uv` for package management. Utilize Ruff for formatting and linting, and Pyright for type checking.

4.  **Testing**: Test edge cases and errors. New features require tests, and bug fixes require regression tests. Use anyIO for async testing.

5.  **Documentation**: Document public APIs thoroughly. Create detailed pull request messages focusing on the problem and solution.

6.  **Collaboration**: Always add specified reviewers to pull requests. Never mention `co-authored-by` or commit message generation tools.

```



----------------------------------------



TITLE: Query with Typed Lifespan Context

DESCRIPTION: Executes a query against a database using shared resources obtained from the server's lifespan context. This example demonstrates type-safe access to application-specific resources like database connections and configuration objects, which are made available through `ctx.request_context.lifespan_context`.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_25



LANGUAGE: python

CODE:

```

import mcp

from mcp.context import Context

from dataclasses import dataclass



# Assume Database and AppConfig are defined elsewhere

class Database:

    def execute(self, query: str, timeout: int):

        pass



class AppConfig:

    query_timeout: int



@dataclass

class AppContext:

    db: Database

    config: AppConfig



@mcp.tool()

def query_with_config(query: str, ctx: Context) -> str:

    """Execute a query using shared database and configuration."""

    # Access typed lifespan context

    app_ctx: AppContext = ctx.request_context.lifespan_context

    

    # Use shared resources

    connection = app_ctx.db

    settings = app_ctx.config

    

    # Execute query with configuration

    result = connection.execute(query, timeout=settings.query_timeout)

    return str(result)

```



----------------------------------------



TITLE: Pre-commit Hooks Configuration

DESCRIPTION: Details on the pre-commit configuration, including the tools it runs (Prettier, Ruff) and the process for updating Ruff configurations.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/CLAUDE.md#_snippet_4



LANGUAGE: yaml

CODE:

```

# .pre-commit-config.yaml

repos:

  - repo: https://github.com/pre-commit/pre-commit-hooks

    rev: v4.5.0

    hooks:

      - id: trailing-whitespace

      - id: end-of-file-fixer

      - id: check-yaml

      - id: check-json

  - repo: https://github.com/astral-sh/ruff-pre-commit

    rev: v0.3.0

    hooks:

      - id: ruff

        args: [--fix, --exit-non-zero-same-as-zero]

      - id: ruff-format

```



----------------------------------------



TITLE: Server Configuration with Environment Variables

DESCRIPTION: An example of configuring MCP servers with environment variables, allowing for dynamic credential management.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-chatbot/README.MD#_snippet_3



LANGUAGE: json

CODE:

```

{

  "mcpServers": {

    "server_name": {

      "command": "uvx",

      "args": ["mcp-server-name", "--additional-args"],

      "env": {

        "API_KEY": "your_api_key_here"

      }

    }

  }

}

```



----------------------------------------



TITLE: Server Lifespan Management

DESCRIPTION: Demonstrates how to manage the application lifecycle (startup and shutdown) for an MCP server using an async context manager. It includes a mock database class with connect and disconnect methods, and a typed application context to hold dependencies.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_6



LANGUAGE: python

CODE:

```

from collections.abc import AsyncIterator

from contextlib import asynccontextmanager

from dataclasses import dataclass



from mcp.server.fastmcp import Context, FastMCP





# Mock database class for example

class Database:

    """Mock database class for example."""



    @classmethod

    async def connect(cls) -> "Database":

        """Connect to database."""

        return cls()



    async def disconnect(self) -> None:

        """Disconnect from database."""

        pass



    def query(self) -> str:

        """Execute a query."""

        return "Query result"





@dataclass

class AppContext:

    """Application context with typed dependencies."""



    db: Database





@asynccontextmanager

async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:

    """Manage application lifecycle with type-safe context."""

    # Initialize on startup

    db = await Database.connect()

    try:

        yield AppContext(db=db)

    finally:

        # Cleanup on shutdown

        await db.disconnect()





# Pass lifespan to server

mcp = FastMCP("My App", lifespan=app_lifespan)





# Access type-safe lifespan context in tools

@mcp.tool()

def query_db(ctx: Context) -> str:

    """Tool that uses initialized resources."""

    db = ctx.request_context.lifespan_context.db

    return db.query()

```



----------------------------------------



TITLE: Tool Progress Reporting

DESCRIPTION: Demonstrates how to use the Context object to report progress during a long-running task. This example shows sending informational messages, debug logs, and progress updates to the server.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_16



LANGUAGE: python

CODE:

```

from mcp.server.fastmcp import Context, FastMCP



mcp = FastMCP(name="Progress Example")





@mcp.tool()

async def long_running_task(task_name: str, ctx: Context, steps: int = 5) -> str:

    """Execute a task with progress updates."""

    await ctx.info(f"Starting: {task_name}")



    for i in range(steps):

        progress = (i + 1) / steps

        await ctx.report_progress(

            progress=progress,

            total=1.0,

            message=f"Step {i + 1}/{steps}",

        )

        await ctx.debug(f"Completed step {i + 1}")



    return f"Task '{task_name}' completed"

```



----------------------------------------



TITLE: Tools with Context and Progress Reporting

DESCRIPTION: Demonstrates how to create tools that accept a Context object, allowing them to interact with MCP capabilities such as logging information and reporting progress during long-running tasks.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_9



LANGUAGE: python

CODE:

```

from mcp.server.fastmcp import Context, FastMCP



mcp = FastMCP(name="Progress Example")





@mcp.tool()

async def long_running_task(task_name: str, ctx: Context, steps: int = 5) -> str:

    """Execute a task with progress updates."""

    await ctx.info(f"Starting: {task_name}")



    for i in range(steps):

        progress = (i + 1) / steps

        await ctx.report_progress(

            progress=progress,

            total=1.0,

            message=f"Step {i + 1}/{steps}",

        )

        await ctx.debug(f"Completed step {i + 1}")



    return f"Task '{task_name}' completed"

```



----------------------------------------



TITLE: FastMCP Quickstart Server

DESCRIPTION: A basic FastMCP server example demonstrating how to define tools and resources. It includes an 'add' tool for summing two integers and a 'greeting' resource for personalized greetings. This snippet is intended to be run from the `examples/snippets/clients` directory.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_3



LANGUAGE: python

CODE:

```

"""

FastMCP quickstart example.



cd to the `examples/snippets/clients` directory and run:

    uv run server fastmcp_quickstart stdio

"""



from mcp.server.fastmcp import FastMCP



# Create an MCP server

mcp = FastMCP("Demo")





# Add an addition tool

@mcp.tool()

def add(a: int, b: int) -> int:

    """Add two numbers"""

    return a + b





# Add a dynamic greeting resource

@mcp.resource("greeting://{name}")

def get_greeting(name: str) -> str:

    """Get a personalized greeting"""

    return f"Hello, {name}!"



```



----------------------------------------



TITLE: Structured Output with Type Hints

DESCRIPTION: Demonstrates how to use Python classes with type hints to define structured data for tools. The SDK automatically generates schemas for these types, enabling structured input and output. Untyped classes or functions returning unstructured data do not generate schemas.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_11



LANGUAGE: python

CODE:

```

from mcp.server.fastmcp import FastMCP





class UserProfile:

    name: str

    age: int

    email: str | None = None



    def __init__(self, name: str, age: int, email: str | None = None):

        self.name = name

        self.age = age

        self.email = email





@mcp.tool()

def get_user(user_id: str) -> UserProfile:

    """Get user profile - returns structured data"""

    return UserProfile(name="Alice", age=30, email="<EMAIL>")





class UntypedConfig:

    def __init__(self, setting1, setting2):

        self.setting1 = setting1

        self.setting2 = setting2





@mcp.tool()

def get_config() -> UntypedConfig:

    """This returns unstructured output - no schema generated"""

    return UntypedConfig("value1", "value2")





@mcp.tool()

def list_cities() -> list[str]:

    """Get a list of cities"""

    return ["London", "Paris", "Tokyo"]

    # Returns: {"result": ["London", "Paris", "Tokyo"]}





@mcp.tool()

def get_temperature(city: str) -> float:

    """Get temperature as a simple float"""

    return 22.5

    # Returns: {"result": 22.5}

```



----------------------------------------



TITLE: Elicitation Example for Tool Calls

DESCRIPTION: Demonstrates how to use the `elicit` method within a tool to ask users for additional information or alternatives when a direct action is not possible.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_19



LANGUAGE: python

CODE:

```

from pydantic import BaseModel, Field



from mcp.server.fastmcp import Context, FastMCP



mcp = FastMCP(name="Elicitation Example")





class BookingPreferences(BaseModel):

    """Schema for collecting user preferences."""



    checkAlternative: bool = Field(description="Would you like to check another date?")

    alternativeDate: str = Field(

        default="2024-12-26",

        description="Alternative date (YYYY-MM-DD)",

    )





@mcp.tool()

async def book_table(

    date: str,

    time: str,

    party_size: int,

    ctx: Context,

) -> str:

    """Book a table with date availability check."""

    # Check if date is available

    if date == "2024-12-25":

        # Date unavailable - ask user for alternative

        result = await ctx.elicit(

            message=(f"No tables available for {party_size} on {date}. Would you like to try another date?"),

            schema=BookingPreferences,

        )



        if result.action == "accept" and result.data:

            if result.data.checkAlternative:

                return f"[SUCCESS] Booked for {result.data.alternativeDate}"

            return "[CANCELLED] No booking made"

        return "[CANCELLED] Booking cancelled"



    # Date available

    return f"[SUCCESS] Booked for {date} at {time}"



```



----------------------------------------



TITLE: Low-Level Server Lifespan Management

DESCRIPTION: Demonstrates the advanced usage of the low-level MCP server, focusing on managing the server's lifecycle using an async context manager. This allows for resource initialization (like database connections) on startup and cleanup on shutdown.



SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_snippet_34



LANGUAGE: python

CODE:

```

"""

Run from the repository root:

    uv run examples/snippets/servers/lowlevel/lifespan.py

"""



from collections.abc import AsyncIterator

from contextlib import asynccontextmanager



import mcp.server.stdio

import mcp.types as types

from mcp.server.lowlevel import NotificationOptions, Server

from mcp.server.models import InitializationOptions





# Mock database class for example

class Database:

    """Mock database class for example."""



    @classmethod

    async def connect(cls) -> "Database":

        """Connect to database."""

        print("Database connected")

        return cls()



    async def disconnect(self) -> None:

        """Disconnect from database."""

        print("Database disconnected")



    async def query(self, query_str: str) -> list[dict[str, str]]:

        """Execute a query."""

        # Simulate database query

        return [{"id": "1", "name": "Example", "query": query_str}]





@asynccontextmanager

async def server_lifespan(_server: Server) -> AsyncIterator[dict]:

    """Manage server startup and shutdown lifecycle."""

    # Initialize resources on startup

    db = await Database.connect()

    try:

        yield {"db": db}

    finally:

        # Clean up on shutdown

        await db.disconnect()



```



========================

QUESTIONS AND ANSWERS

========================

TOPIC: MCP Simple StreamableHttp Stateless Server Example

Q: How can a client connect to the MCP Simple StreamableHttp Stateless Server?

A: Clients can connect to this server using an HTTP client. Currently, streamable HTTP client examples are available in the TypeScript SDK, or you can use the Inspector tool for testing purposes.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-streamablehttp-stateless/README.md#_qa_4



----------------------------------------



TOPIC: MCP Simple StreamableHttp Server Example

Q: What client options are available for connecting to the MCP Simple StreamableHttp Server Example?

A: You can connect to the server using an HTTP client. Currently, Typescript SDK has streamable HTTP client examples, or you can use the Inspector tool.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-streamablehttp/README.md#_qa_13



----------------------------------------



TOPIC: MCP Python SDK Documentation

Q: What is the purpose of the MCP Python SDK?

A: The MCP Python SDK implements the full Model Context Protocol (MCP) specification. It enables developers to build MCP clients that connect to any MCP server and create MCP servers that expose resources, prompts, and tools using standard transports.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/README.md#_qa_0



----------------------------------------



TOPIC: MCP Simple Chatbot Example

Q: What are the main components of the MCP Simple Chatbot's class structure?

A: The class structure includes Configuration for managing settings, Server for handling MCP server initialization and tool discovery, Tool for representing individual tools, LLMClient for LLM communication, and ChatSession for orchestrating the overall interaction.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-chatbot/README.MD#_qa_9



----------------------------------------



TOPIC: MCP OAuth Authentication Demo

Q: What are the key differences between the new MCP architecture and the legacy MCP server acting as an Authorization Server?

A: In the new architecture, MCP servers are Resource Servers only, with separate Authorization Servers. The legacy server acts as both, validates tokens internally, does not support RFC 9728 discovery, and provides OAuth metadata directly at its URL.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/servers/simple-auth/README.md#_qa_5



----------------------------------------



TOPIC: Simple Auth Client Example - MCP Python SDK

Q: How can the transport type be specified for the MCP Python SDK client?

A: The transport type for the MCP Python SDK client can be specified using the `MCP_TRANSPORT_TYPE` environment variable, with options for `streamable_http` (default) or `sse`.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_qa_4



----------------------------------------



TOPIC: MCP Simple Chatbot Example

Q: How does the MCP Simple Chatbot handle the integration of multiple servers?

A: The chatbot uses a `servers_config.json` file, which follows a structure similar to Claude Desktop, to configure and integrate multiple MCP servers. This allows for easy addition of various server implementations.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-chatbot/README.MD#_qa_4



----------------------------------------



TOPIC: Simple Auth Client Example - MCP Python SDK

Q: What are the interactive commands available in the MCP Python SDK client?

A: The interactive commands available in the MCP Python SDK client are `list` to list available tools, `call <tool_name> [args]` to call a tool with optional JSON arguments, and `quit` to exit.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_qa_2



----------------------------------------



TOPIC: Simple Auth Client Example - MCP Python SDK

Q: What authentication method does the MCP Python SDK example support?

A: The MCP Python SDK example supports OAuth 2.0 authentication with PKCE.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_qa_0



----------------------------------------



TOPIC: Simple Auth Client Example - MCP Python SDK

Q: How can the MCP server URL be configured for the MCP Python SDK client?

A: The MCP server URL can be configured using the `MCP_SERVER_PORT` environment variable, which defaults to port 8000.





SOURCE: https://github.com/modelcontextprotocol/python-sdk/blob/main/examples/clients/simple-auth-client/README.md#_qa_3





