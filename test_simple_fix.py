#!/usr/bin/env python3
"""
简单测试JSON格式修复
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from core.memory_manager import GraphitiMemoryManager
from core.llm_client import LLMManager
from core.graphiti_embedder import GraphitiEmbedder
from core.embedding_client import SentenceTransformerEmbeddingClient

async def test_json_fix():
    """测试JSON格式修复"""
    print("=== 测试JSON格式精确匹配修复 ===")
    
    # 创建必要的客户端
    llm_manager = LLMManager()
    base_embedding_client = SentenceTransformerEmbeddingClient()
    embedding_client = GraphitiEmbedder(base_embedding_client)
    
    memory_manager = GraphitiMemoryManager(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="00000000",
        llm_manager=llm_manager,
        embedding_client=embedding_client
    )
    
    await memory_manager.initialize()
    
    try:
        # 测试简单实体提取
        test_cases = [
            "张三是一名软件工程师，他在腾讯公司工作。",
            "北京是中国的首都，人口超过2000万。",
            "苹果公司由史蒂夫·乔布斯创立，生产iPhone手机。"
        ]
        
        for i, content in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {content}")
            
            result = await memory_manager.add_memory(
                user_id=f"test_user_{i}",
                content=content,
                memory_type="fact"
            )
            print(f"添加结果: {result}")
        
        # 检查数据库状态
        status = await memory_manager.get_status()
        print(f"\n最终状态: {status}")
        
        # 搜索实体
        entities = await memory_manager.search_nodes("张三")
        print(f"搜索张三的实体: {len(entities)} 个")
        for entity in entities:
            print(f"  - {entity.get('name', 'Unknown')}: {entity.get('summary', 'No summary')}")
        
        # 搜索事实
        facts = await memory_manager.search_facts("腾讯")
        print(f"搜索腾讯的事实: {len(facts)} 个")
        for fact in facts:
            print(f"  - {fact.get('fact', 'No fact')}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理资源
        if hasattr(memory_manager, 'cleanup'):
            await memory_manager.cleanup()
        elif hasattr(memory_manager, 'close'):
            await memory_manager.close()

if __name__ == "__main__":
    asyncio.run(test_json_fix())