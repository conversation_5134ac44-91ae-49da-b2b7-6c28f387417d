#!/usr/bin/env python3
"""
测试 GraphitiLLM 的结构化输出功能
验证 Pydantic 模型和 JSON schema 是否工作
"""
import asyncio
import json
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import List, Optional

async def test_structured_output():
    """测试结构化输出功能"""
    print("🧪 测试 GraphitiLLM 结构化输出功能")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 步骤1: 初始化 LLM 组件
        print("📋 步骤1: 初始化 LLM 组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.graphiti_llm import GraphitiLLM
        from graphiti_core.prompts.models import Message
        
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.1,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        graphiti_llm = GraphitiLLM(llm_manager)
        print("✅ LLM 组件初始化完成")
        
        # 步骤2: 定义测试用的 Pydantic 模型
        print("\n📋 步骤2: 定义测试 Pydantic 模型...")
        
        class Entity(BaseModel):
            name: str
            type: str
        
        class Relationship(BaseModel):
            source: str
            target: str
            relation: str
        
        class ExtractionResult(BaseModel):
            entities: List[Entity]
            relationships: List[Relationship]
        
        print("✅ Pydantic 模型定义完成")
        
        # 步骤3: 测试结构化输出
        print("\n📋 步骤3: 测试结构化输出...")
        
        test_text = "苹果公司的CEO蒂姆·库克宣布了新的财务策略"
        messages = [Message(role="user", content=f"从以下文本中提取实体和关系：{test_text}")]
        
        try:
            result = await graphiti_llm.generate_response(
                messages=messages,
                response_model=ExtractionResult,
                max_tokens=1000
            )
            
            print(f"  响应类型: {type(result)}")
            print(f"  响应内容: {result.get('content', 'N/A')[:200]}...")
            
            # 尝试解析 JSON
            content = result.get('content', '{}')
            try:
                parsed_result = json.loads(content)
                print(f"  ✅ JSON 解析成功")
                print(f"  实体数量: {len(parsed_result.get('entities', []))}")
                print(f"  关系数量: {len(parsed_result.get('relationships', []))}")
                
                # 验证 Pydantic 模型
                try:
                    extraction = ExtractionResult(**parsed_result)
                    print(f"  ✅ Pydantic 验证成功")
                    print(f"  提取的实体:")
                    for entity in extraction.entities:
                        print(f"    - {entity.name} ({entity.type})")
                    print(f"  提取的关系:")
                    for rel in extraction.relationships:
                        print(f"    - {rel.source} -> {rel.target} ({rel.relation})")
                    
                    return True
                    
                except Exception as pydantic_error:
                    print(f"  ❌ Pydantic 验证失败: {pydantic_error}")
                    return False
                    
            except json.JSONDecodeError as json_error:
                print(f"  ❌ JSON 解析失败: {json_error}")
                print(f"  原始内容: {content}")
                return False
                
        except Exception as e:
            print(f"  ❌ 结构化输出测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 清理
        await llm_manager.cleanup()
        
    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_graphiti_with_fixed_llm():
    """使用修复后的 LLM 测试 Graphiti"""
    print("\n" + "=" * 60)
    print("🔧 使用修复后的 LLM 测试 Graphiti")
    print("=" * 60)
    
    load_dotenv()
    
    try:
        # 初始化组件
        print("📋 初始化组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.graphiti_llm import GraphitiLLM
        from core.graphiti_embedder import GraphitiEmbedder
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # LLM
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.1,  # 更低的温度以获得更一致的结构化输出
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入
        embedding_manager = EmbeddingManager()
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general"
        )
        await embedding_manager.add_client("chinese", chinese_client)
        
        # Graphiti 包装器
        graphiti_llm = GraphitiLLM(llm_manager)
        primary_embedder = embedding_manager.get_primary_client()
        graphiti_embedder = GraphitiEmbedder(primary_embedder)
        
        print("✅ 组件初始化完成")
        
        # 初始化 Graphiti
        print("\n📋 初始化 Graphiti...")
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j", 
            password="password",
            llm_client=graphiti_llm,
            embedder=graphiti_embedder,
            store_raw_episode_content=True
        )
        
        await client.build_indices_and_constraints()
        print("✅ Graphiti 初始化成功")
        
        # 添加简单测试 Episode
        print("\n📋 添加测试 Episode...")
        
        test_episode = "微软公司和谷歌公司在云计算领域展开激烈竞争"
        
        try:
            await client.add_episode(
                name="竞争关系测试",
                episode_body=test_episode,
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="公司竞争关系测试"
            )
            print("✅ Episode 添加成功")
        except Exception as e:
            print(f"❌ Episode 添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 检查数据库状态
        print("\n📋 检查数据库状态...")
        
        from neo4j import AsyncGraphDatabase
        driver = AsyncGraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'password'))
        
        async with driver.session() as session:
            # 检查 Entity 节点
            result = await session.run('MATCH (n:Entity) RETURN count(n) as entity_count')
            record = await result.single()
            entity_count = record['entity_count'] if record else 0
            print(f"  Entity 节点数量: {entity_count}")
            
            if entity_count > 0:
                # 查看实体详情
                result = await session.run('MATCH (n:Entity) RETURN n.name as name, keys(n) as properties LIMIT 5')
                records = await result.data()
                print("  发现的实体:")
                for record in records:
                    print(f"    - {record['name']} (属性: {record['properties']})")
                
                # 检查关系
                result = await session.run('MATCH ()-[r:RELATES_TO]->() RETURN count(r) as rel_count')
                record = await result.single()
                rel_count = record['rel_count'] if record else 0
                print(f"  关系数量: {rel_count}")
                
                if rel_count > 0:
                    result = await session.run('MATCH (a)-[r:RELATES_TO]->(b) RETURN a.name as source, b.name as target, r.name as relation LIMIT 3')
                    records = await result.data()
                    print("  发现的关系:")
                    for record in records:
                        print(f"    - {record['source']} -> {record['target']} ({record['relation']})")
                
                print("🎉 实体提取和关系建立成功!")
                return True
            else:
                print("❌ 没有实体被提取")
                return False
        
        await driver.close()
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        
    except Exception as e:
        print(f"❌ Graphiti 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧪 GraphitiLLM 结构化输出测试")
    print("🎯 验证 Pydantic 模型和实体提取功能")
    print("=" * 70)
    
    # 测试1: 结构化输出功能
    structured_test = await test_structured_output()
    
    # 测试2: Graphiti 集成测试
    if structured_test:
        print(f"\n✅ 结构化输出测试通过，继续 Graphiti 集成测试...")
        graphiti_test = await test_simple_graphiti_with_fixed_llm()
    else:
        print(f"\n❌ 结构化输出测试失败，跳过 Graphiti 测试")
        graphiti_test = False
    
    # 结果总结
    print("\n" + "=" * 70)
    print("📊 测试结果总结:")
    print(f"  结构化输出测试: {'✅ 通过' if structured_test else '❌ 失败'}")
    print(f"  Graphiti 集成测试: {'✅ 通过' if graphiti_test else '❌ 失败'}")
    
    if structured_test and graphiti_test:
        print("\n🎉 所有测试通过！实体关系连接功能已修复")
    elif structured_test:
        print("\n⚠️ 结构化输出正常，但 Graphiti 集成仍有问题")
    else:
        print("\n❌ 结构化输出存在问题，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())