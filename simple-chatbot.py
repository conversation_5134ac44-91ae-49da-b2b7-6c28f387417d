import asyncio
import json
import logging
import os
import shutil
from contextlib import AsyncExitStack
from typing import Any

import httpx
from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from mcp.client.streamable_http import streamablehttp_client

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


class Configuration:
    """管理MCP客户端的配置和环境变量"""

    def __init__(self) -> None:
        """使用环境变量初始化配置"""
        self.load_env()
        self.api_key = os.getenv("LLM_API_KEY")

    @staticmethod
    def load_env() -> None:
        """从.env文件加载环境变量"""
        load_dotenv()

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """从JSON文件加载服务器配置
        
        Args:
            file_path: JSON配置文件路径
            
        Returns:
            包含服务器配置的字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            JSONDecodeError: 配置文件不是有效的JSON
        """
        with open(file_path, "r", encoding='utf-8') as f:
            return json.load(f)

    @property
    def llm_api_key(self) -> str:
        """获取LLM API密钥
        
        Returns:
            API密钥字符串
            
        Raises:
            ValueError: 环境变量中未找到API密钥
        """
        if not self.api_key:
            raise ValueError("环境变量中未找到LLM_API_KEY")
        return self.api_key


class Server:
    """管理MCP服务器连接和工具执行"""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()
        self.connection_type: str = config.get("type", "stdio")  # stdio, http, sse

    async def initialize(self) -> None:
        """初始化服务器连接"""
        logging.info(f"正在初始化服务器 {self.name}，连接类型: {self.connection_type}")
        
        try:
            if self.connection_type == "stdio":
                await self._initialize_stdio()
            elif self.connection_type == "http":
                await self._initialize_http()
            elif self.connection_type == "sse":
                await self._initialize_sse()
            else:
                raise ValueError(f"不支持的连接类型: {self.connection_type}")
                
            logging.info(f"服务器 {self.name} 初始化成功")
        except Exception as e:
            logging.error(f"初始化服务器 {self.name} 时出错: {e}")
            await self.cleanup()
            raise

    async def _initialize_stdio(self) -> None:
        """初始化STDIO连接"""
        command = shutil.which("npx") if self.config["command"] == "npx" else self.config["command"]
        if command is None:
            raise ValueError("命令必须是有效字符串且不能为None")

        server_params = StdioServerParameters(
            command=command,
            args=self.config["args"],
            env={**os.environ, **self.config["env"]} if self.config.get("env") else None,
        )
        
        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        read, write = stdio_transport
        session = await self.exit_stack.enter_async_context(ClientSession(read, write))
        await session.initialize()
        self.session = session

    async def _initialize_http(self) -> None:
        """初始化HTTP连接"""
        server_url = self.config.get("url", f"http://localhost:{self.config.get('port', 8000)}/mcp")
        timeout = self.config.get("timeout", 60)
        
        http_transport = await self.exit_stack.enter_async_context(
            streamablehttp_client(url=server_url, timeout=timeout)
        )
        read_stream, write_stream, get_session_id = http_transport
        session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
        await session.initialize()
        self.session = session

    async def _initialize_sse(self) -> None:
        """初始化SSE连接"""
        server_url = self.config.get("url", f"http://localhost:{self.config.get('port', 8000)}/sse")
        timeout = self.config.get("timeout", 60)
        
        sse_transport = await self.exit_stack.enter_async_context(
            sse_client(url=server_url, timeout=timeout)
        )
        read_stream, write_stream = sse_transport
        session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
        await session.initialize()
        self.session = session

    async def list_tools(self) -> list[Any]:
        """列出服务器可用的工具
        
        Returns:
            可用工具列表
            
        Raises:
            RuntimeError: 服务器未初始化
        """
        if not self.session:
            raise RuntimeError(f"服务器 {self.name} 未初始化")

        tools_response = await self.session.list_tools()
        tools = []

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools.extend(Tool(tool.name, tool.description, tool.inputSchema, tool.title) for tool in item[1])

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """执行工具，包含重试机制
        
        Args:
            tool_name: 要执行的工具名称
            arguments: 工具参数
            retries: 重试次数
            delay: 重试间隔时间（秒）
            
        Returns:
            工具执行结果
            
        Raises:
            RuntimeError: 服务器未初始化
            Exception: 重试所有次数后工具执行仍然失败
        """
        if not self.session:
            raise RuntimeError(f"服务器 {self.name} 未初始化")

        attempt = 0
        while attempt < retries:
            try:
                logging.info(f"正在执行工具 {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments)
                return result

            except Exception as e:
                attempt += 1
                logging.warning(f"执行工具时出错: {e}. 尝试 {attempt}/{retries}")
                if attempt < retries:
                    logging.info(f"{delay}秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    logging.error("已达到最大重试次数，执行失败")
                    raise

    async def cleanup(self) -> None:
        """清理服务器资源"""
        async with self._cleanup_lock:
            try:
                await self.exit_stack.aclose()
                self.session = None
            except Exception as e:
                logging.error(f"清理服务器 {self.name} 时出错: {e}")


class Tool:
    """表示工具及其属性和格式"""

    def __init__(
        self,
        name: str,
        description: str,
        input_schema: dict[str, Any],
        title: str | None = None,
    ) -> None:
        self.name: str = name
        self.title: str | None = title
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """为LLM格式化工具信息
        
        Returns:
            描述工具的格式化字符串
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = f"- {param_name}: {param_info.get('description', '无描述')}"
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (必需)"
                args_desc.append(arg_desc)

        # 构建格式化输出
        output = f"工具: {self.name}\n"

        # 如果有可读标题则添加
        if self.title:
            output += f"可读标题: {self.title}\n"

        output += f"""描述: {self.description}
参数:
{chr(10).join(args_desc)}
"""

        return output


class LLMClient:
    """管理与LLM提供商的通信"""

    def __init__(self, api_key: str) -> None:
        self.api_key: str = api_key

    def get_response(self, messages: list[dict[str, str]]) -> str:
        """从LLM获取响应
        
        Args:
            messages: 消息字典列表
            
        Returns:
            LLM的响应字符串
            
        Raises:
            httpx.RequestError: LLM请求失败
        """
        url = "https://api.groq.com/openai/v1/chat/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }
        payload = {
            "messages": messages,
            "model": "meta-llama/llama-4-scout-17b-16e-instruct",
            "temperature": 0.7,
            "max_tokens": 4096,
            "top_p": 1,
            "stream": False,
            "stop": None,
        }

        try:
            with httpx.Client() as client:
                response = client.post(url, headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()
                return data["choices"][0]["message"]["content"]

        except httpx.RequestError as e:
            error_message = f"获取LLM响应时出错: {str(e)}"
            logging.error(error_message)

            if isinstance(e, httpx.HTTPStatusError):
                status_code = e.response.status_code
                logging.error(f"状态码: {status_code}")
                logging.error(f"响应详情: {e.response.text}")

            return f"我遇到了一个错误: {error_message}。请重试或重新表述您的请求。"


class ChatSession:
    """协调用户、LLM和工具之间的交互"""

    def __init__(self, servers: list[Server], llm_client: LLMClient) -> None:
        self.servers: list[Server] = servers
        self.llm_client: LLMClient = llm_client

    async def cleanup_servers(self) -> None:
        """正确清理所有服务器"""
        for server in reversed(self.servers):
            try:
                await server.cleanup()
            except Exception as e:
                logging.warning(f"最终清理时的警告: {e}")

    async def process_llm_response(self, llm_response: str) -> str:
        """处理LLM响应并在需要时执行工具
        
        Args:
            llm_response: 来自LLM的响应
            
        Returns:
            工具执行结果或原始响应
        """
        import json

        try:
            tool_call = json.loads(llm_response)
            if "tool" in tool_call and "arguments" in tool_call:
                logging.info(f"执行工具: {tool_call['tool']}")
                logging.info(f"参数: {tool_call['arguments']}")

                for server in self.servers:
                    tools = await server.list_tools()
                    if any(tool.name == tool_call["tool"] for tool in tools):
                        try:
                            result = await server.execute_tool(tool_call["tool"], tool_call["arguments"])

                            if isinstance(result, dict) and "progress" in result:
                                progress = result["progress"]
                                total = result["total"]
                                percentage = (progress / total) * 100
                                logging.info(f"进度: {progress}/{total} ({percentage:.1f}%)")

                            return f"工具执行结果: {result}"
                        except Exception as e:
                            error_msg = f"执行工具时出错: {str(e)}"
                            logging.error(error_msg)
                            return error_msg

                return f"未找到包含工具的服务器: {tool_call['tool']}"
            return llm_response
        except json.JSONDecodeError:
            return llm_response

    async def interactive_mode(self) -> None:
        """交互式命令行模式"""
        print("\n🎯 交互式MCP客户端")
        print("命令:")
        print("  list - 列出可用工具")
        print("  call <工具名> [参数] - 调用工具")
        print("  chat - 进入聊天模式")
        print("  quit - 退出客户端")
        print()

        while True:
            try:
                command = input("mcp> ").strip()

                if not command:
                    continue

                if command == "quit":
                    break

                elif command == "list":
                    await self.list_all_tools()

                elif command.startswith("call "):
                    await self.handle_tool_call(command)

                elif command == "chat":
                    await self.chat_mode()

                else:
                    print("❌ 未知命令。请尝试 'list', 'call <工具名>', 'chat' 或 'quit'")

            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except EOFError:
                break

    async def list_all_tools(self) -> None:
        """列出所有服务器的可用工具"""
        all_tools = []
        for server in self.servers:
            try:
                tools = await server.list_tools()
                all_tools.extend([(server.name, tool) for tool in tools])
            except Exception as e:
                logging.error(f"获取服务器 {server.name} 的工具列表时出错: {e}")

        if all_tools:
            print("\n📋 可用工具:")
            for i, (server_name, tool) in enumerate(all_tools, 1):
                print(f"{i}. {tool.name} (服务器: {server_name})")
                if tool.description:
                    print(f"   描述: {tool.description}")
                print()
        else:
            print("没有可用的工具")

    async def handle_tool_call(self, command: str) -> None:
        """处理工具调用命令"""
        parts = command.split(maxsplit=2)
        tool_name = parts[1] if len(parts) > 1 else ""

        if not tool_name:
            print("❌ 请指定工具名称")
            return

        # 解析参数
        arguments = {}
        if len(parts) > 2:
            try:
                arguments = json.loads(parts[2])
            except json.JSONDecodeError:
                print("❌ 参数格式无效（期望JSON格式）")
                return

        # 查找并执行工具
        for server in self.servers:
            tools = await server.list_tools()
            if any(tool.name == tool_name for tool in tools):
                try:
                    result = await server.execute_tool(tool_name, arguments)
                    print(f"\n🔧 工具 '{tool_name}' 结果:")
                    if hasattr(result, "content"):
                        for content in result.content:
                            if content.type == "text":
                                print(content.text)
                            else:
                                print(content)
                    else:
                        print(result)
                    return
                except Exception as e:
                    print(f"❌ 调用工具 '{tool_name}' 失败: {e}")
                    return

        print(f"❌ 未找到工具 '{tool_name}'")

    async def chat_mode(self) -> None:
        """聊天模式"""
        try:
            all_tools = []
            for server in self.servers:
                tools = await server.list_tools()
                all_tools.extend(tools)

            tools_description = "\n".join([tool.format_for_llm() for tool in all_tools])

            system_message = (
                "你是一个有用的助手，可以使用这些工具:\n\n"
                f"{tools_description}\n"
                "根据用户的问题选择合适的工具。"
                "如果不需要工具，请直接回答。\n\n"
                "重要：当你需要使用工具时，你必须只响应以下确切的JSON对象格式，不要其他内容:\n"
                "{\n"
                '    "tool": "工具名",\n'
                '    "arguments": {\n'
                '        "参数名": "值"\n'
                "    }\n"
                "}\n\n"
                "收到工具响应后：\n"
                "1. 将原始数据转换为自然的对话响应\n"
                "2. 保持响应简洁但信息丰富\n"
                "3. 专注于最相关的信息\n"
                "4. 使用用户问题中的适当上下文\n"
                "5. 避免简单重复原始数据\n\n"
                "请只使用上面明确定义的工具。"
            )

            messages = [{"role": "system", "content": system_message}]
            
            print("\n💬 进入聊天模式（输入 'quit' 或 'exit' 退出聊天）")

            while True:
                try:
                    user_input = input("您: ").strip()
                    if user_input.lower() in ["quit", "exit"]:
                        print("退出聊天模式...")
                        break

                    messages.append({"role": "user", "content": user_input})

                    llm_response = self.llm_client.get_response(messages)
                    print(f"\n助手: {llm_response}")

                    result = await self.process_llm_response(llm_response)

                    if result != llm_response:
                        messages.append({"role": "assistant", "content": llm_response})
                        messages.append({"role": "system", "content": result})

                        final_response = self.llm_client.get_response(messages)
                        print(f"\n最终响应: {final_response}")
                        messages.append({"role": "assistant", "content": final_response})
                    else:
                        messages.append({"role": "assistant", "content": llm_response})

                except KeyboardInterrupt:
                    print("\n退出聊天模式...")
                    break

        except Exception as e:
            logging.error(f"聊天模式出错: {e}")

    async def start(self) -> None:
        """主聊天会话处理程序"""
        try:
            print("🚀 正在启动MCP客户端...")
            
            # 初始化所有服务器
            for server in self.servers:
                try:
                    await server.initialize()
                    print(f"✅ 服务器 {server.name} 连接成功（{server.connection_type}）")
                except Exception as e:
                    logging.error(f"初始化服务器 {server.name} 失败: {e}")
                    # 继续处理其他服务器

            # 检查是否有成功连接的服务器
            connected_servers = [s for s in self.servers if s.session is not None]
            if not connected_servers:
                print("❌ 没有服务器连接成功")
                return

            print(f"✨ 已连接 {len(connected_servers)}/{len(self.servers)} 个服务器")
            
            # 启动交互模式
            await self.interactive_mode()

        finally:
            await self.cleanup_servers()


async def main() -> None:
    """初始化并运行聊天会话"""
    try:
        config = Configuration()
        
        # 尝试加载配置文件
        try:
            server_config = config.load_config("servers_config.json")
            servers = [Server(name, srv_config) for name, srv_config in server_config["mcpServers"].items()]
        except FileNotFoundError:
            # 如果没有配置文件，使用默认配置
            print("⚠️ 未找到servers_config.json，使用默认配置")
            default_servers = {
                "stdio_server": {
                    "type": "stdio",
                    "command": "python",
                    "args": ["-m", "mcp_server"],
                    "env": {}
                },
                "http_server": {
                    "type": "http",
                    "url": "http://localhost:8000/mcp",
                    "timeout": 60
                },
                "sse_server": {
                    "type": "sse", 
                    "url": "http://localhost:8000/sse",
                    "timeout": 60
                }
            }
            servers = [Server(name, srv_config) for name, srv_config in default_servers.items()]
        
        # 检查是否配置了LLM API密钥
        try:
            llm_client = LLMClient(config.llm_api_key)
            print("✅ LLM客户端已配置")
        except ValueError:
            print("⚠️ 未配置LLM_API_KEY，聊天模式将不可用")
            llm_client = None
        
        chat_session = ChatSession(servers, llm_client)
        await chat_session.start()
        
    except Exception as e:
        logging.error(f"启动应用时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())