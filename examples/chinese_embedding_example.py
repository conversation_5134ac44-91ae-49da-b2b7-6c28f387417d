#!/usr/bin/env python3
"""
Chinese Embedding Models Example
中文向量模型使用示例

演示如何使用各种中文优化的嵌入模型，包括：
- BAAI/bge-large-zh (推荐)
- BAAI/bge-base-zh 
- BAAI/bge-small-zh
- Text2Vec系列
- M3E系列
"""

import asyncio
import numpy as np
from pathlib import Path
from typing import List, Tuple

# 确保项目根目录在Python路径中
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.embedding_client import (
    EmbeddingClientFactory, 
    ChineseEmbeddingClient,
    EmbeddingManager
)
from loguru import logger


async def demo_chinese_models():
    """演示不同的中文嵌入模型"""
    print("🇨🇳 中文嵌入模型对比演示")
    print("=" * 50)
    
    # 测试文本
    test_texts = [
        "人工智能是计算机科学的一个分支",
        "机器学习是人工智能的子领域",
        "深度学习基于人工神经网络",
        "自然语言处理帮助计算机理解人类语言",
        "今天天气真不错，适合出去散步"
    ]
    
    # 要测试的中文模型
    chinese_models = ["bge-large-zh", "bge-base-zh", "bge-small-zh", "m3e-base"]
    
    print(f"📝 测试文本 ({len(test_texts)}条):")
    for i, text in enumerate(test_texts, 1):
        print(f"  {i}. {text}")
    print()
    
    results = {}
    
    for model_name in chinese_models:
        try:
            print(f"🔧 测试模型: {model_name}")
            
            # 创建中文客户端
            client = EmbeddingClientFactory.create_chinese_client(
                model=model_name,
                use_case="general"
            )
            
            # 初始化并获取模型信息
            await client.initialize()
            model_info = client.get_model_info()
            
            print(f"  📊 模型信息:")
            print(f"    - 名称: {model_info['model_name']}")
            print(f"    - 维度: {model_info['dimension']}")
            print(f"    - 最大长度: {model_info['max_length']}")
            print(f"    - 描述: {model_info['description']}")
            
            # 编码测试文本
            response = await client.encode(test_texts)
            embeddings = response.embeddings
            
            print(f"  ✅ 编码完成: {len(embeddings)}个向量")
            print(f"  📏 向量维度: {len(embeddings[0])}")
            
            # 计算相似度矩阵
            similarity_matrix = compute_similarity_matrix(embeddings)
            results[model_name] = {
                "model_info": model_info,
                "embeddings": embeddings,
                "similarity_matrix": similarity_matrix
            }
            
            print(f"  🎯 平均相似度: {np.mean(similarity_matrix):.3f}")
            print()
            
            await client.cleanup()
            
        except Exception as e:
            print(f"  ❌ {model_name} 测试失败: {e}")
            print()
    
    # 分析结果
    if results:
        analyze_results(results, test_texts)


def compute_similarity_matrix(embeddings: List[List[float]]) -> np.ndarray:
    """计算嵌入向量的相似度矩阵"""
    n = len(embeddings)
    similarity_matrix = np.zeros((n, n))
    
    for i in range(n):
        for j in range(n):
            if i != j:
                # 计算余弦相似度
                vec1 = np.array(embeddings[i])
                vec2 = np.array(embeddings[j])
                similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                similarity_matrix[i][j] = similarity
    
    return similarity_matrix


def analyze_results(results: dict, test_texts: List[str]):
    """分析不同模型的结果"""
    print("📊 模型对比分析")
    print("=" * 50)
    
    # 显示模型参数对比
    print("🔍 模型参数对比:")
    print(f"{'模型名称':<20} {'维度':<8} {'最大长度':<10} {'描述'}")
    print("-" * 70)
    
    for model_name, data in results.items():
        info = data["model_info"]
        print(f"{model_name:<20} {info['dimension']:<8} {info['max_length']:<10} {info['description'][:30]}...")
    
    print()
    
    # 分析语义相似度效果
    print("🎯 语义相似度分析:")
    print("测试相关文本对的相似度:")
    
    # 相关文本对 (索引)
    related_pairs = [
        (0, 1, "人工智能 vs 机器学习"),
        (0, 2, "人工智能 vs 深度学习"),
        (1, 2, "机器学习 vs 深度学习"),
        (0, 3, "人工智能 vs NLP")
    ]
    
    unrelated_pair = (4, 0, "天气 vs 人工智能")  # 不相关对比
    
    for model_name, data in results.items():
        similarity_matrix = data["similarity_matrix"]
        print(f"\n  {model_name}:")
        
        # 相关文本对的相似度
        related_scores = []
        for i, j, desc in related_pairs:
            score = similarity_matrix[i][j]
            related_scores.append(score)
            print(f"    {desc}: {score:.3f}")
        
        # 不相关文本对的相似度
        unrelated_score = similarity_matrix[unrelated_pair[0]][unrelated_pair[1]]
        print(f"    {unrelated_pair[2]}: {unrelated_score:.3f}")
        
        # 计算区分度 (相关文本相似度 - 不相关文本相似度)
        discrimination = np.mean(related_scores) - unrelated_score
        print(f"    📈 语义区分度: {discrimination:.3f}")


async def demo_chinese_text_preprocessing():
    """演示中文文本预处理功能"""
    print("\n🔧 中文文本预处理演示")
    print("=" * 50)
    
    # 包含各种中文文本特征的测试用例
    test_cases = [
        "这是一个正常的中文句子。",
        "这是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的句子，用来测试长文本截断功能。这个句子会被自动截断在合适的位置。",
        "包含\n\n多个\n换行符\t和制表符   的文本",
        "混合中英文的text，包含数字123和符号！@#",
        "包含。多个！句号？的复杂，句子；结构：测试"
    ]
    
    client = EmbeddingClientFactory.create_chinese_client(
        model="bge-base-zh",
        use_case="general"
    )
    
    await client.initialize()
    
    for i, text in enumerate(test_cases, 1):
        print(f"📝 测试用例 {i}:")
        print(f"  原文: {repr(text)}")
        
        # 获取预处理后的文本
        processed = client._preprocess_chinese_texts(text)
        print(f"  处理后: {repr(processed)}")
        
        # 编码测试
        try:
            response = await client.encode(text)
            print(f"  ✅ 编码成功，维度: {len(response.embeddings[0])}")
        except Exception as e:
            print(f"  ❌ 编码失败: {e}")
        
        print()
    
    await client.cleanup()


async def demo_embedding_manager_chinese():
    """演示使用EmbeddingManager管理多个中文嵌入模型"""
    print("\n🎯 中文嵌入模型管理器演示")
    print("=" * 50)
    
    # 创建嵌入管理器
    manager = EmbeddingManager()
    
    # 添加多个中文嵌入客户端
    models_config = [
        ("primary", "bge-large-zh", "通用场景主力模型"),
        ("balanced", "bge-base-zh", "平衡性能和速度"),
        ("fast", "bge-small-zh", "快速推理模型"),
    ]
    
    for name, model, description in models_config:
        try:
            client = EmbeddingClientFactory.create_chinese_client(
                model=model,
                use_case="general"
            )
            await manager.add_client(name, client)
            print(f"✅ 添加客户端: {name} ({model}) - {description}")
            
        except Exception as e:
            print(f"❌ 添加客户端失败: {name} - {e}")
    
    # 设置主要客户端
    await manager.set_primary("primary")
    print(f"🎯 设置主要客户端: primary")
    
    # 测试文本
    test_query = "什么是深度学习？"
    candidate_texts = [
        "深度学习是机器学习的子集",
        "神经网络是深度学习的基础",
        "卷积神经网络用于图像识别",
        "今天天气很好，适合户外运动",
        "人工智能改变了我们的生活方式"
    ]
    
    print(f"\n📝 测试查询: {test_query}")
    print(f"📚 候选文本: {len(candidate_texts)}条")
    
    # 使用不同的客户端进行相似度搜索
    for client_name in ["primary", "balanced", "fast"]:
        try:
            print(f"\n🔍 使用客户端: {client_name}")
            
            # 查找最相似的文本
            similar_results = await manager.find_most_similar(
                query=test_query,
                candidates=candidate_texts,
                top_k=3,
                client_name=client_name
            )
            
            print("  🏆 最相似的文本:")
            for rank, (idx, text, similarity) in enumerate(similar_results, 1):
                print(f"    {rank}. [{similarity:.3f}] {text}")
                
        except Exception as e:
            print(f"  ❌ 搜索失败: {e}")
    
    # 比较不同模型的相似度计算结果
    print(f"\n⚖️ 模型间相似度对比:")
    for i, candidate in enumerate(candidate_texts):
        print(f"\n  文本{i+1}: {candidate}")
        for client_name in ["primary", "balanced"]:
            try:
                similarity = await manager.compare_similarity(
                    test_query, candidate, client_name
                )
                print(f"    {client_name}: {similarity:.3f}")
            except Exception as e:
                print(f"    {client_name}: 计算失败 - {e}")
    
    # 清理资源
    await manager.cleanup()
    print(f"\n✅ 清理完成")


async def demo_model_recommendations():
    """演示模型推荐功能"""
    print("\n💡 中文嵌入模型推荐演示")
    print("=" * 50)
    
    # 不同使用场景
    use_cases = [
        ("general", "通用场景"),
        ("performance", "最佳性能"),
        ("balanced", "平衡需求"),
        ("fast", "快速推理"),
        ("multilingual", "多语言支持"),
        ("lightweight", "轻量级部署")
    ]
    
    print("🎯 基于使用场景的模型推荐:")
    for case, description in use_cases:
        recommended = EmbeddingClientFactory.get_model_recommendation(case)
        print(f"  {case:<12}: {recommended:<15} - {description}")
    
    print("\n📋 所有可用的中文嵌入模型:")
    available_models = EmbeddingClientFactory.list_chinese_models()
    
    for model_key, config in available_models.items():
        print(f"\n🏷️  {model_key}:")
        print(f"    模型名称: {config['model_name']}")
        print(f"    维度: {config['dimension']}")
        print(f"    最大长度: {config['max_length']}")
        print(f"    描述: {config['description']}")
        print(f"    适用场景: {', '.join(config['use_case'])}")


def print_performance_tips():
    """打印性能优化建议"""
    print("\n" + "=" * 60)
    print("⚡ 中文嵌入模型性能优化建议")
    print("=" * 60)
    
    tips = """
1. 模型选择建议:
   🎯 通用场景: bge-large-zh (最佳效果)
   ⚡ 速度优先: bge-small-zh (快速推理)
   💡 平衡选择: bge-base-zh (性能与速度平衡)
   🌍 多语言: m3e-large (中英文混合)

2. 部署优化:
   - 本地部署使用 sentence-transformers
   - 云端API使用 HuggingFace Inference API
   - 大规模应用考虑模型量化和缓存

3. 文本预处理:
   - 自动处理长文本截断
   - 智能中文句子分割
   - 去除多余空格和特殊字符

4. 批量处理:
   - 使用 encode_batch 提高吞吐量
   - 合理设置 batch_size (推荐32-128)
   - 考虑异步处理大量文本

5. 缓存策略:
   - 缓存常用文本的嵌入向量
   - 使用Redis或内存缓存加速重复查询
   - 实现向量数据库持久化存储

6. 硬件建议:
   - GPU加速: CUDA兼容显卡
   - 内存要求: 至少8GB (large模型需要16GB+)
   - CPU: 多核处理器提升并行处理能力
"""
    print(tips)


async def main():
    """主演示函数"""
    print("🌟 中文嵌入模型完整演示")
    print("专门针对中文文本优化的向量表示系统")
    print("=" * 60)
    
    try:
        # 1. 基础模型演示
        await demo_chinese_models()
        
        # 询问是否继续
        print("\n" + "=" * 60)
        response = input("是否继续演示文本预处理功能？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            await demo_chinese_text_preprocessing()
        
        # 2. 管理器演示
        print("\n" + "=" * 60)
        response = input("是否演示嵌入管理器功能？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            await demo_embedding_manager_chinese()
        
        # 3. 推荐系统演示
        print("\n" + "=" * 60)
        response = input("是否查看模型推荐和选择建议？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            await demo_model_recommendations()
        
        # 性能优化建议
        print_performance_tips()
        
        print("\n🎉 中文嵌入模型演示完成!")
        print("现在您可以在RAG系统中使用这些优化的中文嵌入模型了。")
        
    except KeyboardInterrupt:
        print("\n⛔ 演示被用户中断")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        logger.error(f"Chinese embedding demo error: {e}")


if __name__ == "__main__":
    # 配置日志
    logger.add(
        "logs/chinese_embedding_demo_{time}.log",
        rotation="10 MB",
        retention="3 days",
        level="INFO"
    )
    
    asyncio.run(main())