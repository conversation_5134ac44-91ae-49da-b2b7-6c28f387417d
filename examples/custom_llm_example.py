#!/usr/bin/env python3
"""
Custom LLM Configuration Example
自定义LLM配置示例

演示如何使用自定义API URL和密钥配置LLM客户端
支持各种OpenAI兼容的服务，如：
- vLLM服务器
- Text Generation WebUI
- LocalAI
- FastChat
- 其他自定义API服务器
"""

import asyncio
import os
import json
from pathlib import Path

# 确保项目根目录在Python路径中
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.llm_client import LLMClientFactory, LLMMessage, LLMManager
from loguru import logger


async def example_openai_compatible_server():
    """演示使用OpenAI兼容服务器"""
    print("🔧 OpenAI兼容服务器配置示例")
    print("=" * 50)
    
    # 配置1: vLLM本地服务器
    print("配置1: vLLM本地服务器")
    vllm_client = LLMClientFactory.create_client(
        provider="openai",
        api_key="EMPTY",  # vLLM通常不需要真实的API密钥
        model="llama-2-7b-chat-hf",  # 你加载的模型名称
        base_url="http://localhost:8000/v1",  # vLLM默认端口
        temperature=0.7
    )
    
    try:
        await vllm_client.initialize()
        print("✅ vLLM客户端初始化成功")
        
        # 测试生成
        messages = [LLMMessage(role="user", content="Hello! Can you introduce yourself?")]
        response = await vllm_client.generate(messages)
        print(f"🤖 vLLM响应: {response.content[:100]}...")
        
    except Exception as e:
        print(f"⚠️  vLLM连接失败: {e}")
    finally:
        await vllm_client.cleanup()
    
    print()


async def example_text_generation_webui():
    """演示使用Text Generation WebUI"""
    print("配置2: Text Generation WebUI")
    print("-" * 30)
    
    # Text Generation WebUI OpenAI API兼容模式
    webui_client = LLMClientFactory.create_client(
        provider="openai",
        api_key="sk-111111111111111111111111111111111111111111111111",  # WebUI需要任意密钥
        model="gpt-3.5-turbo",  # WebUI中任意模型名
        base_url="http://localhost:5000/v1",  # WebUI默认端口
        custom_headers={"User-Agent": "AdvancedRAGSystem/1.0"}
    )
    
    try:
        await webui_client.initialize()
        print("✅ Text Generation WebUI客户端初始化成功")
        
        # 测试流式生成
        messages = [LLMMessage(role="user", content="写一首关于AI的短诗")]
        print("🎵 流式生成诗歌:")
        async for chunk in webui_client.stream_generate(messages):
            print(chunk, end="", flush=True)
        print("\n")
        
    except Exception as e:
        print(f"⚠️  Text Generation WebUI连接失败: {e}")
    finally:
        await webui_client.cleanup()


async def example_custom_api_server():
    """演示使用自定义API服务器"""
    print("配置3: 自定义API服务器")
    print("-" * 30)
    
    # 自定义API服务器配置
    custom_headers = {
        "Authorization": "Bearer your-custom-token",
        "X-Custom-Header": "custom-value",
        "User-Agent": "AdvancedRAGSystem/1.0"
    }
    
    custom_client = LLMClientFactory.create_client(
        provider="openai",
        api_key="your-api-key",
        model="your-model-name",
        base_url="https://your-custom-api.example.com/v1",
        custom_headers=custom_headers,
        temperature=0.8,
        max_tokens=2048
    )
    
    try:
        await custom_client.initialize()
        print("✅ 自定义API服务器客户端初始化成功")
        print(f"📡 使用URL: https://your-custom-api.example.com/v1")
        print(f"🔐 自定义请求头: {list(custom_headers.keys())}")
        
    except Exception as e:
        print(f"⚠️  自定义API服务器连接失败: {e}")
    finally:
        await custom_client.cleanup()


async def example_multiple_providers():
    """演示使用多个LLM提供商"""
    print("配置4: 多提供商配置与自动切换")
    print("-" * 40)
    
    # 创建LLM管理器
    manager = LLMManager()
    
    # 添加多个客户端
    clients_config = [
        {
            "name": "primary_openai",
            "provider": "openai",
            "api_key": "sk-your-openai-key",
            "model": "gpt-4-turbo-preview",
            "base_url": "https://api.openai.com/v1"
        },
        {
            "name": "backup_vllm",
            "provider": "openai",
            "api_key": "EMPTY",
            "model": "llama-2-7b-chat-hf",
            "base_url": "http://localhost:8000/v1"
        },
        {
            "name": "anthropic",
            "provider": "anthropic",
            "api_key": "your-anthropic-key",
            "model": "claude-3-haiku-20240307",
            "base_url": "https://api.anthropic.com"
        },
        {
            "name": "groq",
            "provider": "groq",
            "api_key": "your-groq-key",
            "model": "llama3-70b-8192",
            "base_url": "https://api.groq.com/openai/v1"
        }
    ]
    
    # 初始化所有可用的客户端
    available_clients = []
    for config in clients_config:
        try:
            client = LLMClientFactory.create_client(
                provider=config["provider"],
                api_key=config["api_key"],
                model=config["model"],
                base_url=config.get("base_url")
            )
            await manager.add_client(config["name"], client)
            available_clients.append(config["name"])
            print(f"✅ {config['name']} 初始化成功")
            
        except Exception as e:
            print(f"⚠️  {config['name']} 初始化失败: {e}")
    
    # 设置备用顺序
    if available_clients:
        manager.set_fallback_order(available_clients)
        print(f"🔄 设置备用顺序: {' → '.join(available_clients)}")
        
        # 测试自动切换
        messages = [LLMMessage(role="user", content="测试自动切换功能")]
        try:
            response = await manager.generate(messages)
            print(f"🎯 成功响应: {response.content[:50]}...")
        except Exception as e:
            print(f"❌ 所有客户端都失败: {e}")
    
    # 清理资源
    await manager.cleanup()


async def example_environment_configuration():
    """演示从环境变量配置"""
    print("配置5: 从环境变量配置")
    print("-" * 30)
    
    # 设置环境变量示例
    os.environ["OPENAI_API_KEY"] = "your-openai-key"
    os.environ["OPENAI_BASE_URL"] = "http://localhost:8000/v1"
    os.environ["CUSTOM_HEADERS"] = json.dumps({
        "User-Agent": "AdvancedRAGSystem/1.0",
        "X-Client-Version": "2.0"
    })
    
    # 从环境变量读取配置
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    # 解析自定义headers
    custom_headers = {}
    headers_str = os.getenv("CUSTOM_HEADERS")
    if headers_str:
        try:
            custom_headers = json.loads(headers_str)
        except Exception as e:
            print(f"⚠️  解析CUSTOM_HEADERS失败: {e}")
    
    print(f"🔑 API Key: {api_key[:20]}..." if api_key else "❌ No API Key")
    print(f"🌐 Base URL: {base_url}")
    print(f"📋 自定义Headers: {list(custom_headers.keys())}")
    
    if api_key:
        client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="gpt-3.5-turbo",
            base_url=base_url,
            custom_headers=custom_headers
        )
        
        try:
            await client.initialize()
            print("✅ 环境变量配置客户端初始化成功")
        except Exception as e:
            print(f"⚠️  环境变量配置客户端初始化失败: {e}")
        finally:
            await client.cleanup()


def print_configuration_guide():
    """打印配置指南"""
    print("\n" + "=" * 60)
    print("🔧 LLM自定义配置指南")
    print("=" * 60)
    
    guide = """
1. vLLM服务器配置:
   - 启动vLLM: python -m vllm.entrypoints.openai.api_server --model llama-2-7b-chat-hf
   - API密钥: "EMPTY"或任意字符串
   - Base URL: http://localhost:8000/v1

2. Text Generation WebUI配置:
   - 启动WebUI时添加 --api --api-blocking-port 5000
   - API密钥: 任意51字符字符串 (如: sk-111111111111111111111111111111111111111111111111)
   - Base URL: http://localhost:5000/v1

3. LocalAI配置:
   - Base URL: http://localhost:8080/v1
   - API密钥: 不需要或任意字符串
   - 模型名: 根据LocalAI配置的模型名称

4. FastChat配置:
   - 启动API服务器: python -m fastchat.serve.openai_api_server --host localhost --port 8000
   - Base URL: http://localhost:8000/v1
   - API密钥: "EMPTY"

5. 环境变量配置:
   在.env文件中设置:
   OPENAI_BASE_URL=http://localhost:8000/v1
   CUSTOM_HEADERS={"User-Agent": "MyApp/1.0"}

6. 自定义Headers使用场景:
   - 身份验证: Authorization: Bearer token
   - 用户标识: User-Agent: MyApplication/1.0
   - 版本控制: X-API-Version: v1
   - 自定义参数: X-Custom-Param: value
"""
    print(guide)


async def main():
    """主演示函数"""
    print("🌟 LLM自定义配置演示")
    print("支持各种OpenAI兼容服务器的配置和使用")
    print("=" * 60)
    
    # 打印配置指南
    print_configuration_guide()
    
    try:
        # 运行各种配置示例
        await example_openai_compatible_server()
        await example_text_generation_webui()
        await example_custom_api_server()
        await example_multiple_providers()
        await example_environment_configuration()
        
        print("\n🎉 自定义LLM配置演示完成!")
        print("您可以根据自己的服务器配置修改上述示例代码。")
        
    except KeyboardInterrupt:
        print("\n⛔ 演示被用户中断")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        logger.error(f"Custom LLM demo error: {e}")


if __name__ == "__main__":
    # 配置日志
    logger.add(
        "logs/custom_llm_demo_{time}.log",
        rotation="10 MB",
        retention="3 days",
        level="INFO"
    )
    
    asyncio.run(main())