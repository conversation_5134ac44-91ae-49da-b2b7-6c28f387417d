#!/usr/bin/env python3
"""
测试修复后的实体关系系统
"""
import asyncio
import os
from dotenv import load_dotenv

async def test_fixed_relations():
    """测试修复后的系统"""
    print("🔧 测试修复后的实体关系系统")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.memory_manager import GraphitiMemoryManager
        
        # 初始化
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.1,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        embedding_manager = EmbeddingManager()
        local_client = EmbeddingClientFactory.create_client(
            provider="sentence_transformers",
            model="all-MiniLM-L6-v2"
        )
        await embedding_manager.add_client("local", local_client)
        
        memory_manager = GraphitiMemoryManager(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="00000000",
            llm_manager=llm_manager,
            embedding_client=embedding_manager.get_primary_client(),
            group_id="fixed_test"
        )
        
        await memory_manager.initialize()
        print("✅ 初始化完成")
        
        # 清理数据库
        await memory_manager.clear_graph()
        print("✅ 数据库清理完成")
        
        # 添加一个简单的测试记忆
        print("\n🔗 添加测试记忆...")
        await memory_manager.add_memory(
            memory_type="fact",
            content="苹果公司的CEO是蒂姆·库克",
            category="leadership"
        )
        print("✅ 记忆添加完成")
        
        # 等待处理
        print("⏳ 等待处理...")
        await asyncio.sleep(10)
        
        # 检查结果
        print("\n📊 检查处理结果...")
        from neo4j import AsyncGraphDatabase
        
        driver = AsyncGraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '00000000'))
        
        async with driver.session() as session:
            # 检查节点
            result = await session.run('MATCH (n) RETURN labels(n) as labels, count(n) as count')
            records = await result.data()
            
            entity_count = 0
            episodic_count = 0
            
            for record in records:
                labels = record['labels']
                count = record['count']
                print(f"  {labels}: {count} 个")
                if 'Entity' in labels:
                    entity_count += count
                elif 'Episodic' in labels:
                    episodic_count += count
            
            # 检查关系
            result = await session.run('MATCH ()-[r:RELATES_TO]->() RETURN count(r) as rel_count')
            record = await result.single()
            rel_count = record['rel_count'] if record else 0
            
            print(f"  RELATES_TO关系: {rel_count} 个")
            
            # 评估
            print(f"\n📊 结果评估:")
            if entity_count > 0:
                print(f"🎉 成功! 创建了 {entity_count} 个实体")
                if rel_count > 0:
                    print(f"🎉 关系建立成功! 创建了 {rel_count} 个关系")
                    success = True
                else:
                    print("⚠️ 实体创建成功，但关系建立失败")
                    success = False
            else:
                print("❌ 实体创建失败")
                success = False
                
                # 查看Episodic内容
                result = await session.run('MATCH (e:Episodic) RETURN e.content as content LIMIT 1')
                record = await result.single()
                if record:
                    content = record['content']
                    print(f"  Episodic内容: {content[:100]}...")
        
        await driver.close()
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        await memory_manager.cleanup()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_fixed_relations())
    print("\n" + "=" * 50)
    if success:
        print("🎉 修复成功! 实体关系系统正常工作")
    else:
        print("❌ 仍需要进一步调试")