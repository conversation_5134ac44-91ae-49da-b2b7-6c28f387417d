#!/usr/bin/env python3
"""
Advanced RAG System Demo
高级RAG系统使用演示

这个演示展示了如何使用完整的RAG系统，包括：
- 用户输入 → Graphiti上下文检索 → 超级智能处理 → 有向图执行 → 智能循环控制 → Graphiti记忆更新
"""

import asyncio
import os
from pathlib import Path

# 确保项目根目录在Python路径中
import sys
sys.path.insert(0, str(Path(__file__).parent))

from advanced_rag_system import AdvancedRAGSystem, RAGSystemConfig
from core.intelligent_loop_controller import LoopStrategy
from core.graph_execution_engine import ExecutionStrategy
from loguru import logger

# 配置日志
logger.add(
    "logs/demo_{time}.log", 
    rotation="100 MB", 
    retention="7 days",
    level="INFO"
)


async def setup_environment():
    """设置环境和依赖检查"""
    print("🔧 检查环境配置...")
    
    # 检查环境变量文件
    if not os.path.exists('.env'):
        print("⚠️  .env文件不存在，请复制.env.example并配置API密钥")
        return False
    
    # 检查必要的环境变量
    required_vars = ['OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境配置检查通过")
    return True


async def demo_basic_usage():
    """基本用法演示"""
    print("\n🎯 基本用法演示")
    print("=" * 50)
    
    # 创建系统配置
    config = RAGSystemConfig(
        llm_provider="openai",
        llm_model="gpt-4-turbo-preview",
        embedding_provider="openai",
        embedding_model="text-embedding-ada-002",
        neo4j_uri="bolt://localhost:7687",  # 如果没有Neo4j，会使用内存存储
        max_iterations=3,
        quality_threshold=0.8
    )
    
    # 创建RAG系统
    rag_system = AdvancedRAGSystem(config)
    
    try:
        # 初始化系统
        print("🚀 初始化RAG系统...")
        await rag_system.initialize()
        print("✅ 系统初始化完成")
        
        # 演示查询
        demo_queries = [
            "请帮我分析一下当前的技术趋势",
            "如何提高团队的工作效率？",
            "解释一下机器学习的基本概念"
        ]
        
        for i, query in enumerate(demo_queries, 1):
            print(f"\n📝 演示查询 {i}: {query}")
            print("-" * 30)
            
            # 处理用户输入
            result = await rag_system.process_user_input(
                user_input=query,
                user_id="demo_user",
                strategy=LoopStrategy.ADAPTIVE,
                execution_strategy=ExecutionStrategy.HYBRID
            )
            
            # 显示结果
            print(f"✅ 处理成功: {result['success']}")
            print(f"🔄 循环次数: {result['loop_iterations']}")
            print(f"📊 质量分数: {result['quality_score']:.3f}")
            print(f"⏱️  处理时间: {result['processing_time']:.2f}秒")
            
            if result.get('response'):
                print(f"💬 响应: {result['response'][:200]}...")
            
            if result.get('key_insights'):
                print(f"💡 关键洞察: {len(result['key_insights'])}个")
            
            print()
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        logger.error(f"Demo error: {e}")
        
    finally:
        # 清理资源
        print("🧹 清理系统资源...")
        await rag_system.cleanup()
        print("✅ 清理完成")


async def demo_advanced_features():
    """高级功能演示"""
    print("\n🔥 高级功能演示")
    print("=" * 50)
    
    config = RAGSystemConfig(
        llm_provider="openai",
        llm_model="gpt-4-turbo-preview",
        max_iterations=5,  # 更多迭代
        quality_threshold=0.9  # 更高质量要求
    )
    
    rag_system = AdvancedRAGSystem(config)
    
    try:
        await rag_system.initialize()
        print("✅ 高级RAG系统初始化完成")
        
        # 复杂任务演示
        complex_query = """
        我需要分析我们公司的数据架构现状，并制定一个数据治理的改进方案。
        请考虑以下方面：
        1. 数据质量评估
        2. 数据安全性
        3. 性能优化建议
        4. 成本控制策略
        """
        
        print(f"📝 复杂查询: {complex_query.strip()}")
        print("-" * 40)
        
        # 使用质量优先策略
        result = await rag_system.process_user_input(
            user_input=complex_query,
            user_id="enterprise_user",
            strategy=LoopStrategy.QUALITY_FOCUSED,
            execution_strategy=ExecutionStrategy.CRITICAL_PATH
        )
        
        print(f"✅ 处理成功: {result['success']}")
        print(f"🎯 策略: {result.get('strategy_used', 'Unknown')}")
        print(f"🔄 循环次数: {result['loop_iterations']}")
        print(f"📊 最终质量分数: {result['quality_score']:.3f}")
        print(f"📈 平均质量分数: {result['execution_summary']['average_quality']:.3f}")
        print(f"🔁 收敛率: {result['execution_summary']['convergence_rate']:.3f}")
        
        if result.get('key_insights'):
            print(f"\n💡 关键洞察 ({len(result['key_insights'])}个):")
            for insight in result['key_insights'][:5]:
                print(f"  • {insight}")
        
        # 获取会话统计
        session_stats = await rag_system.get_session_statistics(result['session_id'])
        print(f"\n📊 会话统计:")
        print(f"  交互次数: {session_stats.get('interaction_count', 0)}")
        print(f"  最后质量分数: {session_stats.get('last_quality_score', 0):.3f}")
        
    except Exception as e:
        print(f"❌ 高级功能演示错误: {e}")
        logger.error(f"Advanced demo error: {e}")
        
    finally:
        await rag_system.cleanup()


async def demo_memory_system():
    """记忆系统演示"""
    print("\n🧠 记忆系统演示")
    print("=" * 50)
    
    config = RAGSystemConfig()
    rag_system = AdvancedRAGSystem(config)
    
    try:
        await rag_system.initialize()
        
        user_id = "memory_demo_user"
        
        # 第一次交互 - 建立偏好
        print("📝 第一次交互 - 建立用户偏好")
        result1 = await rag_system.process_user_input(
            user_input="我喜欢详细的技术分析，特别是关于云计算和容器化的内容",
            user_id=user_id
        )
        print(f"✅ 第一次交互完成，质量分数: {result1['quality_score']:.3f}")
        
        # 模拟一些时间间隔
        await asyncio.sleep(1)
        
        # 第二次交互 - 相关查询
        print("\n📝 第二次交互 - 相关技术查询")
        result2 = await rag_system.process_user_input(
            user_input="请解释Docker容器的最佳实践",
            user_id=user_id
        )
        print(f"✅ 第二次交互完成，质量分数: {result2['quality_score']:.3f}")
        
        # 第三次交互 - 测试记忆效果
        print("\n📝 第三次交互 - 测试记忆联想")
        result3 = await rag_system.process_user_input(
            user_input="推荐一些云原生的解决方案",
            user_id=user_id
        )
        print(f"✅ 第三次交互完成，质量分数: {result3['quality_score']:.3f}")
        
        # 获取用户画像
        print("\n👤 获取用户画像:")
        user_profile = await rag_system.get_user_profile(user_id)
        if user_profile and not user_profile.get('error'):
            print(f"  用户偏好已学习并存储")
        else:
            print(f"  用户画像: {user_profile}")
        
        print(f"\n📈 记忆系统效果:")
        print(f"  质量改进: {result1['quality_score']:.3f} → {result3['quality_score']:.3f}")
        
    except Exception as e:
        print(f"❌ 记忆系统演示错误: {e}")
        logger.error(f"Memory demo error: {e}")
        
    finally:
        await rag_system.cleanup()


async def demo_mcp_integration():
    """MCP集成演示"""
    print("\n🔌 MCP集成演示")
    print("=" * 50)
    
    config = RAGSystemConfig()
    rag_system = AdvancedRAGSystem(config)
    
    try:
        await rag_system.initialize()
        
        # 添加示例MCP服务器（Milvus）
        milvus_config = {
            "transport": "stdio",
            "command": "python",
            "args": ["servers/milvus_server.py"],
            "env": {}
        }
        
        print("🔧 添加Milvus MCP服务器...")
        success = await rag_system.add_mcp_server("milvus", milvus_config)
        
        if success:
            print("✅ Milvus服务器添加成功")
            
            # 测试包含向量搜索的查询
            result = await rag_system.process_user_input(
                user_input="在知识库中搜索关于人工智能伦理的相关内容，并总结要点",
                user_id="mcp_demo_user"
            )
            
            print(f"✅ MCP集成查询完成")
            print(f"📊 质量分数: {result['quality_score']:.3f}")
            print(f"🔄 循环次数: {result['loop_iterations']}")
            
        else:
            print("⚠️  Milvus服务器添加失败，可能需要先安装依赖")
        
    except Exception as e:
        print(f"❌ MCP集成演示错误: {e}")
        logger.error(f"MCP demo error: {e}")
        
    finally:
        await rag_system.cleanup()


async def main():
    """主演示函数"""
    print("🌟 Advanced RAG System Demo")
    print("高级RAG系统完整演示")
    print("=" * 60)
    
    # 环境检查
    if not await setup_environment():
        print("❌ 环境检查失败，请检查配置后重试")
        return
    
    try:
        # 运行演示
        await demo_basic_usage()
        
        # 询问是否继续高级演示
        print("\n" + "=" * 60)
        response = input("是否继续高级功能演示？(y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            await demo_advanced_features()
            
            print("\n" + "=" * 60)
            response = input("是否演示记忆系统？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                await demo_memory_system()
            
            print("\n" + "=" * 60)
            response = input("是否演示MCP集成？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                await demo_mcp_integration()
        
        print("\n🎉 演示完成!")
        print("您现在可以根据需要修改代码并集成到您的应用中。")
        
    except KeyboardInterrupt:
        print("\n⛔ 演示被用户中断")
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        logger.error(f"Main demo error: {e}")


if __name__ == "__main__":
    asyncio.run(main())