"""
LLM Client with Streaming Support
"""
import asyncio
import json
import os
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, AsyncGenerator, Dict, List, Optional, Union
from enum import Enum

import httpx
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
import groq

from loguru import logger


class LLMProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GROQ = "groq"


@dataclass
class LLMMessage:
    role: str
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    content: str
    finish_reason: Optional[str] = None
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class BaseLLMClient(ABC):
    """Base class for LLM clients with streaming support"""
    
    def __init__(
        self,
        provider: LLMProvider,
        api_key: str,
        model: str,
        temperature: float = 0.7,
        max_tokens: int = 4096,
        base_url: Optional[str] = None,
        custom_headers: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        self.provider = provider
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.base_url = base_url
        self.custom_headers = custom_headers or {}
        self.kwargs = kwargs
        self._client = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the LLM client"""
        pass

    @abstractmethod
    async def generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate a single response"""
        pass

    @abstractmethod
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response"""
        pass

    async def cleanup(self) -> None:
        """Cleanup resources"""
        if hasattr(self._client, 'close'):
            await self._client.close()


class OpenAILLMClient(BaseLLMClient):
    """OpenAI LLM Client with streaming support"""
    
    async def initialize(self) -> None:
        client_params = {
            "api_key": self.api_key,
        }
        
        # 添加自定义base_url支持
        if self.base_url:
            client_params["base_url"] = self.base_url
            logger.info(f"Using custom OpenAI base URL: {self.base_url}")
        
        # 添加自定义headers支持
        if self.custom_headers:
            client_params["default_headers"] = self.custom_headers
            logger.info(f"Using custom headers: {list(self.custom_headers.keys())}")
        
        # 添加其他自定义参数
        client_params.update(self.kwargs)
        
        self._client = AsyncOpenAI(**client_params)
        logger.info(f"Initialized OpenAI client with model: {self.model}")

    async def generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        if not self._client:
            await self.initialize()

        openai_messages = []
        if system_prompt:
            openai_messages.append({"role": "system", "content": str(system_prompt)})
        
        # Ensure messages are properly converted to dictionaries
        for msg in messages:
            if self.base_url and "bigmodel.cn" in self.base_url:
                openai_messages.append({
                    "role": str(msg.role), 
                    "content": [{"type": "text", "text": str(msg.content)}]
                })
            else:
                openai_messages.append({
                    "role": str(msg.role), 
                    "content": str(msg.content)
                })

        try:
            # 避免参数冲突，kwargs中的参数优先
            call_params = {
                "model": self.model,
                "messages": openai_messages,
                "temperature": kwargs.get("temperature", self.temperature),
                "max_tokens": kwargs.get("max_tokens", self.max_tokens)
            }
            
            # 移除可能冲突的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ["temperature", "max_tokens"]}
            call_params.update(filtered_kwargs)
            
            response = await self._client.chat.completions.create(**call_params)
            
            return LLMResponse(
                content=response.choices[0].message.content,
                finish_reason=response.choices[0].finish_reason,
                usage=response.usage.dict() if response.usage else None
            )
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise

    async def stream_generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        if not self._client:
            await self.initialize()

        openai_messages = []
        if system_prompt:
            openai_messages.append({"role": "system", "content": str(system_prompt)})
        
        # Ensure messages are properly converted to dictionaries
        for msg in messages:
            if self.base_url and "bigmodel.cn" in self.base_url:
                openai_messages.append({
                    "role": str(msg.role), 
                    "content": [{"type": "text", "text": str(msg.content)}]
                })
            else:
                openai_messages.append({
                    "role": str(msg.role), 
                    "content": str(msg.content)
                })

        try:
            # 避免参数冲突，移除可能冲突的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ["temperature", "max_tokens"]}
            
            stream = await self._client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens),
                stream=True,
                **filtered_kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"OpenAI streaming error: {e}")
            raise


class AnthropicLLMClient(BaseLLMClient):
    """Anthropic LLM Client with streaming support"""
    
    async def initialize(self) -> None:
        client_params = {
            "api_key": self.api_key,
        }
        
        # 添加自定义base_url支持
        if self.base_url:
            client_params["base_url"] = self.base_url
            logger.info(f"Using custom Anthropic base URL: {self.base_url}")
        
        # 添加自定义headers支持
        if self.custom_headers:
            client_params["default_headers"] = self.custom_headers
            logger.info(f"Using custom headers: {list(self.custom_headers.keys())}")
        
        # 添加其他自定义参数
        client_params.update(self.kwargs)
        
        self._client = AsyncAnthropic(**client_params)
        logger.info(f"Initialized Anthropic client with model: {self.model}")

    async def generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        if not self._client:
            await self.initialize()

        try:
            # 避免参数冲突，kwargs中的参数优先
            call_params = {
                "model": self.model,
                "messages": [
                    {"role": msg.role, "content": msg.content} 
                    for msg in messages
                ],
                "system": system_prompt if system_prompt else "",
                "temperature": kwargs.get("temperature", self.temperature),
                "max_tokens": kwargs.get("max_tokens", self.max_tokens)
            }
            
            # 移除可能冲突的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ["temperature", "max_tokens"]}
            call_params.update(filtered_kwargs)
            
            response = await self._client.messages.create(**call_params)
            
            return LLMResponse(
                content=response.content[0].text,
                finish_reason=response.stop_reason,
                usage={"input_tokens": response.usage.input_tokens, "output_tokens": response.usage.output_tokens}
            )
        except Exception as e:
            logger.error(f"Anthropic generation error: {e}")
            raise

    async def stream_generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        if not self._client:
            await self.initialize()

        try:
            stream = await self._client.messages.create(
                model=self.model,
                messages=[
                    {"role": msg.role, "content": msg.content} 
                    for msg in messages
                ],
                system=system_prompt if system_prompt else "",
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.type == "content_block_delta" and hasattr(chunk.delta, 'text'):
                    yield chunk.delta.text
                    
        except Exception as e:
            logger.error(f"Anthropic streaming error: {e}")
            raise


class GroqLLMClient(BaseLLMClient):
    """Groq LLM Client with streaming support"""
    
    async def initialize(self) -> None:
        client_params = {
            "api_key": self.api_key,
        }
        
        # 添加自定义base_url支持
        if self.base_url:
            # 修正Groq的base_url，避免重复路径
            if "api.groq.com" in self.base_url and not self.base_url.endswith("/openai/v1"):
                self.base_url = self.base_url.rstrip('/') + "/openai/v1"
            client_params["base_url"] = self.base_url
            logger.info(f"Using custom Groq base URL: {self.base_url}")
        
        # 添加其他自定义参数
        client_params.update(self.kwargs)
        
        self._client = groq.AsyncGroq(**client_params)
        logger.info(f"Initialized Groq client with model: {self.model}")

    async def generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        if not self._client:
            await self.initialize()

        groq_messages = []
        if system_prompt:
            groq_messages.append({"role": "system", "content": system_prompt})
        
        groq_messages.extend([
            {"role": msg.role, "content": msg.content} 
            for msg in messages
        ])

        try:
            # 避免参数冲突，kwargs中的参数优先
            call_params = {
                "model": self.model,
                "messages": groq_messages,
                "temperature": kwargs.get("temperature", self.temperature),
                "max_tokens": kwargs.get("max_tokens", self.max_tokens)
            }
            
            # 移除可能冲突的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ["temperature", "max_tokens"]}
            call_params.update(filtered_kwargs)
            
            response = await self._client.chat.completions.create(**call_params)
            
            return LLMResponse(
                content=response.choices[0].message.content,
                finish_reason=response.choices[0].finish_reason,
                usage=response.usage.dict() if response.usage else None
            )
        except Exception as e:
            logger.error(f"Groq generation error: {e}")
            raise

    async def stream_generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        if not self._client:
            await self.initialize()

        groq_messages = []
        if system_prompt:
            groq_messages.append({"role": "system", "content": system_prompt})
        
        groq_messages.extend([
            {"role": msg.role, "content": msg.content} 
            for msg in messages
        ])

        try:
            stream = await self._client.chat.completions.create(
                model=self.model,
                messages=groq_messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Groq streaming error: {e}")
            raise


class LLMClientFactory:
    """Factory for creating LLM clients"""
    
    @staticmethod
    def create_client(
        provider: Union[str, LLMProvider],
        api_key: str,
        model: str,
        **kwargs
    ) -> BaseLLMClient:
        if isinstance(provider, str):
            provider = LLMProvider(provider)
        
        if provider == LLMProvider.OPENAI:
            return OpenAILLMClient(provider, api_key, model, **kwargs)
        elif provider == LLMProvider.ANTHROPIC:
            return AnthropicLLMClient(provider, api_key, model, **kwargs)
        elif provider == LLMProvider.GROQ:
            return GroqLLMClient(provider, api_key, model, **kwargs)
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")


class LLMManager:
    """Manager for multiple LLM clients with fallback support"""
    
    def __init__(self):
        self.clients: Dict[str, BaseLLMClient] = {}
        self.primary_client: Optional[str] = None
        self.fallback_order: List[str] = []

    async def add_client(self, name: str, client: BaseLLMClient) -> None:
        """Add an LLM client"""
        await client.initialize()
        self.clients[name] = client
        
        if not self.primary_client:
            self.primary_client = name
            
        logger.info(f"Added LLM client: {name}")

    async def set_primary(self, name: str) -> None:
        """Set primary LLM client"""
        if name not in self.clients:
            raise ValueError(f"Client {name} not found")
        self.primary_client = name
        logger.info(f"Set primary LLM client: {name}")

    def set_fallback_order(self, order: List[str]) -> None:
        """Set fallback order for clients"""
        self.fallback_order = [name for name in order if name in self.clients]

    async def generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        client_name: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate response with fallback support"""
        client_order = []
        
        if client_name and client_name in self.clients:
            client_order.append(client_name)
        elif self.primary_client:
            client_order.append(self.primary_client)
            
        client_order.extend(self.fallback_order)
        
        # Remove duplicates while preserving order
        client_order = list(dict.fromkeys(client_order))
        
        for name in client_order:
            if name not in self.clients:
                continue
                
            try:
                logger.info(f"Attempting generation with client: {name}")
                return await self.clients[name].generate(messages, system_prompt, **kwargs)
            except Exception as e:
                logger.warning(f"Client {name} failed: {e}")
                continue
                
        raise RuntimeError("All LLM clients failed to generate response")

    async def stream_generate(
        self,
        messages: List[LLMMessage],
        system_prompt: Optional[str] = None,
        client_name: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Stream generate response with fallback support"""
        client_order = []
        
        if client_name and client_name in self.clients:
            client_order.append(client_name)
        elif self.primary_client:
            client_order.append(self.primary_client)
            
        client_order.extend(self.fallback_order)
        client_order = list(dict.fromkeys(client_order))
        
        for name in client_order:
            if name not in self.clients:
                continue
                
            try:
                logger.info(f"Attempting streaming with client: {name}")
                async for chunk in self.clients[name].stream_generate(messages, system_prompt, **kwargs):
                    yield chunk
                return
            except Exception as e:
                logger.warning(f"Client {name} streaming failed: {e}")
                continue
                
        raise RuntimeError("All LLM clients failed to stream generate")

    async def cleanup(self) -> None:
        """Cleanup all clients"""
        for client in self.clients.values():
            await client.cleanup()
        logger.info("Cleaned up all LLM clients")