"""
Graphiti LLM Client Wrapper
"""
import json
import re
from typing import Optional, Dict, Any, Union, List
from loguru import logger
from pydantic import BaseModel
from graphiti_core.llm_client import LLMClient
from graphiti_core.llm_client.config import ModelSize
from graphiti_core.prompts.models import Message
from .llm_client import LLMManager, LLMMessage

class GraphitiLLM(LLMClient):
    """
    修复后的Graphiti LLM包装器
    专门优化实体提取和结构化响应处理，解决关系建立问题
    """
    def __init__(self, llm_manager: LLMManager):
        super().__init__(config=None)
        self.llm_manager = llm_manager

    @classmethod
    def create(cls, *args, **kwargs):
        """
        Factory method for creating an instance of the LLM client.
        """
        return cls(*args, **kwargs)

    async def generate_response(
        self,
        messages: List[Message],
        response_model: Optional[type[BaseModel]] = None,
        max_tokens: Optional[int] = None,
        model_size: ModelSize = ModelSize.medium,
    ) -> Dict[str, Any]:
        """
        优化的响应生成，专门处理Graphiti的实体提取需求
        修复了结构化响应和JSON格式问题
        """
        try:
            if max_tokens is None:
                max_tokens = 16384  # 增加最大token数以支持更复杂的实体提取

            # 转换消息格式
            llm_messages = []
            for msg in messages:
                role = str(msg.role) if hasattr(msg.role, 'value') else str(msg.role)
                content = str(msg.content)
                llm_messages.append(LLMMessage(role=role, content=content))

            # 如果需要结构化响应（实体提取场景）
            if response_model is not None:
                # 获取响应模型的schema
                schema = response_model.model_json_schema()
                
                # 创建专门的实体提取系统提示
                system_prompt = self._create_entity_extraction_system_prompt(schema)
                
                # 增强用户消息，确保返回正确的JSON格式
                if llm_messages:
                    enhanced_content = self._enhance_content_for_structured_response(
                        llm_messages[-1].content, schema
                    )
                    llm_messages[-1].content = enhanced_content

                # 使用系统提示生成响应
                response = await self.llm_manager.generate(
                    messages=llm_messages,
                    system_prompt=system_prompt,
                    max_tokens=max_tokens,
                    temperature=0.1  # 低温度确保一致性
                )
            else:
                # 普通响应生成
                response = await self.llm_manager.generate(
                    messages=llm_messages,
                    max_tokens=max_tokens,
                    temperature=0.3
                )

            content = response.content.strip()

            # 清理和验证结构化响应
            if response_model is not None:
                content = self._clean_and_validate_json_response(content, response_model)

            return {
                "content": content,
                "finish_reason": response.finish_reason or "stop",
                "usage": response.usage or self._estimate_token_usage(llm_messages, content)
            }

        except Exception as e:
            logger.error(f"GraphitiLLM generate_response error: {e}")
            
            # 智能回退机制
            fallback_content = self._create_intelligent_fallback(response_model, e)
            
            return {
                "content": fallback_content,
                "finish_reason": "error",
                "usage": self._estimate_token_usage(llm_messages if 'llm_messages' in locals() else [], "error")
            }

    def _create_entity_extraction_system_prompt(self, schema: Dict[str, Any]) -> str:
        """创建使用JSON模板的实体提取系统提示"""
        # 根据不同的模型创建不同的JSON模板
        model_name = getattr(schema, '__name__', 'Unknown')
        
        # 创建具体的JSON模板
        json_templates = self._get_json_templates()
        
        return f"""你是专业的实体提取专家。请严格按照JSON模板返回结果。

任务：从文本中提取实体和关系，按模板格式返回JSON数据。

JSON模板 (必须严格遵循):
{json_templates}

关键要求：
1. 只返回JSON数据，不要任何解释文字
2. 不要markdown代码块，直接返回JSON
3. 严格按照模板结构，不要添加或删除字段
4. 如果没有找到实体/关系，返回空数组[]
5. 所有字符串值用双引号包围
6. 确保JSON语法完全正确

返回格式：直接返回符合模板的JSON对象"""

    def _get_json_templates(self) -> str:
        """获取标准JSON模板 - 精确匹配Graphiti要求"""
        return '''
模板1 - 实体提取 (ExtractedEntities):
{
    "extracted_entities": [
        {
            "name": "实体名称",
            "entity_type_id": 1,
            "summary": "实体的简短描述",
            "attributes": {}
        }
    ]
}

模板2 - 关系提取 (ExtractedEdges):  
{
    "edges": [
        {
            "source_node_name": "源实体名称",
            "target_node_name": "目标实体名称",
            "relation_name": "关系类型",
            "relation_type": "RELATES_TO",
            "source_entity_id": 1,
            "target_entity_id": 2,
            "fact": "完整的事实描述",
            "summary": "关系的简短描述",
            "attributes": {}
        }
    ]
}

模板3 - 节点解析 (NodeResolutions):
{
    "entity_resolutions": [
        {
            "id": 1,
            "name": "实体名称",
            "additional_duplicates": [],
            "duplicate_idx": -1
        }
    ]
}'''

    def _enhance_content_for_structured_response(self, content: str, schema: Dict[str, Any]) -> str:
        """使用JSON模板增强内容"""
        # 获取特定的JSON模板
        template = self._get_specific_template(schema)
        
        enhanced_content = f"""文本内容：
{content}

任务：从上述文本提取实体和关系，严格按照以下JSON模板返回：

{template}

要求：
1. 只返回JSON，不要任何其他文字
2. 严格遵循模板格式
3. 如果没有实体/关系，返回空数组[]
4. 确保JSON语法正确

直接返回JSON："""
        
        return enhanced_content

    def _get_specific_template(self, schema: Dict[str, Any]) -> str:
        """根据schema获取特定模板 - 精确匹配Graphiti Pydantic模型"""
        # 尝试从schema中推断模型类型
        if "properties" in schema:
            properties = schema["properties"]
            
            if "extracted_entities" in properties:
                return '''{
    "extracted_entities": [
        {
            "name": "实体名称",
            "entity_type_id": 1,
            "summary": "实体的简短描述",
            "attributes": {}
        }
    ]
}'''
            elif "edges" in properties:
                return '''{
    "edges": [
        {
            "source_node_name": "源实体名称",
            "target_node_name": "目标实体名称", 
            "relation_name": "关系类型",
            "relation_type": "RELATES_TO",
            "source_entity_id": 1,
            "target_entity_id": 2,
            "fact": "完整的事实描述",
            "summary": "关系的简短描述",
            "attributes": {}
        }
    ]
}'''
            elif "entity_resolutions" in properties:
                return '''{
    "entity_resolutions": [
        {
            "id": 1,
            "name": "实体名称",
            "additional_duplicates": [],
            "duplicate_idx": -1
        }
    ]
}'''
        
        # 默认返回通用模板
        return '''{
    "entities": [
        {
            "name": "实体名称",
            "type": "实体类型"
        }
    ],
    "relationships": [
        {
            "source": "源实体",
            "target": "目标实体",
            "relation": "关系类型"
        }
    ]
}'''

    def _clean_and_validate_json_response(self, content: str, response_model: type[BaseModel]) -> str:
        """清理和验证JSON响应"""
        logger.debug(f"Original content from LLM: {content}")
        try:
            # 移除markdown标记
            match = re.search(r"```(json)?\s*(.*?)\s*```", content, re.DOTALL)
            if match:
                content = match.group(2).strip()
            else:
                content = content.strip()
            
            logger.debug(f"Content after markdown removal: {content}")

            # 查找JSON对象
            json_match = self._extract_json_object(content)
            if json_match:
                content = json_match
            
            # 验证JSON是否有效
            parsed_json = json.loads(content)
            logger.debug(f"Parsed JSON: {parsed_json}")
            
            # 预处理：检查是否需要字段映射
            if hasattr(response_model, '__name__'):
                model_name = response_model.__name__
                parsed_json = self._preprocess_json_for_model(parsed_json, model_name)
            
            # 验证是否符合响应模型
            try:
                response_model(**parsed_json)
                logger.debug("JSON response validation successful")
                final_json = json.dumps(parsed_json, ensure_ascii=False)
                logger.debug(f"Final JSON returned to Graphiti: {final_json}")
                return final_json
            except Exception as validation_error:
                logger.warning(f"JSON validation failed: {validation_error}")
                # 如果预处理后仍然失败，使用回退响应
                logger.info("Using fallback response due to validation failure")
                fallback_instance = self._create_fallback_response(response_model)
                return fallback_instance.model_dump_json()
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {e}")
            # 尝试修复JSON
            repaired_content = self._repair_malformed_json(content)
            try:
                json.loads(repaired_content)
                return repaired_content
            except:
                # 最终回退到模型默认实例
                return self._create_fallback_response(response_model).model_dump_json()

    def _preprocess_json_for_model(self, parsed_json: Dict[str, Any], model_name: str) -> Dict[str, Any]:
        """预处理JSON以适配特定的Graphiti模型 - 增强版"""
        # 创建副本避免修改原数据
        processed_json = parsed_json.copy()
        
        if model_name == "ExtractedEntities":
            # 处理实体提取的多种可能格式
            entities = []
            
            # 从各种可能的位置提取实体数据
            if "extracted_entities" in processed_json:
                entities = processed_json.pop("extracted_entities", [])
            elif "entities" in processed_json:
                entities = processed_json.pop("entities", [])
            elif "entity" in processed_json:
                entities = [processed_json.pop("entity", {})]
            else:
                # 如果没有找到任何实体字段，创建空数组
                entities = []
            
            # 确保每个实体都有所有必需字段
            extracted_entities = []
            for i, entity in enumerate(entities):
                if isinstance(entity, dict):
                    entity_obj = {
                        "name": entity.get("name", entity.get("entity_name", entity.get("label", "Unknown"))),
                        "entity_type_id": entity.get("entity_type_id", i + 1),
                        "summary": entity.get("summary", entity.get("description", entity.get("name", "实体描述"))),
                        "attributes": entity.get("attributes", entity.get("metadata", {}))
                    }
                    extracted_entities.append(entity_obj)
            
            processed_json["extracted_entities"] = extracted_entities
            
        elif model_name == "ExtractedEdges":
            # 处理关系提取的多种可能格式
            edges = []

            # 检查是否是直接的列表格式（常见的LLM响应）
            if isinstance(processed_json, list):
                edges = processed_json
                processed_json = {}  # 重置为字典格式
            else:
                # 从各种可能的位置提取关系数据
                if "edges" in processed_json:
                    edges = processed_json.pop("edges", [])
                elif "relationships" in processed_json:
                    edges = processed_json.pop("relationships", [])
                elif "relations" in processed_json:
                    edges = processed_json.pop("relations", [])
                elif "relation" in processed_json:
                    edges = [processed_json.pop("relation", {})]
                else:
                    # 如果没有找到任何关系字段，创建空数组
                    edges = []

            # 确保每个关系都有所有必需字段
            extracted_edges = []
            for edge in edges:
                if isinstance(edge, dict):
                    edge_obj = {
                        "source_node_name": edge.get("source_node_name", edge.get("source", edge.get("from", "Unknown"))),
                        "target_node_name": edge.get("target_node_name", edge.get("target", edge.get("to", "Unknown"))),
                        "relation_name": edge.get("relation_name", edge.get("relation", edge.get("type", "RELATES_TO"))),
                        "relation_type": edge.get("relation_type", edge.get("relation", "RELATES_TO")),
                        "source_entity_id": edge.get("source_entity_id", 1),
                        "target_entity_id": edge.get("target_entity_id", 2),
                        "fact": edge.get("fact", edge.get("relation_name", f"{edge.get('source', '实体')}与{edge.get('target', '实体')}的关系")),
                        "summary": edge.get("summary", edge.get("relation_name", f"{edge.get('source', '实体')}与{edge.get('target', '实体')}的关系")),
                        "attributes": edge.get("attributes", edge.get("metadata", {}))
                    }
                    extracted_edges.append(edge_obj)

            processed_json["edges"] = extracted_edges
            
        elif model_name == "NodeResolutions":
            # 处理节点解析的多种可能格式
            resolutions = []

            # 检查是否是直接的列表格式（常见的LLM响应）
            if isinstance(processed_json, list):
                resolutions = processed_json
                processed_json = {}  # 重置为字典格式
            else:
                # 从各种可能的位置提取解析数据
                if "entity_resolutions" in processed_json:
                    resolutions = processed_json.pop("entity_resolutions", [])
                elif "resolutions" in processed_json:
                    resolutions = processed_json.pop("resolutions", [])
                elif "entities" in processed_json:
                    # 从实体数据创建解析
                    for i, entity in enumerate(processed_json.pop("entities", [])):
                        if isinstance(entity, dict):
                            resolutions.append({
                                "id": entity.get("id", i + 1),
                                "name": entity.get("name", entity.get("label", "Unknown")),
                                "additional_duplicates": entity.get("additional_duplicates", []),
                                "duplicate_idx": entity.get("duplicate_idx", -1)
                            })
                else:
                # 如果没有找到任何解析字段，创建空数组
                resolutions = []
            
            # 确保每个解析都有所有必需字段
            entity_resolutions = []
            for resolution in resolutions:
                if isinstance(resolution, dict):
                    resolution_obj = {
                        "id": resolution.get("id", 1),
                        "name": resolution.get("name", resolution.get("label", "Unknown")),
                        "additional_duplicates": resolution.get("additional_duplicates", []),
                        "duplicate_idx": resolution.get("duplicate_idx", -1)
                    }
                    entity_resolutions.append(resolution_obj)
            
            processed_json["entity_resolutions"] = entity_resolutions
            
        # 清理其他可能的冗余字段
        for key in list(processed_json.keys()):
            if key not in ["extracted_entities", "edges", "entity_resolutions"]:
                processed_json.pop(key, None)
                
        return processed_json

    def _extract_json_object(self, content: str) -> Optional[str]:
        """从内容中提取JSON对象"""
        # 查找第一个 { 和最后一个 }
        start_idx = content.find('{')
        if start_idx == -1:
            return None
            
        brace_count = 0
        end_idx = -1
        
        for i, char in enumerate(content[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break
        
        if end_idx > start_idx:
            return content[start_idx:end_idx]
        
        return None

    def _repair_malformed_json(self, content: str) -> str:
        """修复格式错误的JSON"""
        # 常见JSON修复
        content = content.replace("'", '"')  # 单引号转双引号
        content = re.sub(r',\s*}', '}', content)  # 移除末尾逗号
        content = re.sub(r',\s*]', ']', content)  # 移除数组末尾逗号
        
        # 如果仍然无效，返回空的合法JSON
        try:
            json.loads(content)
            return content
        except:
            return '{"entities": [], "relationships": []}'

    def _repair_json_structure(self, parsed_json: Dict[str, Any], response_model: type[BaseModel]) -> str:
        """修复JSON结构以匹配响应模型 - 精确处理Graphiti字段"""
        try:
            # 获取模型字段
            fields = response_model.model_fields
            repaired_data = {}
            
            # 特殊处理：如果是schema对象而不是实际数据
            if "$defs" in parsed_json or "properties" in parsed_json:
                logger.warning("Received schema instead of data, creating fallback")
                return self._create_fallback_response(response_model).model_dump_json()
            
            # 针对不同的Graphiti模型进行精确修复
            if hasattr(response_model, '__name__'):
                model_name = response_model.__name__
                
                if model_name == "ExtractedEdges":
                    # 精确处理ExtractedEdges模型
                    edges = []
                    
                    # 从各种可能的位置提取关系数据
                    if "edges" in parsed_json:
                        edges = parsed_json["edges"]
                    elif "relationships" in parsed_json:
                        edges = parsed_json["relationships"]
                    elif "relations" in parsed_json:
                        edges = parsed_json["relations"]
                    
                    # 确保每个edge都有所有必需字段
                    repaired_edges = []
                    for edge in edges:
                        if isinstance(edge, dict):
                            repaired_edge = {
                                "source_node_name": edge.get("source_node_name", edge.get("source", "Unknown")),
                                "target_node_name": edge.get("target_node_name", edge.get("target", "Unknown")),
                                "relation_name": edge.get("relation_name", edge.get("relation", "RELATES_TO")),
                                "relation_type": edge.get("relation_type", "RELATES_TO"),
                                "source_entity_id": edge.get("source_entity_id", 1),
                                "target_entity_id": edge.get("target_entity_id", 2),
                                "fact": edge.get("fact", edge.get("relation_name", "关系描述")),
                                "summary": edge.get("summary", edge.get("relation_name", "关系描述")),
                                "attributes": edge.get("attributes", {})
                            }
                            repaired_edges.append(repaired_edge)
                    
                    repaired_data["edges"] = repaired_edges
                    
                elif model_name == "ExtractedEntities":
                    # 精确处理ExtractedEntities模型
                    entities = []
                    
                    # 从各种可能的位置提取实体数据
                    if "extracted_entities" in parsed_json:
                        entities = parsed_json["extracted_entities"]
                    elif "entities" in parsed_json:
                        entities = parsed_json["entities"]
                    
                    # 确保每个实体都有所有必需字段
                    repaired_entities = []
                    for i, entity in enumerate(entities):
                        if isinstance(entity, dict):
                            repaired_entity = {
                                "name": entity.get("name", "Unknown"),
                                "entity_type_id": entity.get("entity_type_id", i + 1),
                                "summary": entity.get("summary", entity.get("name", "实体描述")),
                                "attributes": entity.get("attributes", {})
                            }
                            repaired_entities.append(repaired_entity)
                    
                    repaired_data["extracted_entities"] = repaired_entities
                    
                elif model_name == "NodeResolutions":
                    # 精确处理NodeResolutions模型
                    resolutions = []
                    
                    # 从各种可能的位置提取解析数据
                    if "entity_resolutions" in parsed_json:
                        resolutions = parsed_json["entity_resolutions"]
                    elif "resolutions" in parsed_json:
                        resolutions = parsed_json["resolutions"]
                    elif "entities" in parsed_json:
                        # 从实体数据创建解析
                        for i, entity in enumerate(parsed_json["entities"]):
                            if isinstance(entity, dict):
                                resolutions.append({
                                    "id": entity.get("id", i + 1),
                                    "name": entity.get("name", "Unknown"),
                                    "additional_duplicates": entity.get("additional_duplicates", []),
                                    "duplicate_idx": entity.get("duplicate_idx", -1)
                                })
                    
                    # 确保每个解析都有所有必需字段
                    repaired_resolutions = []
                    for resolution in resolutions:
                        if isinstance(resolution, dict):
                            repaired_resolution = {
                                "id": resolution.get("id", 1),
                                "name": resolution.get("name", "Unknown"),
                                "additional_duplicates": resolution.get("additional_duplicates", []),
                                "duplicate_idx": resolution.get("duplicate_idx", -1)
                            }
                            repaired_resolutions.append(repaired_resolution)
                    
                    repaired_data["entity_resolutions"] = repaired_resolutions
                
                else:
                    # 通用修复逻辑
                    for field_name, field_info in fields.items():
                        if field_name in parsed_json:
                            repaired_data[field_name] = parsed_json[field_name]
                        else:
                            # 添加缺失字段的默认值
                            annotation = field_info.annotation
                            if hasattr(annotation, '__origin__'):
                                if annotation.__origin__ is list:
                                    repaired_data[field_name] = []
                                elif annotation.__origin__ is dict:
                                    repaired_data[field_name] = {}
                                else:
                                    repaired_data[field_name] = None
                            elif annotation == str:
                                repaired_data[field_name] = ""
                            elif annotation == int:
                                repaired_data[field_name] = 0
                            elif annotation == float:
                                repaired_data[field_name] = 0.0
                            elif annotation == bool:
                                repaired_data[field_name] = False
                            else:
                                repaired_data[field_name] = None
            else:
                # 通用修复逻辑（无模型名称时）
                for field_name, field_info in fields.items():
                    if field_name in parsed_json:
                        repaired_data[field_name] = parsed_json[field_name]
                    else:
                        # 添加缺失字段的默认值
                        annotation = field_info.annotation
                        if hasattr(annotation, '__origin__'):
                            if annotation.__origin__ is list:
                                repaired_data[field_name] = []
                            elif annotation.__origin__ is dict:
                                repaired_data[field_name] = {}
                            else:
                                repaired_data[field_name] = None
                        elif annotation == str:
                            repaired_data[field_name] = ""
                        elif annotation == int:
                            repaired_data[field_name] = 0
                        else:
                            repaired_data[field_name] = None
            
            return json.dumps(repaired_data, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"JSON structure repair failed: {e}")
            return self._create_fallback_response(response_model).model_dump_json()

    def _extract_json_from_response(self, content: str) -> str:
        """Extract JSON from response - 兼容性方法"""
        json_match = self._extract_json_object(content)
        return json_match if json_match else content.strip()

    def _create_fallback_response(self, response_model: type[BaseModel]) -> BaseModel:
        """创建回退响应 - 精确匹配Graphiti模型要求"""
        try:
            if hasattr(response_model, '__name__'):
                model_name = response_model.__name__
                
                # 为不同的Graphiti模型创建特定的回退响应
                if model_name == "ExtractedEntities":
                    return response_model(extracted_entities=[])
                elif model_name == "ExtractedEdges":
                    return response_model(edges=[])
                elif model_name == "NodeResolutions":
                    return response_model(entity_resolutions=[])
                elif "Resolution" in model_name:
                    # 通用的Resolution模型
                    fields = response_model.model_fields
                    if "entity_resolutions" in fields:
                        return response_model(entity_resolutions=[])
                    elif "resolutions" in fields:
                        return response_model(resolutions=[])
            
            # 通用回退逻辑
            fields = response_model.model_fields
            fallback_data = {}
            
            for field_name, field_info in fields.items():
                annotation = field_info.annotation
                
                # 处理不同类型的字段
                if hasattr(annotation, '__origin__'):
                    if annotation.__origin__ is list:
                        # 针对Graphiti模型的特殊处理
                        if field_name == "extracted_entities":
                            fallback_data[field_name] = []
                        elif field_name == "edges":
                            fallback_data[field_name] = []
                        elif field_name == "entity_resolutions":
                            fallback_data[field_name] = []
                        else:
                            fallback_data[field_name] = []
                    elif annotation.__origin__ is dict:
                        fallback_data[field_name] = {}
                    else:
                        fallback_data[field_name] = None
                elif annotation == str:
                    fallback_data[field_name] = ""
                elif annotation == int:
                    fallback_data[field_name] = 0
                elif annotation == float:
                    fallback_data[field_name] = 0.0
                elif annotation == bool:
                    fallback_data[field_name] = False
                else:
                    fallback_data[field_name] = None
            
            return response_model(**fallback_data)
            
        except Exception as e:
            logger.error(f"Fallback response creation failed: {e}")
            # 最后的回退：创建空实例
            try:
                return response_model()
            except:
                # 如果连空实例都创建不了，返回一个基本的对象
                class EmptyModel(BaseModel):
                    pass
                return EmptyModel()

    def _create_intelligent_fallback(self, response_model: Optional[type[BaseModel]], error: Exception) -> str:
        """创建智能回退内容"""
        if response_model is not None:
            # 结构化响应的回退
            fallback_instance = self._create_fallback_response(response_model)
            return fallback_instance.model_dump_json()
        else:
            # 普通响应的回退
            return f"抱歉，生成响应时出现问题。错误信息：{str(error)[:100]}"

    def _estimate_token_usage(self, messages: List[LLMMessage], response_content: str) -> Dict[str, int]:
        """估算token使用量"""
        prompt_tokens = sum(len(msg.content) for msg in messages) // 4
        completion_tokens = len(response_content) // 4
        total_tokens = prompt_tokens + completion_tokens
        
        return {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens
        }

    async def _generate_response(
        self, 
        messages: Union[List, str], 
        response_model=None, 
        max_tokens: int = 8192, 
        model_size=None
    ) -> Dict[str, Any]:
        """
        生成符合Graphiti期望格式的响应
        """
        try:
            # 转换消息格式
            llm_messages = []
            
            if isinstance(messages, list):
                for msg in messages:
                    if hasattr(msg, 'content') and hasattr(msg, 'role'):
                        # Graphiti Message对象
                        role = str(msg.role.value) if hasattr(msg.role, 'value') else str(msg.role)
                        content = str(msg.content)
                        llm_messages.append(LLMMessage(role=role, content=content))
                    elif isinstance(msg, dict):
                        # 字典格式消息
                        llm_messages.append(LLMMessage(
                            role=msg.get('role', 'user'),
                            content=str(msg.get('content', ''))
                        ))
                    else:
                        # 字符串消息
                        llm_messages.append(LLMMessage(role="user", content=str(msg)))
            else:
                # 单个消息
                llm_messages.append(LLMMessage(role="user", content=str(messages)))
            
            # 调用LLM生成响应
            response = await self.llm_manager.generate(
                messages=llm_messages,
                max_tokens=max_tokens,
                temperature=0.3
            )
            
            # 返回Graphiti期望的字典格式
            return {
                "content": response.content,
                "finish_reason": response.finish_reason or "stop",
                "usage": response.usage or {
                    "prompt_tokens": sum(len(msg.content) for msg in llm_messages) // 4,
                    "completion_tokens": len(response.content) // 4,
                    "total_tokens": (sum(len(msg.content) for msg in llm_messages) + len(response.content)) // 4
                }
            }
            
        except Exception as e:
            logger.error(f"GraphitiLLM生成错误: {e}")
            # 返回错误回退响应
            return {
                "content": f"由于技术问题，生成了回退响应: {str(e)[:100]}",
                "finish_reason": "error",
                "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}
            }