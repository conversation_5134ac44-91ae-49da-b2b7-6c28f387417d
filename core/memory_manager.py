"""
Graphiti Memory Management System
基于Graphiti文档的完整记忆管理实现
"""
import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.search.search_config_recipes import (
    NODE_HYBRID_SEARCH_EPISODE_MENTIONS,
    NODE_HYBRID_SEARCH_RRF
)
from loguru import logger

from .llm_client import LLMManager, LLMMessage
from .embedding_client import BaseEmbeddingClient
from .graphiti_embedder import GraphitiEmbedder
from .graphiti_llm import GraphitiLLM


class MemoryType(Enum):
    """记忆类型枚举 - 基于Graphiti文档的分类"""
    USER_PREFERENCE = "Preference"  # 用户偏好
    TASK_PATTERN = "Procedure"      # 任务模式/程序
    EXECUTION_RESULT = "Fact"       # 执行结果作为事实
    ERROR_CASE = "Requirement"      # 错误案例作为需求改进
    CONVERSATION = "Fact"           # 对话记录
    FACT = "Fact"                   # 基本事实
    REQUIREMENT = "Requirement"     # 需求信息


@dataclass
class MemoryContext:
    """记忆上下文 - 增强版本支持更多元数据"""
    user_id: str
    session_id: str
    memory_type: MemoryType
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    relevance_score: Optional[float] = None
    category: Optional[str] = None  # 分类标签
    update_of_uuid: Optional[str] = None  # 更新的记忆UUID


@dataclass
class RetrievalContext:
    """检索上下文 - 支持更复杂的查询"""
    query: str
    memory_types: List[MemoryType]
    max_results: int = 10
    similarity_threshold: float = 0.7
    time_weight: float = 0.1
    entity_type: Optional[str] = None  # 实体类型过滤
    center_node_uuid: Optional[str] = None  # 中心节点UUID


class GraphitiMemoryManager:
    """
    基于Graphiti知识图谱的高级记忆管理系统
    实现了Graphiti文档中的所有MCP工具功能
    """
    
    def __init__(
        self,
        neo4j_uri: str,
        neo4j_user: str,
        neo4j_password: str,
        llm_manager: LLMManager,
        embedding_client: BaseEmbeddingClient,
        database_name: str = "graphiti_memory",
        group_id: Optional[str] = None
    ):
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.database_name = database_name
        self.llm_manager = llm_manager
        self.embedding_client = embedding_client
        self.group_id = group_id or "default"  # 支持分组管理
        
        self.client: Optional[Graphiti] = None
        self.user_nodes: Dict[str, str] = {}  # user_id -> node_uuid
        self.session_nodes: Dict[str, str] = {}  # session_id -> node_uuid
        self.memory_cache: Dict[str, str] = {}  # 记忆缓存

    async def initialize(self) -> None:
        """Initialize Graphiti client and database"""
        try:
            graphiti_embedder = GraphitiEmbedder(self.embedding_client)
            graphiti_llm = GraphitiLLM(self.llm_manager)
            # Handle empty credentials (no auth mode)
            if not self.neo4j_user and not self.neo4j_password:
                self.client = Graphiti(
                    uri=self.neo4j_uri,
                    user="neo4j",
                    password="00000000",
                    llm_client=graphiti_llm,
                    embedder=graphiti_embedder,
                    store_raw_episode_content=True
                )
            else:
                self.client = Graphiti(
                    uri=self.neo4j_uri,
                    user=self.neo4j_user,
                    password=self.neo4j_password,
                    llm_client=graphiti_llm,
                    embedder=graphiti_embedder,
                    store_raw_episode_content=True
                )
            
            # Build indices and constraints
            await self.client.build_indices_and_constraints()
            
            logger.info("Initialized Graphiti memory manager")
            
        except Exception as e:
            logger.error(f"Failed to initialize Graphiti: {e}")
            raise

    async def create_user_context(self, user_id: str) -> str:
        """Create or get user context node"""
        if user_id in self.user_nodes:
            return self.user_nodes[user_id]
        
        if not self.client:
            await self.initialize()
            
        try:
            # Create user initialization episode
            episode_name = f"User_{user_id}_initialization"
            episode_body = f"A new user is registered with the User ID: {user_id}. This user will now interact with the system."
            
            await self.client.add_episode(
                name=episode_name,
                episode_body=episode_body,
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="User initialization"
            )
            
            # Wait for processing
            await asyncio.sleep(2)
            
            # Search for user node
            search_result = await self.client._search(
                user_id,
                NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            )
            
            if search_result.nodes:
                user_node_uuid = search_result.nodes[0].uuid
                self.user_nodes[user_id] = user_node_uuid
                logger.info(f"Created user context node: {user_node_uuid}")
                return user_node_uuid
            else:
                raise RuntimeError(f"Failed to create user context for {user_id}")
                
        except Exception as e:
            logger.error(f"Error creating user context: {e}")
            raise

    async def create_session_context(self, user_id: str, session_id: str) -> str:
        """Create session context node"""
        if session_id in self.session_nodes:
            return self.session_nodes[session_id]
            
        if not self.client:
            await self.initialize()
        
        try:
            # Ensure user context exists
            await self.create_user_context(user_id)
            
            # Create session episode
            episode_name = f"Session_{session_id}_start"
            episode_body = f"User {user_id} started session {session_id}"
            
            await self.client.add_episode(
                name=episode_name,
                episode_body=episode_body,
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="Session initialization"
            )
            
            await asyncio.sleep(0.5)
            
            # Store session node reference
            self.session_nodes[session_id] = session_id
            logger.info(f"Created session context: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session context: {e}")
            raise

    async def store_memory(self, memory_context: MemoryContext) -> str:
        """Store memory in knowledge graph"""
        if not self.client:
            await self.initialize()
            
        try:
            # Ensure user and session contexts exist
            await self.create_user_context(memory_context.user_id)
            await self.create_session_context(memory_context.user_id, memory_context.session_id)
            
            # Create episode based on memory type
            episode_name = f"{memory_context.memory_type.value}_{uuid.uuid4().hex[:8]}"
            
            # Enhance content with AI analysis
            enhanced_content = await self._enhance_memory_content(memory_context)
            
            episode_body = json.dumps({
                "user_id": memory_context.user_id,
                "session_id": memory_context.session_id,
                "type": memory_context.memory_type.value,
                "content": enhanced_content,
                "metadata": memory_context.metadata,
                "original_timestamp": memory_context.timestamp.isoformat()
            })
            
            await self.client.add_episode(
                name=episode_name,
                episode_body=episode_body,
                source=self._get_episode_source(memory_context.memory_type),
                reference_time=memory_context.timestamp,
                source_description=f"Memory: {memory_context.memory_type.value}"
            )
            
            logger.debug(f"Stored memory: {memory_context.memory_type.value}")
            return episode_name
            
        except Exception as e:
            logger.error(f"Failed to store memory: {e}")
            raise

    async def _enhance_memory_content(self, memory_context: MemoryContext) -> str:
        """Enhance memory content with AI analysis"""
        try:
            system_prompt = f"""
Analyze and enhance the following memory for better storage and retrieval:

Memory Type: {memory_context.memory_type.value}
Original Content: {memory_context.content}
Metadata: {json.dumps(memory_context.metadata, indent=2)}

Please:
1. Extract key concepts and entities
2. Identify relationships and patterns
3. Add semantic tags for better retrieval
4. Maintain the original meaning while making it more structured

Return enhanced content that preserves the original information but adds semantic richness.
"""

            messages = [LLMMessage(role="user", content=memory_context.content)]
            
            response = await self.llm_manager.generate(
                messages=messages,
                system_prompt=system_prompt,
                temperature=0.3
            )
            
            return response.content
            
        except Exception as e:
            logger.warning(f"Failed to enhance memory content: {e}")
            return memory_context.content

    def _get_episode_source(self, memory_type: MemoryType) -> EpisodeType:
        """Map memory type to episode source"""
        mapping = {
            MemoryType.USER_PREFERENCE: EpisodeType.text,
            MemoryType.TASK_PATTERN: EpisodeType.json,
            MemoryType.EXECUTION_RESULT: EpisodeType.json,
            MemoryType.ERROR_CASE: EpisodeType.event,
            MemoryType.CONVERSATION: EpisodeType.message,
            MemoryType.FACT: EpisodeType.text
        }
        return mapping.get(memory_type, EpisodeType.text)

    async def retrieve_memories(
        self,
        retrieval_context: RetrievalContext,
        user_id: str,
        session_id: Optional[str] = None
    ) -> List[MemoryContext]:
        """Retrieve relevant memories from knowledge graph"""
        if not self.client:
            await self.initialize()
            
        try:
            # Get user context
            user_node_uuid = await self.create_user_context(user_id)
            
            # Perform context-aware search
            search_results = await self.client.search(
                query=retrieval_context.query,
                center_node_uuid=user_node_uuid,
                num_results=retrieval_context.max_results
            )
            
            memories = []
            
            for edge in search_results:
                try:
                    # Parse episode content
                    episode_data = json.loads(edge.fact)
                    
                    memory_type_str = episode_data.get("type", "fact")
                    memory_type = MemoryType(memory_type_str)
                    
                    # Filter by memory types if specified
                    if (retrieval_context.memory_types and 
                        memory_type not in retrieval_context.memory_types):
                        continue
                    
                    memory = MemoryContext(
                        user_id=episode_data.get("user_id", user_id),
                        session_id=episode_data.get("session_id", session_id or "unknown"),
                        memory_type=memory_type,
                        content=episode_data.get("content", edge.fact),
                        metadata=episode_data.get("metadata", {}),
                        timestamp=datetime.fromisoformat(
                            episode_data.get("original_timestamp", datetime.now().isoformat())
                        ),
                        relevance_score=getattr(edge, 'score', 0.0)
                    )
                    memories.append(memory)
                    
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    logger.debug(f"Skipped unparseable memory: {e}")
                    continue
            
            # Apply similarity threshold
            filtered_memories = [
                mem for mem in memories 
                if mem.relevance_score is None or mem.relevance_score >= retrieval_context.similarity_threshold
            ]
            
            # Sort by relevance and time
            filtered_memories.sort(
                key=lambda m: (
                    m.relevance_score or 0.0,
                    m.timestamp.timestamp() * retrieval_context.time_weight
                ),
                reverse=True
            )
            
            logger.debug(f"Retrieved {len(filtered_memories)} relevant memories")
            return filtered_memories
            
        except Exception as e:
            logger.error(f"Failed to retrieve memories: {e}")
            return []

    async def store_conversation(
        self,
        user_id: str,
        session_id: str,
        user_input: str,
        assistant_response: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store conversation exchange"""
        conversation_content = {
            "user_input": user_input,
            "assistant_response": assistant_response,
            "exchange_type": "qa_pair"
        }
        
        memory_context = MemoryContext(
            user_id=user_id,
            session_id=session_id,
            memory_type=MemoryType.CONVERSATION,
            content=json.dumps(conversation_content),
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc)
        )
        
        return await self.store_memory(memory_context)

    async def store_execution_pattern(
        self,
        user_id: str,
        session_id: str,
        task_description: str,
        execution_plan: Dict[str, Any],
        results: Dict[str, Any],
        success: bool,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store task execution pattern"""
        pattern_content = {
            "task": task_description,
            "execution_plan": execution_plan,
            "results": results,
            "success": success,
            "pattern_type": "task_execution"
        }
        
        memory_type = MemoryType.TASK_PATTERN if success else MemoryType.ERROR_CASE
        
        memory_context = MemoryContext(
            user_id=user_id,
            session_id=session_id,
            memory_type=memory_type,
            content=json.dumps(pattern_content),
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc)
        )
        
        return await self.store_memory(memory_context)

    async def store_user_preference(
        self,
        user_id: str,
        session_id: str,
        preference_type: str,
        preference_value: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store user preference"""
        preference_content = {
            "type": preference_type,
            "value": preference_value,
            "preference_category": "user_setting"
        }
        
        memory_context = MemoryContext(
            user_id=user_id,
            session_id=session_id,
            memory_type=MemoryType.USER_PREFERENCE,
            content=json.dumps(preference_content),
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc)
        )
        
        return await self.store_memory(memory_context)

    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Generate user profile from memories"""
        try:
            # Retrieve various types of memories
            preference_context = RetrievalContext(
                query=f"user {user_id} preferences settings",
                memory_types=[MemoryType.USER_PREFERENCE],
                max_results=20
            )
            preferences = await self.retrieve_memories(preference_context, user_id)
            
            pattern_context = RetrievalContext(
                query=f"user {user_id} successful tasks patterns",
                memory_types=[MemoryType.TASK_PATTERN],
                max_results=10
            )
            patterns = await self.retrieve_memories(pattern_context, user_id)
            
            # Use LLM to generate profile
            system_prompt = """
Generate a comprehensive user profile based on stored memories.
Include preferences, common patterns, and behavioral insights.
Format as JSON with clear categories.
"""
            
            memory_summary = {
                "preferences": [mem.content for mem in preferences[:5]],
                "successful_patterns": [mem.content for mem in patterns[:5]]
            }
            
            messages = [LLMMessage(
                role="user", 
                content=f"Generate user profile from: {json.dumps(memory_summary, indent=2)}"
            )]
            
            response = await self.llm_manager.generate(
                messages=messages,
                system_prompt=system_prompt,
                temperature=0.1
            )
            
            return json.loads(response.content)
            
        except Exception as e:
            logger.error(f"Failed to generate user profile: {e}")
            return {"error": str(e)}

    # === MCP工具方法 - 基于Graphiti文档 ===
    
    async def add_memory(
        self, 
        memory_type: str, 
        content: str, 
        category: Optional[str] = None,
        update_of_uuid: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        MCP工具: add_memory - 添加或更新记忆到知识图谱
        基于Graphiti文档的最佳实践实现
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 转换memory_type为标准类型
            memory_type_enum = self._map_memory_type(memory_type)
            
            # 创建增强的记忆内容
            enhanced_content = await self._enhance_memory_for_graphiti(
                content, memory_type, category
            )
            
            # 确定Episode类型
            episode_type = self._get_episode_type_for_memory(memory_type)
            
            # 生成记忆名称
            memory_name = self._generate_memory_name(memory_type, category)
            
            # 添加到Graphiti
            await self.client.add_episode(
                name=memory_name,
                episode_body=enhanced_content,
                source=episode_type,
                reference_time=datetime.now(timezone.utc),
                source_description=f"{memory_type}:{category or 'general'}",
                group_id=self.group_id
            )
            
            # 缓存记忆UUID
            memory_uuid = str(uuid.uuid4())
            self.memory_cache[memory_uuid] = enhanced_content
            
            logger.info(f"Added memory: {memory_type} - {category or 'general'}")
            return memory_uuid
            
        except Exception as e:
            logger.error(f"Failed to add memory: {e}")
            raise
    
    async def search_nodes(
        self,
        query: str,
        entity_type: Optional[str] = None,
        center_node_uuid: Optional[str] = None,
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        MCP工具: search_nodes - 搜索知识图谱中的节点
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 使用适当的搜索配方
            if entity_type:
                # 按实体类型搜索
                search_results = await self.client._search(
                    query,
                    NODE_HYBRID_SEARCH_RRF if entity_type else NODE_HYBRID_SEARCH_EPISODE_MENTIONS
                )
            else:
                # 通用搜索
                if center_node_uuid:
                    # 以特定节点为中心搜索
                    edge_results = await self.client.search(
                        query=query,
                        center_node_uuid=center_node_uuid,
                        num_results=max_results
                    )
                    return [{"fact": edge.fact, "relevance": getattr(edge, 'score', 0.0)} 
                           for edge in edge_results]
                else:
                    # 全局搜索
                    search_results = await self.client._search(query, NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
            
            # 格式化结果
            nodes = []
            if hasattr(search_results, 'nodes'):
                for node in search_results.nodes[:max_results]:
                    nodes.append({
                        "uuid": node.uuid,
                        "name": getattr(node, 'name', 'Unknown'),
                        "type": getattr(node, 'type', 'Entity'),
                        "summary": getattr(node, 'summary', '')
                    })
            
            return nodes
            
        except Exception as e:
            logger.error(f"Failed to search nodes: {e}")
            return []
    
    async def search_facts(
        self,
        query: str,
        center_node_uuid: Optional[str] = None,
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        MCP工具: search_facts - 搜索知识图谱中的事实关系
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 搜索事实关系
            edge_results = await self.client.search(
                query=query,
                center_node_uuid=center_node_uuid,
                num_results=max_results
            )
            
            facts = []
            for edge in edge_results:
                facts.append({
                    "fact": edge.fact,
                    "relevance_score": getattr(edge, 'score', 0.0),
                    "source_uuid": getattr(edge, 'source_uuid', ''),
                    "target_uuid": getattr(edge, 'target_uuid', '')
                })
            
            return facts
            
        except Exception as e:
            logger.error(f"Failed to search facts: {e}")
            return []
    
    async def get_episodes(
        self,
        group_id: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        MCP工具: get_episodes - 获取最近的episodes
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 这里需要直接查询数据库获取episodes
            from neo4j import AsyncGraphDatabase
            
            # 获取实际的数据库连接信息
            uri = self.neo4j_uri
            user = self.neo4j_user if self.neo4j_user else "neo4j"
            password = self.neo4j_password if self.neo4j_password else "00000000"
            
            driver = AsyncGraphDatabase.driver(uri, auth=(user, password))
            
            async with driver.session() as session:
                # 查询最近的Episodic节点
                query = """
                MATCH (e:Episodic)
                WHERE e.group_id = $group_id OR $group_id IS NULL
                RETURN e.name as name, e.content as content, e.created_at as created_at
                ORDER BY e.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, {
                    "group_id": group_id or self.group_id,
                    "limit": limit
                })
                
                episodes = []
                async for record in result:
                    episodes.append({
                        "name": record.get("name", ""),
                        "content": record.get("content", ""),
                        "created_at": record.get("created_at", "")
                    })
                
                await driver.close()
                return episodes
                
        except Exception as e:
            logger.error(f"Failed to get episodes: {e}")
            return []
    
    async def clear_graph(self) -> bool:
        """
        MCP工具: clear_graph - 清理图谱数据并重建索引
        """
        if not self.client:
            await self.initialize()
            
        try:
            from graphiti_core.utils.maintenance.graph_data_operations import clear_data
            
            await clear_data(self.client.driver)
            await self.client.build_indices_and_constraints()
            
            # 清理缓存
            self.user_nodes.clear()
            self.session_nodes.clear()
            self.memory_cache.clear()
            
            logger.info("Graph cleared and indices rebuilt")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear graph: {e}")
            return False
    
    async def get_status(self) -> Dict[str, Any]:
        """
        MCP工具: get_status - 获取Graphiti服务状态
        """
        try:
            status = {
                "graphiti_initialized": self.client is not None,
                "neo4j_connection": False,
                "user_nodes_cached": len(self.user_nodes),
                "session_nodes_cached": len(self.session_nodes),
                "memory_cache_size": len(self.memory_cache),
                "group_id": self.group_id
            }
            
            # 测试Neo4j连接
            if self.client:
                try:
                    from neo4j import AsyncGraphDatabase
                    
                    uri = self.neo4j_uri
                    user = self.neo4j_user if self.neo4j_user else "neo4j"
                    password = self.neo4j_password if self.neo4j_password else "00000000"
                    
                    driver = AsyncGraphDatabase.driver(uri, auth=(user, password))
                    async with driver.session() as session:
                        await session.run("RETURN 1")
                    await driver.close()
                    status["neo4j_connection"] = True
                except:
                    status["neo4j_connection"] = False
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get status: {e}")
            return {"error": str(e)}
    
    # === 辅助方法 ===
    
    def _map_memory_type(self, memory_type: str) -> MemoryType:
        """映射字符串类型到MemoryType枚举"""
        type_mapping = {
            "preference": MemoryType.USER_PREFERENCE,
            "procedure": MemoryType.TASK_PATTERN,
            "fact": MemoryType.FACT,
            "requirement": MemoryType.REQUIREMENT,
            "conversation": MemoryType.CONVERSATION,
            "execution": MemoryType.EXECUTION_RESULT,
            "error": MemoryType.ERROR_CASE
        }
        
        return type_mapping.get(memory_type.lower(), MemoryType.FACT)
    
    def _get_episode_type_for_memory(self, memory_type: str) -> EpisodeType:
        """根据记忆类型确定Episode类型"""
        type_mapping = {
            "preference": EpisodeType.text,
            "procedure": EpisodeType.json,
            "fact": EpisodeType.text,
            "requirement": EpisodeType.text,
            "conversation": EpisodeType.message,
            "execution": EpisodeType.json,
            "error": EpisodeType.text  # 修复：使用text代替不存在的event
        }
        
        return type_mapping.get(memory_type.lower(), EpisodeType.text)
    
    def _generate_memory_name(self, memory_type: str, category: Optional[str] = None) -> str:
        """生成记忆名称"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if category:
            return f"{memory_type}_{category}_{timestamp}"
        else:
            return f"{memory_type}_{timestamp}"
    
    async def _enhance_memory_for_graphiti(
        self, 
        content: str, 
        memory_type: str, 
        category: Optional[str] = None
    ) -> str:
        """为Graphiti优化记忆内容"""
        try:
            # 基于文档的内容增强提示
            if memory_type.lower() == "preference":
                prompt = f"""
请将以下用户偏好信息转换为适合知识图谱存储的格式：

原始偏好: {content}
分类: {category or '通用'}

请返回增强后的偏好描述，包含：
1. 明确的偏好主体
2. 具体的偏好内容
3. 适用场景
4. 重要级别

格式化为清晰的描述性文本。
"""
            elif memory_type.lower() == "procedure":
                prompt = f"""
请将以下过程信息转换为适合知识图谱存储的程序描述：

原始过程: {content}
分类: {category or '通用'}

请返回结构化的程序描述，包含：
1. 程序目标
2. 执行步骤
3. 前置条件
4. 预期结果

使用JSON格式返回。
"""
            else:
                prompt = f"""
请将以下信息转换为适合知识图谱存储的事实描述：

原始信息: {content}
类型: {memory_type}
分类: {category or '通用'}

请返回清晰、结构化的事实描述，便于实体提取和关系建立。
"""
            
            messages = [LLMMessage(role="user", content=prompt)]
            response = await self.llm_manager.generate(
                messages=messages,
                temperature=0.3
            )
            
            return response.content
            
        except Exception as e:
            logger.warning(f"Failed to enhance memory content: {e}")
            return content

    # === 对话管理范式 - 基于Graphiti文档 ===
    
    async def create_user_node(self, user_name: str) -> str:
        """
        创建用户节点 - 模拟Graphiti文档中的用户管理模式
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 创建用户初始化episode
            await self.client.add_episode(
                name='User Creation',
                episode_body=f'{user_name} is interested in using the financial analysis system',
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description='User Management System',
                group_id=self.group_id
            )
            
            # 等待处理
            await asyncio.sleep(1)
            
            # 搜索用户节点
            nl = await self.client._search(user_name, NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
            if nl.nodes:
                user_node_uuid = nl.nodes[0].uuid
                self.user_nodes[user_name] = user_node_uuid
                logger.info(f"Created user node for {user_name}: {user_node_uuid}")
                return user_node_uuid
            else:
                raise RuntimeError(f"Failed to create user node for {user_name}")
                
        except Exception as e:
            logger.error(f"Failed to create user node: {e}")
            raise
    
    async def add_conversation_episode(
        self, 
        user_name: str, 
        user_input: str, 
        assistant_response: str,
        context_metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        添加对话episode - 基于Graphiti文档的对话管理模式
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 确保用户节点存在
            if user_name not in self.user_nodes:
                await self.create_user_node(user_name)
            
            # 构建对话内容 - 模拟Graphiti文档中的格式
            conversation_body = f"""{user_name}: {user_input}
Assistant: {assistant_response}"""
            
            # 添加episode
            episode_name = f"Conversation_{user_name}_{datetime.now().strftime('%H%M%S')}"
            await self.client.add_episode(
                name=episode_name,
                episode_body=conversation_body,
                source=EpisodeType.message,
                reference_time=datetime.now(timezone.utc),
                source_description='Financial Analysis Conversation',
                group_id=self.group_id
            )
            
            logger.info(f"Added conversation episode for {user_name}")
            return episode_name
            
        except Exception as e:
            logger.error(f"Failed to add conversation episode: {e}")
            raise
    
    async def search_user_context(
        self, 
        user_name: str, 
        query: str, 
        num_results: int = 5
    ) -> List[Dict[str, Any]]:
        """
        搜索用户上下文 - 模拟Graphiti文档中的上下文检索模式
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 获取用户节点UUID
            user_node_uuid = self.user_nodes.get(user_name)
            if not user_node_uuid:
                # 尝试搜索用户节点
                nl = await self.client._search(user_name, NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
                if nl.nodes:
                    user_node_uuid = nl.nodes[0].uuid
                    self.user_nodes[user_name] = user_node_uuid
                else:
                    logger.warning(f"User node not found for {user_name}")
                    return []
            
            # 以用户为中心进行搜索 - 模拟文档中的center_node_uuid模式
            edge_results = await self.client.search(
                query=query,
                center_node_uuid=user_node_uuid,
                num_results=num_results
            )
            
            # 格式化结果
            context_results = []
            for edge in edge_results:
                context_results.append({
                    "fact": edge.fact,
                    "relevance_score": getattr(edge, 'score', 0.0),
                    "type": "user_context"
                })
            
            return context_results
            
        except Exception as e:
            logger.error(f"Failed to search user context: {e}")
            return []
    
    async def build_conversation_context(
        self, 
        user_name: str, 
        current_query: str
    ) -> str:
        """
        构建对话上下文 - 模拟Graphiti文档中的上下文构建模式
        """
        try:
            # 搜索相关上下文
            context_results = await self.search_user_context(user_name, current_query, num_results=5)
            
            if not context_results:
                return "No previous conversation context found"
            
            # 构建上下文字符串 - 模拟文档中的facts格式
            context_facts = []
            for result in context_results:
                fact = result.get("fact", "")
                if fact:
                    context_facts.append(f"- {fact}")
            
            context_string = f"""Previous conversation context for {user_name}:
{chr(10).join(context_facts)}"""
            
            return context_string
            
        except Exception as e:
            logger.error(f"Failed to build conversation context: {e}")
            return "Error building conversation context"
    
    async def store_user_preference_advanced(
        self,
        user_name: str,
        preference_type: str,
        preference_value: str,
        category: Optional[str] = None
    ) -> str:
        """
        存储用户偏好 - 基于Graphiti文档的偏好管理模式
        """
        if not self.client:
            await self.initialize()
            
        try:
            # 确保用户节点存在
            if user_name not in self.user_nodes:
                await self.create_user_node(user_name)
            
            # 构建偏好内容 - 模拟文档中的偏好格式
            preference_content = f"{user_name} prefers {preference_type}: {preference_value}"
            if category:
                preference_content += f" (Category: {category})"
            
            # 使用MCP工具格式存储
            await self.add_memory(
                memory_type="preference",
                content=preference_content,
                category=category or preference_type,
                user_id=user_name,
                session_id=self.group_id
            )
            
            logger.info(f"Stored preference for {user_name}: {preference_type}")
            return f"preference_{preference_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
        except Exception as e:
            logger.error(f"Failed to store user preference: {e}")
            raise
    
    async def get_user_conversation_history(
        self, 
        user_name: str, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取用户对话历史 - 模拟Graphiti文档中的历史检索模式
        """
        try:
            # 搜索用户相关的对话
            conversation_query = f"{user_name} conversation history"
            search_results = await self.search_facts(
                query=conversation_query,
                center_node_uuid=self.user_nodes.get(user_name),
                max_results=limit
            )
            
            # 过滤和格式化对话历史
            conversations = []
            for result in search_results:
                fact = result.get("fact", "")
                if user_name in fact and (":" in fact):  # 简单的对话格式检测
                    conversations.append({
                        "content": fact,
                        "relevance": result.get("relevance_score", 0.0),
                        "type": "conversation"
                    })
            
            # 按相关性排序
            conversations.sort(key=lambda x: x["relevance"], reverse=True)
            
            return conversations[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []
    
    async def analyze_user_patterns(self, user_name: str) -> Dict[str, Any]:
        """
        分析用户模式 - 基于Graphiti文档的模式分析
        """
        try:
            # 搜索用户相关的所有信息
            user_context = await self.search_user_context(
                user_name, 
                f"{user_name} patterns preferences behavior", 
                num_results=20
            )
            
            # 统计分析
            patterns = {
                "total_interactions": len(user_context),
                "preference_patterns": [],
                "common_topics": [],
                "interaction_quality": 0.0
            }
            
            # 分析偏好模式
            preference_facts = [ctx for ctx in user_context if "prefer" in ctx.get("fact", "").lower()]
            patterns["preference_patterns"] = [fact.get("fact", "") for fact in preference_facts[:5]]
            
            # 计算平均交互质量
            if user_context:
                avg_relevance = sum(ctx.get("relevance_score", 0.0) for ctx in user_context) / len(user_context)
                patterns["interaction_quality"] = avg_relevance
            
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to analyze user patterns: {e}")
            return {"error": str(e)}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        if self.client:
            # Graphiti doesn't require explicit cleanup
            logger.info("Graphiti memory manager cleaned up")