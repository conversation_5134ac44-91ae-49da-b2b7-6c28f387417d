"""
Embedding Client for Vector Representations
"""
import asyncio
import os
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import numpy as np
from dataclasses import dataclass

import httpx
from openai import AsyncOpenAI
from sentence_transformers import SentenceTransformer
from loguru import logger


@dataclass
class EmbeddingResponse:
    embeddings: List[List[float]]
    model: str
    usage: Optional[Dict] = None


class BaseEmbeddingClient(ABC):
    """Base class for embedding clients"""
    
    def __init__(
        self,
        model: str,
        dimension: Optional[int] = None,
        **kwargs
    ):
        self.model = model
        self.dimension = dimension
        self.kwargs = kwargs
        self._client = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the embedding client"""
        pass

    @abstractmethod
    async def encode(
        self,
        texts: Union[str, List[str]],
        **kwargs
    ) -> EmbeddingResponse:
        """Encode texts into embeddings"""
        pass

    async def encode_single(self, text: str, **kwargs) -> List[float]:
        """Encode a single text into embedding"""
        response = await self.encode([text], **kwargs)
        return response.embeddings[0]

    async def encode_batch(
        self,
        texts: List[str],
        batch_size: int = 100,
        **kwargs
    ) -> List[List[float]]:
        """Encode texts in batches"""
        all_embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            response = await self.encode(batch, **kwargs)
            all_embeddings.extend(response.embeddings)
            
        return all_embeddings

    async def cleanup(self) -> None:
        """Cleanup resources"""
        if self._client is not None and hasattr(self._client, 'close'):
            close_method = getattr(self._client, 'close', None)
            if close_method is not None:
                await close_method()


class OpenAIEmbeddingClient(BaseEmbeddingClient):
    """OpenAI Embedding Client"""
    
    def __init__(self, api_key: str, model: str = "text-embedding-ada-002", **kwargs):
        super().__init__(model, **kwargs)
        self.api_key = api_key

    async def initialize(self) -> None:
        self._client = AsyncOpenAI(api_key=self.api_key)
        logger.info(f"Initialized OpenAI embedding client with model: {self.model}")

    async def encode(
        self,
        texts: Union[str, List[str]],
        **kwargs
    ) -> EmbeddingResponse:
        if not self._client:
            await self.initialize()

        if isinstance(texts, str):
            texts = [texts]

        try:
            response = await self._client.embeddings.create(
                model=self.model,
                input=texts,
                **kwargs
            )
            
            embeddings = [item.embedding for item in response.data]
            
            return EmbeddingResponse(
                embeddings=embeddings,
                model=self.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "total_tokens": response.usage.total_tokens
                } if response.usage else None
            )
        except Exception as e:
            logger.error(f"OpenAI embedding error: {e}")
            raise


class SentenceTransformerEmbeddingClient(BaseEmbeddingClient):
    """Sentence Transformers Embedding Client (Local)"""
    
    def __init__(self, model: str = "BAAI/bge-large-zh", **kwargs):
        super().__init__(model, **kwargs)
        # 中文优化模型映射
        self.chinese_models = {
            "bge-large-zh": "BAAI/bge-large-zh",
            "bge-base-zh": "BAAI/bge-base-zh", 
            "bge-small-zh": "BAAI/bge-small-zh",
            "bge-large-zh-v1.5": "BAAI/bge-large-zh-v1.5",
            "text2vec-large-chinese": "GanymedeNil/text2vec-large-chinese",
            "text2vec-base-chinese": "shibing624/text2vec-base-chinese",
            "m3e-large": "moka-ai/m3e-large",
            "m3e-base": "moka-ai/m3e-base"
        }
        
        # 如果传入简化名称，映射到完整模型名
        if model in self.chinese_models:
            self.model = self.chinese_models[model]

    async def initialize(self) -> None:
        # Load model in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        self._client = await loop.run_in_executor(
            None, 
            lambda: SentenceTransformer(self.model)
        )
        self.dimension = self._client.get_sentence_embedding_dimension()
        logger.info(f"Initialized SentenceTransformer client with model: {self.model}")

    async def encode(
        self,
        texts: Union[str, List[str]],
        **kwargs
    ) -> EmbeddingResponse:
        if not self._client:
            await self.initialize()

        if isinstance(texts, str):
            texts = [texts]

        try:
            # Run encoding in thread pool
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None,
                lambda: self._client.encode(texts, **kwargs).tolist()
            )
            
            return EmbeddingResponse(
                embeddings=embeddings,
                model=self.model
            )
        except Exception as e:
            logger.error(f"SentenceTransformer embedding error: {e}")
            raise


class HuggingFaceEmbeddingClient(BaseEmbeddingClient):
    """HuggingFace API Embedding Client"""
    
    def __init__(
        self, 
        api_key: str, 
        model: str = "BAAI/bge-large-zh",
        api_url: str = "https://api-inference.huggingface.co/pipeline/feature-extraction",
        **kwargs
    ):
        super().__init__(model, **kwargs)
        self.api_key = api_key
        self.api_url = api_url

    async def initialize(self) -> None:
        self._client = httpx.AsyncClient(
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        logger.info(f"Initialized HuggingFace embedding client with model: {self.model}")

    async def encode(
        self,
        texts: Union[str, List[str]],
        **kwargs
    ) -> EmbeddingResponse:
        if not self._client:
            await self.initialize()

        if isinstance(texts, str):
            texts = [texts]

        try:
            payload = {
                "inputs": texts,
                "options": {"wait_for_model": True}
            }
            
            response = await self._client.post(
                f"{self.api_url}/{self.model}",
                json=payload
            )
            response.raise_for_status()
            
            embeddings = response.json()
            
            # Handle different response formats
            if isinstance(embeddings[0], list):
                # Direct embeddings
                final_embeddings = embeddings
            else:
                # Pooled embeddings
                final_embeddings = [emb for emb in embeddings]
            
            return EmbeddingResponse(
                embeddings=final_embeddings,
                model=self.model
            )
        except Exception as e:
            logger.error(f"HuggingFace embedding error: {e}")
            raise


class ChineseEmbeddingClient(BaseEmbeddingClient):
    """专门针对中文优化的嵌入模型客户端"""
    
    # 推荐的中文嵌入模型配置
    CHINESE_MODELS = {
        # BGE系列 - 目前最佳中文嵌入模型
        "bge-large-zh": {
            "model_name": "BAAI/bge-large-zh",
            "dimension": 1024,
            "max_length": 512,
            "description": "BGE大型中文模型，最佳性能",
            "use_case": ["通用检索", "语义相似度", "文档匹配"]
        },
        "bge-large-zh-v1.5": {
            "model_name": "BAAI/bge-large-zh-v1.5", 
            "dimension": 1024,
            "max_length": 512,
            "description": "BGE v1.5 改进版本",
            "use_case": ["改进的检索性能", "更好的多语言支持"]
        },
        "bge-base-zh": {
            "model_name": "BAAI/bge-base-zh",
            "dimension": 768,
            "max_length": 512, 
            "description": "BGE基础中文模型，平衡性能和速度",
            "use_case": ["中等规模应用", "资源受限环境"]
        },
        "bge-small-zh": {
            "model_name": "BAAI/bge-small-zh",
            "dimension": 512,
            "max_length": 512,
            "description": "BGE小型中文模型，快速推理",
            "use_case": ["实时应用", "移动端部署"]
        },
        # Text2Vec系列
        "text2vec-large": {
            "model_name": "GanymedeNil/text2vec-large-chinese",
            "dimension": 1024,
            "max_length": 512,
            "description": "Text2Vec大型中文模型",
            "use_case": ["文本相似度", "信息检索"]
        },
        "text2vec-base": {
            "model_name": "shibing624/text2vec-base-chinese", 
            "dimension": 768,
            "max_length": 256,
            "description": "Text2Vec基础中文模型",
            "use_case": ["轻量级应用", "快速部署"]
        },
        # M3E系列 - 多语言
        "m3e-large": {
            "model_name": "moka-ai/m3e-large",
            "dimension": 1024,
            "max_length": 512,
            "description": "M3E大型多语言模型，支持中英文",
            "use_case": ["中英混合文本", "跨语言检索"]
        },
        "m3e-base": {
            "model_name": "moka-ai/m3e-base",
            "dimension": 768,
            "max_length": 512,
            "description": "M3E基础多语言模型",
            "use_case": ["多语言场景", "中等性能需求"]
        }
    }
    
    def __init__(
        self, 
        model: str = "bge-large-zh",
        provider: str = "sentence_transformers",
        api_key: Optional[str] = None,
        **kwargs
    ):
        # 获取模型配置
        if model in self.CHINESE_MODELS:
            model_config = self.CHINESE_MODELS[model]
            actual_model = model_config["model_name"]
            self.dimension = model_config["dimension"]
            self.max_length = model_config["max_length"]
            self.description = model_config["description"]
            self.use_case = model_config["use_case"]
        else:
            # 如果不在预定义列表中，直接使用
            actual_model = model
            self.dimension = None
            self.max_length = 512
            self.description = f"Custom model: {model}"
            self.use_case = ["Custom use case"]
        
        super().__init__(actual_model, dimension=self.dimension, **kwargs)
        self.provider = provider
        self.api_key = api_key
        self.client_impl = None
    
    async def initialize(self) -> None:
        """根据provider初始化对应的客户端"""
        if self.provider == "sentence_transformers":
            self.client_impl = SentenceTransformerEmbeddingClient(
                self.model, dimension=self.dimension
            )
        elif self.provider == "huggingface" and self.api_key:
            self.client_impl = HuggingFaceEmbeddingClient(
                self.api_key, self.model
            )
        else:
            raise ValueError(f"Unsupported provider: {self.provider} or missing api_key")
        
        await self.client_impl.initialize()
        logger.info(f"Initialized Chinese embedding client: {self.description}")
    
    async def encode(
        self,
        texts: Union[str, List[str]], 
        **kwargs
    ) -> EmbeddingResponse:
        if not self.client_impl:
            await self.initialize()
        
        # 对中文文本进行预处理
        processed_texts = self._preprocess_chinese_texts(texts)
        return await self.client_impl.encode(processed_texts, **kwargs)
    
    def _preprocess_chinese_texts(self, texts: Union[str, List[str]]) -> Union[str, List[str]]:
        """中文文本预处理"""
        import re
        
        def clean_chinese_text(text: str) -> str:
            # 移除多余的空格和换行
            text = re.sub(r'\s+', ' ', text).strip()
            
            # 如果文本太长，进行截断（保留完整句子）
            if len(text) > self.max_length:
                # 尝试在句号、感叹号、问号处截断
                sentences = re.split(r'[。！？]', text[:self.max_length])
                if len(sentences) > 1:
                    # 保留完整句子
                    text = '。'.join(sentences[:-1]) + '。'
                else:
                    # 如果没有句号，直接截断
                    text = text[:self.max_length]
            
            return text
        
        if isinstance(texts, str):
            return clean_chinese_text(texts)
        else:
            return [clean_chinese_text(text) for text in texts]
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model,
            "dimension": self.dimension,
            "max_length": self.max_length,
            "description": self.description,
            "use_case": self.use_case,
            "provider": self.provider
        }
    
    @classmethod
    def list_available_models(cls) -> Dict[str, Any]:
        """列出所有可用的中文模型"""
        return cls.CHINESE_MODELS
    
    @classmethod
    def get_recommended_model(cls, use_case: str = "general") -> str:
        """根据使用场景推荐模型"""
        recommendations = {
            "general": "bge-large-zh",  # 通用场景
            "performance": "bge-large-zh-v1.5",  # 最佳性能
            "balanced": "bge-base-zh",  # 平衡性能和速度
            "fast": "bge-small-zh",  # 快速推理
            "multilingual": "m3e-large",  # 多语言支持
            "lightweight": "text2vec-base"  # 轻量级
        }
        return recommendations.get(use_case, "bge-large-zh")
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.client_impl:
            await self.client_impl.cleanup()


class EmbeddingClientFactory:
    """Factory for creating embedding clients"""
    
    @staticmethod
    def create_client(
        provider: str,
        model: str,
        api_key: Optional[str] = None,
        **kwargs
    ) -> BaseEmbeddingClient:
        provider = provider.lower()
        
        if provider == "openai":
            if not api_key:
                raise ValueError("API key required for OpenAI embedding client")
            return OpenAIEmbeddingClient(api_key, model, **kwargs)
        
        elif provider == "sentence_transformers" or provider == "local":
            return SentenceTransformerEmbeddingClient(model, **kwargs)
        
        elif provider == "huggingface":
            if not api_key:
                raise ValueError("API key required for HuggingFace embedding client")
            return HuggingFaceEmbeddingClient(api_key, model, **kwargs)
        
        elif provider == "chinese" or provider == "chinese_optimized":
            # 使用中文优化客户端
            return ChineseEmbeddingClient(model, "sentence_transformers", api_key, **kwargs)
        
        elif provider == "chinese_huggingface":
            # 使用HuggingFace的中文优化客户端
            if not api_key:
                raise ValueError("API key required for Chinese HuggingFace embedding client")
            return ChineseEmbeddingClient(model, "huggingface", api_key, **kwargs)
        
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")
    
    @staticmethod
    def create_chinese_client(
        model: str = "bge-large-zh",
        use_case: str = "general",
        provider: str = "sentence_transformers",
        api_key: Optional[str] = None,
        **kwargs
    ) -> ChineseEmbeddingClient:
        """创建中文优化的嵌入客户端的便捷方法"""
        # 如果没有指定模型，根据使用场景推荐
        if model == "auto":
            model = ChineseEmbeddingClient.get_recommended_model(use_case)
        
        return ChineseEmbeddingClient(model, provider, api_key, **kwargs)
    
    @staticmethod
    def list_chinese_models() -> Dict[str, Any]:
        """列出所有支持的中文嵌入模型"""
        return ChineseEmbeddingClient.list_available_models()
    
    @staticmethod
    def get_model_recommendation(use_case: str = "general") -> str:
        """根据使用场景获取模型推荐"""
        return ChineseEmbeddingClient.get_recommended_model(use_case)


class EmbeddingManager:
    """Manager for multiple embedding clients"""
    
    def __init__(self):
        self.clients: Dict[str, BaseEmbeddingClient] = {}
        self.primary_client: Optional[str] = None

    async def add_client(self, name: str, client: BaseEmbeddingClient) -> None:
        """Add an embedding client"""
        await client.initialize()
        self.clients[name] = client
        
        if not self.primary_client:
            self.primary_client = name
            
        logger.info(f"Added embedding client: {name}")

    async def set_primary(self, name: str) -> None:
        """Set primary embedding client"""
        if name not in self.clients:
            raise ValueError(f"Client {name} not found")
        self.primary_client = name
        logger.info(f"Set primary embedding client: {name}")

    async def encode(
        self,
        texts: Union[str, List[str]],
        client_name: Optional[str] = None,
        **kwargs
    ) -> EmbeddingResponse:
        """Encode texts using specified or primary client"""
        client_name = client_name or self.primary_client
        
        if not client_name or client_name not in self.clients:
            raise ValueError(f"Client {client_name} not available")
            
        return await self.clients[client_name].encode(texts, **kwargs)

    async def encode_single(
        self,
        text: str,
        client_name: Optional[str] = None,
        **kwargs
    ) -> List[float]:
        """Encode single text"""
        response = await self.encode([text], client_name, **kwargs)
        return response.embeddings[0]

    async def encode_batch(
        self,
        texts: List[str],
        batch_size: int = 100,
        client_name: Optional[str] = None,
        **kwargs
    ) -> List[List[float]]:
        """Encode texts in batches"""
        client_name = client_name or self.primary_client
        
        if not client_name or client_name not in self.clients:
            raise ValueError(f"Client {client_name} not available")
            
        return await self.clients[client_name].encode_batch(texts, batch_size, **kwargs)

    def get_dimension(self, client_name: Optional[str] = None) -> Optional[int]:
        """Get embedding dimension for client"""
        client_name = client_name or self.primary_client
        
        if client_name and client_name in self.clients:
            return self.clients[client_name].dimension
        return None

    def get_primary_client(self) -> Optional[BaseEmbeddingClient]:
        """Get the primary embedding client instance"""
        if not self.primary_client or self.primary_client not in self.clients:
            # Try to find the 'chinese' client if no primary is set
            if 'chinese' in self.clients:
                self.primary_client = 'chinese'
                logger.info("Auto-set 'chinese' as primary embedding client.")
                return self.clients['chinese']
            # Fallback to the first available client
            if self.clients:
                first_client_name = next(iter(self.clients))
                self.primary_client = first_client_name
                logger.info(f"Auto-set '{first_client_name}' as primary embedding client.")
                return self.clients[first_client_name]
            return None
        return self.clients[self.primary_client]

    async def compare_similarity(
        self,
        text1: str,
        text2: str,
        client_name: Optional[str] = None,
        **kwargs
    ) -> float:
        """Calculate cosine similarity between two texts"""
        embeddings = await self.encode([text1, text2], client_name, **kwargs)
        
        emb1 = np.array(embeddings.embeddings[0])
        emb2 = np.array(embeddings.embeddings[1])
        
        # Cosine similarity
        dot_product = np.dot(emb1, emb2)
        norm1 = np.linalg.norm(emb1)
        norm2 = np.linalg.norm(emb2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return float(dot_product / (norm1 * norm2))

    async def find_most_similar(
        self,
        query: str,
        candidates: List[str],
        top_k: int = 5,
        client_name: Optional[str] = None,
        **kwargs
    ) -> List[tuple]:
        """Find most similar texts to query"""
        all_texts = [query] + candidates
        embeddings = await self.encode(all_texts, client_name, **kwargs)
        
        query_emb = np.array(embeddings.embeddings[0])
        candidate_embs = np.array(embeddings.embeddings[1:])
        
        # Calculate similarities
        similarities = []
        for i, candidate_emb in enumerate(candidate_embs):
            similarity = np.dot(query_emb, candidate_emb) / (
                np.linalg.norm(query_emb) * np.linalg.norm(candidate_emb)
            )
            similarities.append((i, candidates[i], float(similarity)))
        
        # Sort by similarity descending
        similarities.sort(key=lambda x: x[2], reverse=True)
        
        return similarities[:top_k]

    async def cleanup(self) -> None:
        """Cleanup all clients"""
        for client in self.clients.values():
            await client.cleanup()
        logger.info("Cleaned up all embedding clients")