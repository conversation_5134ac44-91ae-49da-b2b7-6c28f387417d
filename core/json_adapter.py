"""
JSON Adapter for MCP Services
MCP服务JSON适配器 - 提供JSON格式的统一接口和数据转换
"""
import json
import re
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from loguru import logger


class JSONSchema:
    """JSON Schema定义和验证"""
    
    MCP_TOOL_SCHEMA = {
        "type": "object",
        "properties": {
            "name": {"type": "string"},
            "description": {"type": "string"},
            "parameters": {
                "type": "object",
                "properties": {},
                "additionalProperties": True
            },
            "required": {"type": "array", "items": {"type": "string"}}
        },
        "required": ["name", "description"]
    }
    
    SUPER_INTELLIGENCE_RESPONSE_SCHEMA = {
        "type": "object",
        "properties": {
            "analysis": {
                "type": "object",
                "properties": {
                    "intent_type": {"type": "string", "enum": ["query", "operation", "analysis", "conversation", "task_execution", "memory_retrieval"]},
                    "complexity": {"type": "string", "enum": ["simple", "medium", "complex", "expert"]},
                    "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                    "estimated_success_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                    "key_concepts": {"type": "array", "items": {"type": "string"}},
                    "required_tools": {"type": "array", "items": {"type": "string"}},
                    "data_dependencies": {"type": "array", "items": {"type": "string"}},
                    "risk_factors": {"type": "array", "items": {"type": "string"}},
                    "optimization_suggestions": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["intent_type", "complexity", "confidence"]
            },
            "execution_graph": {
                "type": "object",
                "properties": {
                    "nodes": {"type": "array"},
                    "edges": {"type": "array"},
                    "parallel_groups": {"type": "array"},
                    "critical_path": {"type": "array"},
                    "execution_priority": {"type": "array"}
                }
            },
            "quality_assurance": {"type": "object"},
            "optimization": {"type": "object"},
            "memory_instructions": {"type": "object"},
            "loop_control": {"type": "object"}
        },
        "required": ["analysis"]
    }


@dataclass
class JSONResponse:
    """标准JSON响应格式"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str = ""
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class JSONAdapter:
    """MCP服务JSON适配器"""
    
    def __init__(self):
        self.response_formatters = {}
        self.request_parsers = {}
        self._register_default_formatters()
    
    def _register_default_formatters(self):
        """注册默认的格式化器"""
        self.response_formatters.update({
            'mcp_tool_list': self._format_mcp_tools,
            'super_intelligence_result': self._format_super_intelligence,
            'execution_result': self._format_execution_result,
            'memory_result': self._format_memory_result,
            'rag_response': self._format_rag_response
        })
    
    def adapt_mcp_tools(self, tools_by_server: Dict[str, List]) -> JSONResponse:
        """适配MCP工具列表为标准JSON格式"""
        try:
            adapted_tools = {}
            total_tools = 0
            
            for server_name, tools in tools_by_server.items():
                server_tools = []
                for tool in tools:
                    tool_info = {
                        "name": getattr(tool, 'name', str(tool)),
                        "description": getattr(tool, 'description', 'No description'),
                        "parameters": getattr(tool, 'parameters', {}),
                        "server": server_name,
                        "available": True
                    }
                    server_tools.append(tool_info)
                    total_tools += 1
                
                adapted_tools[server_name] = {
                    "tools": server_tools,
                    "tool_count": len(server_tools),
                    "status": "active"
                }
            
            return JSONResponse(
                success=True,
                data={
                    "servers": adapted_tools,
                    "total_servers": len(tools_by_server),
                    "total_tools": total_tools
                },
                metadata={"type": "mcp_tool_discovery"}
            )
            
        except Exception as e:
            logger.error(f"Failed to adapt MCP tools: {e}")
            return JSONResponse(
                success=False,
                error=f"MCP tool adaptation failed: {str(e)}"
            )
    
    def adapt_super_intelligence_prompt(self, context_data: Dict[str, Any]) -> str:
        """为超级智能处理生成结构化JSON提示"""
        base_prompt = """
你是一个超级智能任务处理系统。请分析用户请求并严格按照以下JSON格式输出完整的执行方案。

**重要要求：输出必须是有效的JSON格式，不要包含任何其他文本或解释。**

```json
{
  "analysis": {
    "intent_type": "query|operation|analysis|conversation|task_execution|memory_retrieval",
    "complexity": "simple|medium|complex|expert",
    "confidence": 0.8,
    "estimated_success_rate": 0.7,
    "key_concepts": ["概念1", "概念2"],
    "required_tools": ["工具1", "工具2"],
    "data_dependencies": ["依赖1"],
    "risk_factors": ["风险1"],
    "optimization_suggestions": ["建议1"]
  },
  "execution_graph": {
    "nodes": [
      {
        "node_id": "n1",
        "tool_name": "工具名称",
        "parameters": {"key": "value"},
        "success_criteria": "成功条件",
        "failure_handling": "失败处理",
        "estimated_duration": 30,
        "resource_cost": "low"
      }
    ],
    "edges": [
      {"from": "n1", "to": "n2", "condition": "success", "data_flow": "output_field"}
    ],
    "parallel_groups": [["n1"]],
    "critical_path": ["n1"],
    "execution_priority": [1]
  },
  "quality_assurance": {
    "checkpoints": ["检查点1"],
    "success_threshold": 0.8,
    "improvement_strategy": "retry|supplement|accept|escalate",
    "validation_rules": ["规则1"]
  },
  "optimization": {
    "cycle_prediction": {
      "likely_issues": ["可能问题"],
      "prevention_actions": ["预防措施"],
      "fallback_plan": "备用方案"
    },
    "resource_allocation": {
      "estimated_duration": 120,
      "memory_requirement": "256MB",
      "api_calls": 5,
      "cost_estimate": "low"
    },
    "performance_tuning": {
      "concurrency_level": 3,
      "timeout_settings": 60,
      "retry_strategy": "exponential_backoff"
    }
  },
  "memory_instructions": {
    "should_remember": ["需要记住的内容"],
    "search_queries": ["搜索查询"],
    "episode_content": "本次交互摘要",
    "memory_type": "conversation"
  },
  "loop_control": {
    "continue_condition": "继续条件",
    "termination_criteria": "终止条件",
    "next_cycle_focus": "下轮重点",
    "learning_feedback": "学习反馈"
  }
}
```

请基于以下上下文信息生成JSON响应：
"""
        
        # 添加上下文信息
        if context_data:
            base_prompt += f"\n上下文信息：\n{json.dumps(context_data, ensure_ascii=False, indent=2)}"
        
        return base_prompt
    
    def parse_super_intelligence_response(self, raw_response: str) -> Dict[str, Any]:
        """解析超级智能处理的响应，支持多种格式"""
        try:
            # 首先尝试直接解析JSON
            if raw_response.strip().startswith('{'):
                return json.loads(raw_response)
            
            # 尝试提取JSON代码块
            json_match = re.search(r'```json\s*\n(.*?)\n```', raw_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            
            # 尝试提取第一个完整的JSON对象
            json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            
            # 如果都失败，返回结构化的错误响应
            logger.warning(f"Failed to parse JSON response, creating fallback structure")
            return self._create_fallback_response(raw_response)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parse error: {e}")
            return self._create_fallback_response(raw_response)
        except Exception as e:
            logger.error(f"Unexpected error parsing response: {e}")
            return self._create_fallback_response(raw_response)
    
    def _create_fallback_response(self, raw_response: str) -> Dict[str, Any]:
        """创建备用响应结构"""
        # 尝试从原始响应中提取有用信息
        intent_keywords = {
            "query": ["查询", "什么", "如何", "为什么", "question", "query"],
            "operation": ["执行", "操作", "处理", "运行", "operate", "execute"],
            "analysis": ["分析", "评估", "检查", "analyze", "evaluate"],
            "conversation": ["聊天", "对话", "交流", "chat", "talk"],
            "task_execution": ["任务", "完成", "实现", "task", "implement"]
        }
        
        detected_intent = "query"
        for intent, keywords in intent_keywords.items():
            if any(keyword in raw_response.lower() for keyword in keywords):
                detected_intent = intent
                break
        
        # 估算复杂度
        complexity = "simple"
        if len(raw_response) > 200:
            complexity = "medium"
        if any(word in raw_response for word in ["复杂", "困难", "challenging", "complex"]):
            complexity = "complex"
        
        return {
            "analysis": {
                "intent_type": detected_intent,
                "complexity": complexity,
                "confidence": 0.5,
                "estimated_success_rate": 0.6,
                "key_concepts": [raw_response[:50] + "..." if len(raw_response) > 50 else raw_response],
                "required_tools": ["direct_response"],
                "data_dependencies": [],
                "risk_factors": ["Response parsing failed"],
                "optimization_suggestions": ["Improve JSON format compliance"]
            },
            "execution_graph": {
                "nodes": [{
                    "node_id": "fallback_response",
                    "tool_name": "direct_response", 
                    "parameters": {"response": raw_response[:200]},
                    "success_criteria": "response_generated",
                    "failure_handling": "return_error",
                    "estimated_duration": 5,
                    "resource_cost": "low"
                }],
                "edges": [],
                "parallel_groups": [["fallback_response"]],
                "critical_path": ["fallback_response"],
                "execution_priority": [1]
            },
            "quality_assurance": {
                "checkpoints": ["basic_response"],
                "success_threshold": 0.5,
                "improvement_strategy": "accept",
                "validation_rules": ["response_not_empty"]
            },
            "optimization": {
                "cycle_prediction": {
                    "likely_issues": ["JSON parsing failure"],
                    "prevention_actions": ["Improve prompt engineering"],
                    "fallback_plan": "Use direct response"
                },
                "resource_allocation": {
                    "estimated_duration": 10,
                    "memory_requirement": "64MB", 
                    "api_calls": 1,
                    "cost_estimate": "low"
                },
                "performance_tuning": {
                    "concurrency_level": 1,
                    "timeout_settings": 30,
                    "retry_strategy": "none"
                }
            },
            "memory_instructions": {
                "should_remember": ["JSON parsing failed"],
                "search_queries": [],
                "episode_content": f"Fallback response for: {raw_response[:100]}",
                "memory_type": "error_case"
            },
            "loop_control": {
                "continue_condition": "never",
                "termination_criteria": "immediate", 
                "next_cycle_focus": "none",
                "learning_feedback": "Need better structured responses"
            },
            "_fallback": True,
            "_original_response": raw_response
        }
    
    def adapt_execution_result(self, execution_result: Dict[str, Any]) -> JSONResponse:
        """适配执行结果为标准JSON格式"""
        try:
            # 处理不同类型的执行结果
            if isinstance(execution_result, bool):
                # 简单布尔结果
                return JSONResponse(
                    success=execution_result,
                    data={
                        "execution_success": execution_result,
                        "completed_nodes": 1 if execution_result else 0,
                        "total_nodes": 1,
                        "execution_time": 0.0,
                        "error_message": None if execution_result else "Execution failed"
                    },
                    metadata={"type": "simple_execution_result"}
                )
            
            elif isinstance(execution_result, dict):
                # 详细执行结果
                return JSONResponse(
                    success=execution_result.get("success", False),
                    data={
                        "execution_success": execution_result.get("success", False),
                        "completed_nodes": execution_result.get("completed_nodes", 0),
                        "total_nodes": execution_result.get("total_nodes", 1),
                        "execution_time": execution_result.get("execution_time", 0.0),
                        "critical_path": execution_result.get("critical_path", []),
                        "failed_nodes": execution_result.get("failed_nodes", []),
                        "node_results": execution_result.get("node_results", {}),
                        "error_message": execution_result.get("error")
                    },
                    metadata={"type": "detailed_execution_result"}
                )
            
            else:
                return JSONResponse(
                    success=False,
                    error=f"Unsupported execution result type: {type(execution_result)}"
                )
                
        except Exception as e:
            logger.error(f"Failed to adapt execution result: {e}")
            return JSONResponse(
                success=False,
                error=f"Execution result adaptation failed: {str(e)}"
            )
    
    def _format_mcp_tools(self, data: Any) -> Dict[str, Any]:
        """格式化MCP工具数据"""
        return {"formatted_tools": data}
    
    def _format_super_intelligence(self, data: Any) -> Dict[str, Any]:
        """格式化超级智能结果"""
        return {"intelligence_analysis": data}
    
    def _format_execution_result(self, data: Any) -> Dict[str, Any]:
        """格式化执行结果"""
        return {"execution_data": data}
    
    def _format_memory_result(self, data: Any) -> Dict[str, Any]:
        """格式化记忆结果"""
        return {"memory_data": data}
    
    def _format_rag_response(self, data: Any) -> Dict[str, Any]:
        """格式化RAG响应"""
        return {
            "rag_response": data,
            "formatted_at": datetime.now().isoformat()
        }
    
    def validate_json_schema(self, data: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """验证JSON数据是否符合schema"""
        try:
            # 简化的schema验证，实际项目中可使用jsonschema库
            if schema.get("type") == "object":
                if not isinstance(data, dict):
                    return False
                
                required = schema.get("required", [])
                for field in required:
                    if field not in data:
                        return False
                
                properties = schema.get("properties", {})
                for key, value in data.items():
                    if key in properties:
                        prop_schema = properties[key]
                        if not self._validate_property(value, prop_schema):
                            return False
            
            return True
            
        except Exception as e:
            logger.error(f"Schema validation error: {e}")
            return False
    
    def _validate_property(self, value: Any, prop_schema: Dict[str, Any]) -> bool:
        """验证单个属性"""
        prop_type = prop_schema.get("type")
        
        if prop_type == "string":
            return isinstance(value, str)
        elif prop_type == "number":
            return isinstance(value, (int, float))
        elif prop_type == "boolean":
            return isinstance(value, bool)
        elif prop_type == "array":
            return isinstance(value, list)
        elif prop_type == "object":
            return isinstance(value, dict)
        
        return True
    
    def create_standardized_response(
        self, 
        success: bool, 
        data: Any = None, 
        error: str = None,
        response_type: str = "general"
    ) -> JSONResponse:
        """创建标准化响应"""
        response = JSONResponse(
            success=success,
            data=data,
            error=error,
            metadata={"response_type": response_type}
        )
        
        return response


# 全局JSON适配器实例
json_adapter = JSONAdapter()


def adapt_to_json(data: Any, data_type: str = "general") -> JSONResponse:
    """便捷函数：将数据适配为JSON响应"""
    return json_adapter.create_standardized_response(
        success=True,
        data=data,
        response_type=data_type
    )


def parse_json_safely(json_str: str) -> Dict[str, Any]:
    """安全地解析JSON字符串"""
    return json_adapter.parse_super_intelligence_response(json_str)


def validate_response_schema(data: Dict[str, Any], schema_name: str = "super_intelligence") -> bool:
    """验证响应是否符合指定schema"""
    schemas = {
        "super_intelligence": JSONSchema.SUPER_INTELLIGENCE_RESPONSE_SCHEMA,
        "mcp_tool": JSONSchema.MCP_TOOL_SCHEMA
    }
    
    schema = schemas.get(schema_name)
    if not schema:
        return True
    
    return json_adapter.validate_json_schema(data, schema)