"""
Directed Graph Execution Engine
有向图执行引擎 - 基于DAG的智能任务执行系统
"""
import asyncio
import heapq
import time
from collections import defaultdict, deque
from typing import Any, Dict, List, Optional, Set, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

from loguru import logger

from .mcp_client import EnhancedMCPClient, ToolCall, ToolExecutionStatus


class ExecutionStrategy(Enum):
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    HYBRID = "hybrid"
    PRIORITY_BASED = "priority_based"
    CRITICAL_PATH = "critical_path"


class NodeStatus(Enum):
    WAITING = "waiting"
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ExecutionNode:
    id: str
    tool_call: ToolCall
    dependencies: Set[str] = field(default_factory=set)
    dependents: Set[str] = field(default_factory=set)
    status: NodeStatus = NodeStatus.WAITING
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_duration: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    priority: int = 1
    estimated_duration: float = 30.0
    resource_cost: str = "medium"
    
    def is_ready(self, completed_nodes: Set[str]) -> bool:
        """检查节点是否准备好执行"""
        return (self.status == NodeStatus.WAITING and 
                self.dependencies.issubset(completed_nodes))
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries
    
    def get_execution_weight(self) -> float:
        """获取执行权重（用于优先级排序）"""
        cost_weights = {"low": 0.5, "medium": 1.0, "high": 2.0}
        cost_weight = cost_weights.get(self.resource_cost, 1.0)
        return (self.priority * 10) + (self.estimated_duration * cost_weight)


@dataclass
class ExecutionGraph:
    nodes: Dict[str, ExecutionNode] = field(default_factory=dict)
    edges: List[Dict[str, str]] = field(default_factory=list)
    execution_order: List[str] = field(default_factory=list)
    parallel_groups: List[List[str]] = field(default_factory=list)
    critical_path: List[str] = field(default_factory=list)
    
    def add_node(self, node: ExecutionNode) -> None:
        """添加节点到图中"""
        self.nodes[node.id] = node
    
    def add_edge(self, from_id: str, to_id: str, condition: str = "success") -> None:
        """添加边到图中"""
        if from_id in self.nodes and to_id in self.nodes:
            self.edges.append({
                "from": from_id, 
                "to": to_id, 
                "condition": condition
            })
            self.nodes[to_id].dependencies.add(from_id)
            self.nodes[from_id].dependents.add(to_id)
    
    def get_ready_nodes(self, completed_nodes: Set[str]) -> List[ExecutionNode]:
        """获取准备执行的节点"""
        ready_nodes = []
        for node in self.nodes.values():
            if node.is_ready(completed_nodes):
                ready_nodes.append(node)
        return ready_nodes
    
    def calculate_critical_path(self) -> List[str]:
        """计算关键路径（最长路径）"""
        # 拓扑排序
        in_degree = defaultdict(int)
        for node_id in self.nodes:
            in_degree[node_id] = len(self.nodes[node_id].dependencies)
        
        queue = deque([node_id for node_id, degree in in_degree.items() if degree == 0])
        topo_order = []
        
        while queue:
            current = queue.popleft()
            topo_order.append(current)
            
            for dependent in self.nodes[current].dependents:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)
        
        # 计算最长路径
        distances = {node_id: 0 for node_id in self.nodes}
        predecessors = {node_id: None for node_id in self.nodes}
        
        for current in topo_order:
            current_node = self.nodes[current]
            for dependent in current_node.dependents:
                new_distance = distances[current] + current_node.estimated_duration
                if new_distance > distances[dependent]:
                    distances[dependent] = new_distance
                    predecessors[dependent] = current
        
        # 构建关键路径
        # 找到最长路径的终点
        max_distance = max(distances.values())
        end_nodes = [node_id for node_id, dist in distances.items() if dist == max_distance]
        
        if not end_nodes:
            return []
        
        # 回溯构建路径
        critical_path = []
        current = end_nodes[0]  # 选择其中一个终点
        
        while current is not None:
            critical_path.append(current)
            current = predecessors[current]
        
        critical_path.reverse()
        self.critical_path = critical_path
        return critical_path
    
    def detect_cycles(self) -> List[List[str]]:
        """检测图中的环"""
        white = set(self.nodes.keys())  # 未访问
        gray = set()  # 正在访问
        black = set()  # 已访问
        cycles = []
        
        def dfs_visit(node_id: str, path: List[str]) -> None:
            if node_id in gray:
                # 发现环
                cycle_start = path.index(node_id)
                cycles.append(path[cycle_start:] + [node_id])
                return
            
            if node_id in black:
                return
            
            white.remove(node_id)
            gray.add(node_id)
            path.append(node_id)
            
            for dependent in self.nodes[node_id].dependents:
                dfs_visit(dependent, path.copy())
            
            gray.remove(node_id)
            black.add(node_id)
        
        while white:
            start_node = next(iter(white))
            dfs_visit(start_node, [])
        
        return cycles


class GraphExecutionEngine:
    """有向图执行引擎"""
    
    def __init__(
        self,
        mcp_client: EnhancedMCPClient,
        max_concurrent: int = 5,
        default_timeout: float = 60.0,
        retry_delay: float = 2.0
    ):
        self.mcp_client = mcp_client
        self.max_concurrent = max_concurrent
        self.default_timeout = default_timeout
        self.retry_delay = retry_delay
        
        # 执行状态
        self.current_graph: Optional[ExecutionGraph] = None
        self.running_nodes: Dict[str, asyncio.Task] = {}
        self.completed_nodes: Set[str] = set()
        self.failed_nodes: Set[str] = set()
        self.execution_stats: Dict[str, Any] = {}
        self.execution_semaphore = asyncio.Semaphore(max_concurrent)
        
        # 监控和回调
        self.progress_callback: Optional[Callable] = None
        self.node_completion_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_node_completion_callback(self, callback: Callable[[ExecutionNode], None]) -> None:
        """设置节点完成回调函数"""
        self.node_completion_callback = callback

    async def execute_graph(
        self,
        tool_calls: List[ToolCall],
        strategy: ExecutionStrategy = ExecutionStrategy.HYBRID,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        执行有向图
        
        Args:
            tool_calls: 工具调用列表
            strategy: 执行策略
            timeout: 超时时间
            
        Returns:
            执行结果字典
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting graph execution with {len(tool_calls)} nodes")
            
            # 1. 构建执行图
            self.current_graph = await self._build_execution_graph(tool_calls)
            
            # 2. 验证图的合法性
            validation_result = await self._validate_graph()
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Graph validation failed: {validation_result['errors']}",
                    "execution_time": time.time() - start_time
                }
            
            # 3. 计算关键路径和优化执行顺序
            critical_path = self.current_graph.calculate_critical_path()
            logger.info(f"Critical path: {critical_path}")
            
            # 4. 根据策略执行图
            execution_result = await self._execute_with_strategy(strategy, timeout)
            
            # 5. 收集执行统计
            execution_stats = self._collect_execution_stats(start_time)
            
            # 6. 构建最终结果
            final_result = {
                "success": execution_result["success"],
                "total_nodes": len(self.current_graph.nodes),
                "completed_nodes": len(self.completed_nodes),
                "failed_nodes": len(self.failed_nodes),
                "execution_time": time.time() - start_time,
                "critical_path": critical_path,
                "node_results": self._extract_node_results(),
                "stats": execution_stats
            }
            
            if not execution_result["success"]:
                final_result["error"] = execution_result.get("error", "Execution failed")
            
            logger.info(f"Graph execution completed: {final_result['success']}")
            return final_result
            
        except Exception as e:
            error_msg = f"Graph execution error: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "execution_time": time.time() - start_time
            }
        finally:
            # 清理状态
            await self._cleanup_execution_state()

    async def _build_execution_graph(self, tool_calls: List[ToolCall]) -> ExecutionGraph:
        """构建执行图"""
        graph = ExecutionGraph()
        
        # 添加节点
        for tool_call in tool_calls:
            node = ExecutionNode(
                id=tool_call.id,
                tool_call=tool_call,
                priority=tool_call.priority,
                estimated_duration=30.0  # 默认估计时间
            )
            
            # 添加依赖关系
            for dep_id in tool_call.dependencies:
                node.dependencies.add(dep_id)
            
            graph.add_node(node)
        
        # 添加边
        for node in graph.nodes.values():
            for dep_id in node.dependencies:
                if dep_id in graph.nodes:
                    graph.add_edge(dep_id, node.id)
        
        return graph

    async def _validate_graph(self) -> Dict[str, Any]:
        """验证图的合法性"""
        errors = []
        
        if not self.current_graph:
            return {"valid": False, "errors": ["No graph to validate"]}
        
        # 检查环
        cycles = self.current_graph.detect_cycles()
        if cycles:
            errors.append(f"Detected cycles in graph: {cycles}")
        
        # 检查依赖关系的有效性
        for node in self.current_graph.nodes.values():
            for dep_id in node.dependencies:
                if dep_id not in self.current_graph.nodes:
                    errors.append(f"Node {node.id} has invalid dependency: {dep_id}")
        
        # 检查是否有孤立节点（没有入度也没有出度的节点，除非是单节点图）
        if len(self.current_graph.nodes) > 1:
            for node in self.current_graph.nodes.values():
                if not node.dependencies and not node.dependents:
                    logger.warning(f"Isolated node detected: {node.id}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "cycles": cycles
        }

    async def _execute_with_strategy(
        self, 
        strategy: ExecutionStrategy, 
        timeout: Optional[float]
    ) -> Dict[str, Any]:
        """根据策略执行图"""
        if strategy == ExecutionStrategy.SEQUENTIAL:
            return await self._execute_sequential(timeout)
        elif strategy == ExecutionStrategy.PARALLEL:
            return await self._execute_parallel(timeout)
        elif strategy == ExecutionStrategy.HYBRID:
            return await self._execute_hybrid(timeout)
        elif strategy == ExecutionStrategy.PRIORITY_BASED:
            return await self._execute_priority_based(timeout)
        elif strategy == ExecutionStrategy.CRITICAL_PATH:
            return await self._execute_critical_path_optimized(timeout)
        else:
            return await self._execute_hybrid(timeout)  # 默认策略

    async def _execute_hybrid(self, timeout: Optional[float]) -> Dict[str, Any]:
        """混合执行策略（推荐）"""
        try:
            timeout = timeout or (self.default_timeout * len(self.current_graph.nodes))
            execution_start = time.time()
            
            while (self.completed_nodes | self.failed_nodes) < set(self.current_graph.nodes.keys()):
                # 检查超时
                if time.time() - execution_start > timeout:
                    return {"success": False, "error": "Execution timeout"}
                
                # 获取准备执行的节点
                ready_nodes = self.current_graph.get_ready_nodes(self.completed_nodes)
                
                if not ready_nodes and not self.running_nodes:
                    # 没有可执行的节点，可能有循环依赖或其他问题
                    break
                
                # 按优先级排序准备执行的节点
                ready_nodes.sort(key=lambda x: x.get_execution_weight(), reverse=True)
                
                # 启动新的任务（受并发限制）
                for node in ready_nodes:
                    if len(self.running_nodes) >= self.max_concurrent:
                        break
                    
                    if node.id not in self.running_nodes:
                        task = asyncio.create_task(self._execute_node(node))
                        self.running_nodes[node.id] = task
                        node.status = NodeStatus.RUNNING
                        logger.debug(f"Started execution of node: {node.id}")
                
                # 等待至少一个任务完成
                if self.running_nodes:
                    done, pending = await asyncio.wait(
                        self.running_nodes.values(),
                        return_when=asyncio.FIRST_COMPLETED,
                        timeout=1.0
                    )
                    
                    # 处理完成的任务
                    for task in done:
                        await self._handle_completed_task(task)
                
                # 报告进度
                if self.progress_callback:
                    progress = {
                        "total_nodes": len(self.current_graph.nodes),
                        "completed": len(self.completed_nodes),
                        "running": len(self.running_nodes),
                        "failed": len(self.failed_nodes)
                    }
                    self.progress_callback(progress)
                
                # 短暂休息避免忙等待
                await asyncio.sleep(0.1)
            
            # 等待所有剩余任务完成
            if self.running_nodes:
                await asyncio.wait(self.running_nodes.values())
                for task in list(self.running_nodes.values()):
                    await self._handle_completed_task(task)
            
            success = len(self.completed_nodes) > 0 and len(self.failed_nodes) == 0
            return {"success": success}
            
        except Exception as e:
            logger.error(f"Hybrid execution failed: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_node(self, node: ExecutionNode) -> ExecutionNode:
        """执行单个节点"""
        async with self.execution_semaphore:
            node.start_time = datetime.now()
            
            try:
                logger.info(f"Executing node: {node.id} (tool: {node.tool_call.name})")
                
                # 执行工具调用
                result = await self.mcp_client.execute_single_tool(
                    tool_name=node.tool_call.name,
                    arguments=node.tool_call.arguments,
                    timeout=self.default_timeout
                )
                
                node.result = result.result
                node.error = result.error
                
                if result.status == ToolExecutionStatus.COMPLETED:
                    node.status = NodeStatus.COMPLETED
                    logger.info(f"Node {node.id} completed successfully")
                else:
                    node.status = NodeStatus.FAILED
                    logger.warning(f"Node {node.id} failed: {node.error}")
                
            except Exception as e:
                node.error = str(e)
                node.status = NodeStatus.FAILED
                logger.error(f"Node {node.id} execution error: {e}")
            
            finally:
                node.end_time = datetime.now()
                if node.start_time:
                    node.execution_duration = (node.end_time - node.start_time).total_seconds()
        
        return node

    async def _handle_completed_task(self, task: asyncio.Task) -> None:
        """处理完成的任务"""
        try:
            node = await task
            
            # 从运行中的任务移除
            node_id = node.id
            if node_id in self.running_nodes:
                del self.running_nodes[node_id]
            
            # 更新完成状态
            if node.status == NodeStatus.COMPLETED:
                self.completed_nodes.add(node_id)
            else:
                self.failed_nodes.add(node_id)
                
                # 如果可以重试，准备重试
                if node.can_retry():
                    node.retry_count += 1
                    node.status = NodeStatus.WAITING
                    self.failed_nodes.remove(node_id)
                    
                    # 延迟后重试
                    await asyncio.sleep(self.retry_delay)
                    logger.info(f"Retrying node {node_id} (attempt {node.retry_count})")
            
            # 调用回调函数
            if self.node_completion_callback:
                self.node_completion_callback(node)
                
        except Exception as e:
            logger.error(f"Error handling completed task: {e}")

    async def _execute_sequential(self, timeout: Optional[float]) -> Dict[str, Any]:
        """顺序执行策略"""
        # 拓扑排序
        execution_order = self._topological_sort()
        
        for node_id in execution_order:
            node = self.current_graph.nodes[node_id]
            executed_node = await self._execute_node(node)
            
            if executed_node.status == NodeStatus.COMPLETED:
                self.completed_nodes.add(node_id)
            else:
                self.failed_nodes.add(node_id)
                # 顺序执行中，如果有节点失败就停止
                break
        
        return {"success": len(self.failed_nodes) == 0}

    async def _execute_parallel(self, timeout: Optional[float]) -> Dict[str, Any]:
        """并行执行策略"""
        tasks = []
        for node in self.current_graph.nodes.values():
            task = asyncio.create_task(self._execute_node(node))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.failed_nodes.add(list(self.current_graph.nodes.keys())[i])
            else:
                node = result
                if node.status == NodeStatus.COMPLETED:
                    self.completed_nodes.add(node.id)
                else:
                    self.failed_nodes.add(node.id)
        
        return {"success": len(self.failed_nodes) == 0}

    async def _execute_priority_based(self, timeout: Optional[float]) -> Dict[str, Any]:
        """基于优先级的执行策略"""
        # 使用优先队列
        priority_queue = []
        for node in self.current_graph.nodes.values():
            heapq.heappush(priority_queue, (-node.priority, node.id, node))
        
        while priority_queue:
            _, node_id, node = heapq.heappop(priority_queue)
            
            # 检查依赖是否满足
            if node.dependencies.issubset(self.completed_nodes):
                executed_node = await self._execute_node(node)
                
                if executed_node.status == NodeStatus.COMPLETED:
                    self.completed_nodes.add(node_id)
                else:
                    self.failed_nodes.add(node_id)
            else:
                # 依赖未满足，重新加入队列
                heapq.heappush(priority_queue, (-node.priority, node_id, node))
        
        return {"success": len(self.failed_nodes) == 0}

    async def _execute_critical_path_optimized(self, timeout: Optional[float]) -> Dict[str, Any]:
        """关键路径优化执行策略"""
        critical_path = self.current_graph.calculate_critical_path()
        
        # 优先执行关键路径上的节点
        critical_nodes = {node_id for node_id in critical_path}
        
        # 分两个阶段：先执行关键路径，再执行其他节点
        for is_critical in [True, False]:
            target_nodes = (critical_nodes if is_critical 
                          else set(self.current_graph.nodes.keys()) - critical_nodes)
            
            for node_id in target_nodes:
                if node_id in self.completed_nodes or node_id in self.failed_nodes:
                    continue
                
                node = self.current_graph.nodes[node_id]
                if node.dependencies.issubset(self.completed_nodes):
                    executed_node = await self._execute_node(node)
                    
                    if executed_node.status == NodeStatus.COMPLETED:
                        self.completed_nodes.add(node_id)
                    else:
                        self.failed_nodes.add(node_id)
        
        return {"success": len(self.failed_nodes) == 0}

    def _topological_sort(self) -> List[str]:
        """拓扑排序"""
        in_degree = defaultdict(int)
        for node_id in self.current_graph.nodes:
            in_degree[node_id] = len(self.current_graph.nodes[node_id].dependencies)
        
        queue = deque([node_id for node_id, degree in in_degree.items() if degree == 0])
        result = []
        
        while queue:
            current = queue.popleft()
            result.append(current)
            
            for dependent in self.current_graph.nodes[current].dependents:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)
        
        return result

    def _collect_execution_stats(self, start_time: float) -> Dict[str, Any]:
        """收集执行统计信息"""
        if not self.current_graph:
            return {}
        
        stats = {
            "total_execution_time": time.time() - start_time,
            "total_nodes": len(self.current_graph.nodes),
            "completed_nodes": len(self.completed_nodes),
            "failed_nodes": len(self.failed_nodes),
            "success_rate": len(self.completed_nodes) / len(self.current_graph.nodes),
            "critical_path_length": len(self.current_graph.critical_path),
            "average_node_duration": 0,
            "longest_node_duration": 0,
            "shortest_node_duration": float('inf')
        }
        
        durations = []
        for node in self.current_graph.nodes.values():
            if node.execution_duration is not None:
                durations.append(node.execution_duration)
                stats["longest_node_duration"] = max(
                    stats["longest_node_duration"], 
                    node.execution_duration
                )
                stats["shortest_node_duration"] = min(
                    stats["shortest_node_duration"], 
                    node.execution_duration
                )
        
        if durations:
            stats["average_node_duration"] = sum(durations) / len(durations)
        else:
            stats["shortest_node_duration"] = 0
        
        return stats

    def _extract_node_results(self) -> Dict[str, Any]:
        """提取节点执行结果"""
        if not self.current_graph:
            return {}
        
        results = {}
        for node_id, node in self.current_graph.nodes.items():
            results[node_id] = {
                "status": node.status.value,
                "tool_name": node.tool_call.name,
                "result": node.result,
                "error": node.error,
                "execution_duration": node.execution_duration,
                "retry_count": node.retry_count
            }
        
        return results

    async def _cleanup_execution_state(self) -> None:
        """清理执行状态"""
        # 取消所有正在运行的任务
        for task in self.running_nodes.values():
            if not task.done():
                task.cancel()
        
        # 等待任务完成
        if self.running_nodes:
            await asyncio.wait(
                self.running_nodes.values(), 
                return_when=asyncio.ALL_COMPLETED
            )
        
        # 重置状态
        self.running_nodes.clear()
        self.completed_nodes.clear()
        self.failed_nodes.clear()
        self.current_graph = None