"""
Enhanced MCP Client with Advanced Features
"""
import asyncio
import json
import os
import shutil
import uuid
from contextlib import AsyncExitStack
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set, Union
from datetime import datetime
from enum import Enum

from mcp import ClientSession, StdioServerParameters
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from mcp.client.streamable_http import streamablehttp_client

from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from .llm_client import LLMManager, LLMMessage
from .json_adapter import json_adapter, JSONResponse


class TransportType(Enum):
    STDIO = "stdio"
    HTTP = "http"
    SSE = "sse"


class ToolExecutionStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ToolCall:
    id: str
    name: str
    arguments: Dict[str, Any]
    status: ToolExecutionStatus = ToolExecutionStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)
    priority: int = 1


@dataclass
class ExecutionNode:
    id: str
    tool_call: ToolCall
    dependencies: Set[str] = field(default_factory=set)
    dependents: Set[str] = field(default_factory=set)
    execution_order: int = 0


class EnhancedMCPServer:
    """Enhanced MCP Server with advanced connection management"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.transport_type = TransportType(config.get("type", "stdio"))
        self._tools_cache: Optional[List] = None
        self._resources_cache: Optional[List] = None
        self._connection_retries = 0
        self._max_retries = config.get("max_retries", 3)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def initialize(self) -> None:
        """Initialize server connection with retry logic"""
        logger.info(f"Initializing server {self.name} with transport {self.transport_type.value}")
        
        try:
            if self.transport_type == TransportType.STDIO:
                await self._initialize_stdio()
            elif self.transport_type == TransportType.HTTP:
                await self._initialize_http()
            elif self.transport_type == TransportType.SSE:
                await self._initialize_sse()
            else:
                raise ValueError(f"Unsupported transport type: {self.transport_type}")
                
            await self._cache_server_capabilities()
            logger.info(f"Server {self.name} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize server {self.name}: {e}")
            await self.cleanup()
            raise

    async def _initialize_stdio(self) -> None:
        """Initialize STDIO connection"""
        command = self.config["command"]
        if command == "npx":
            command = shutil.which("npx")
        elif not shutil.which(command):
            raise ValueError(f"Command not found: {command}")

        server_params = StdioServerParameters(
            command=command,
            args=self.config.get("args", []),
            env={**os.environ, **self.config.get("env", {})},
        )
        
        stdio_transport = await self.exit_stack.enter_async_context(
            stdio_client(server_params)
        )
        read, write = stdio_transport
        session = await self.exit_stack.enter_async_context(
            ClientSession(read, write)
        )
        await session.initialize()
        self.session = session

    async def _initialize_http(self) -> None:
        """Initialize HTTP connection"""
        url = self.config.get("url", f"http://localhost:{self.config.get('port', 8000)}/mcp")
        timeout = self.config.get("timeout", 60)
        
        http_transport = await self.exit_stack.enter_async_context(
            streamablehttp_client(url=url, timeout=timeout)
        )
        read_stream, write_stream, get_session_id = http_transport
        session = await self.exit_stack.enter_async_context(
            ClientSession(read_stream, write_stream)
        )
        await session.initialize()
        self.session = session

    async def _initialize_sse(self) -> None:
        """Initialize SSE connection"""
        url = self.config.get("url", f"http://localhost:{self.config.get('port', 8000)}/sse")
        timeout = self.config.get("timeout", 60)
        
        sse_transport = await self.exit_stack.enter_async_context(
            sse_client(url=url, timeout=timeout)
        )
        read_stream, write_stream = sse_transport
        session = await self.exit_stack.enter_async_context(
            ClientSession(read_stream, write_stream)
        )
        await session.initialize()
        self.session = session

    async def _cache_server_capabilities(self) -> None:
        """Cache server tools and resources"""
        if not self.session:
            return
            
        try:
            # Cache tools
            tools_response = await self.session.list_tools()
            self._tools_cache = []
            for item in tools_response:
                if isinstance(item, tuple) and item[0] == "tools":
                    self._tools_cache.extend(item[1])
            
            # Cache resources
            try:
                resources_response = await self.session.list_resources()
                self._resources_cache = []
                for item in resources_response:
                    if isinstance(item, tuple) and item[0] == "resources":
                        self._resources_cache.extend(item[1])
            except Exception as e:
                logger.warning(f"Could not cache resources for {self.name}: {e}")
                self._resources_cache = []
                
        except Exception as e:
            logger.error(f"Failed to cache capabilities for {self.name}: {e}")

    async def list_tools(self) -> List[Any]:
        """List available tools with caching"""
        if self._tools_cache is not None:
            return self._tools_cache
            
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        tools_response = await self.session.list_tools()
        tools = []
        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools.extend(item[1])
        
        self._tools_cache = tools
        return tools

    async def list_resources(self) -> List[Any]:
        """List available resources with caching"""
        if self._resources_cache is not None:
            return self._resources_cache
            
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            resources_response = await self.session.list_resources()
            resources = []
            for item in resources_response:
                if isinstance(item, tuple) and item[0] == "resources":
                    resources.extend(item[1])
            
            self._resources_cache = resources
            return resources
        except Exception as e:
            logger.warning(f"Could not list resources for {self.name}: {e}")
            return []

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8)
    )
    async def execute_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        timeout: Optional[float] = None
    ) -> Any:
        """Execute tool with retry logic and timeout"""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        logger.info(f"Executing tool {tool_name} on server {self.name}")
        
        try:
            if timeout:
                result = await asyncio.wait_for(
                    self.session.call_tool(tool_name, arguments),
                    timeout=timeout
                )
            else:
                result = await self.session.call_tool(tool_name, arguments)
                
            logger.debug(f"Tool {tool_name} completed successfully")
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"Tool {tool_name} timed out after {timeout}s")
            raise
        except Exception as e:
            logger.error(f"Tool {tool_name} failed: {e}")
            raise

    async def health_check(self) -> bool:
        """Check if server is healthy"""
        if not self.session:
            return False
            
        try:
            await self.list_tools()
            return True
        except Exception as e:
            logger.warning(f"Health check failed for {self.name}: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup server resources"""
        try:
            await self.exit_stack.aclose()
            self.session = None
            self._tools_cache = None
            self._resources_cache = None
        except Exception as e:
            logger.error(f"Cleanup error for server {self.name}: {e}")


class EnhancedMCPClient:
    """Enhanced MCP Client with intelligent execution and memory integration"""
    
    def __init__(self, llm_manager: LLMManager):
        self.servers: Dict[str, EnhancedMCPServer] = {}
        self.llm_manager = llm_manager
        self.execution_history: List[ToolCall] = []
        self._tool_registry: Dict[str, str] = {}  # tool_name -> server_name

    async def add_server(self, name: str, config: Dict[str, Any]) -> None:
        """Add and initialize MCP server"""
        server = EnhancedMCPServer(name, config)
        await server.initialize()
        self.servers[name] = server
        
        # Update tool registry
        await self._update_tool_registry()
        logger.info(f"Added server: {name}")

    async def _update_tool_registry(self) -> None:
        """Update tool registry with all server tools"""
        self._tool_registry.clear()
        
        for server_name, server in self.servers.items():
            if not await server.health_check():
                continue
                
            tools = await server.list_tools()
            for tool in tools:
                tool_name = getattr(tool, 'name', str(tool))
                self._tool_registry[tool_name] = server_name

    async def discover_tools(self) -> Dict[str, List[Any]]:
        """Discover all available tools across servers"""
        all_tools = {}
        
        # Add built-in tools first
        builtin_tools = [
            {
                "name": "direct_response",
                "description": "Direct response tool for fallback scenarios",
                "parameters": {
                    "response": {
                        "type": "string",
                        "description": "The response text to return"
                    },
                    "input": {
                        "type": "string", 
                        "description": "Alternative parameter name for response text"
                    }
                }
            }
        ]
        all_tools["builtin"] = builtin_tools
        self._tool_registry["direct_response"] = "builtin"
        
        for server_name, server in self.servers.items():
            if await server.health_check():
                tools = await server.list_tools()
                all_tools[server_name] = tools
                
        return all_tools

    async def execute_single_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        server_name: Optional[str] = None,
        timeout: Optional[float] = None
    ) -> ToolCall:
        """Execute a single tool call"""
        call_id = str(uuid.uuid4())
        tool_call = ToolCall(
            id=call_id,
            name=tool_name,
            arguments=arguments,
            start_time=datetime.now()
        )
        
        try:
            tool_call.status = ToolExecutionStatus.IN_PROGRESS
            
            # Handle built-in tools first
            if tool_name == "direct_response":
                # Built-in direct response tool
                response_text = arguments.get("response", arguments.get("input", "默认响应"))
                result = {
                    "success": True,
                    "response": response_text,
                    "type": "direct_response"
                }
            else:
                # Determine which server to use
                if not server_name:
                    server_name = self._tool_registry.get(tool_name)
                    if not server_name:
                        raise ValueError(f"Tool {tool_name} not found in any server")
                
                server = self.servers.get(server_name)
                if not server:
                    raise ValueError(f"Server {server_name} not available")
                
                # Execute tool
                result = await server.execute_tool(tool_name, arguments, timeout)
            
            tool_call.result = result
            tool_call.status = ToolExecutionStatus.COMPLETED
            tool_call.end_time = datetime.now()
            
        except Exception as e:
            tool_call.error = str(e)
            tool_call.status = ToolExecutionStatus.FAILED
            tool_call.end_time = datetime.now()
            logger.error(f"Tool execution failed: {e}")
            
        finally:
            self.execution_history.append(tool_call)
            
        return tool_call

    def create_execution_graph(self, tool_calls: List[ToolCall]) -> Dict[str, ExecutionNode]:
        """Create execution graph from tool calls"""
        nodes = {}
        
        # Create nodes
        for call in tool_calls:
            node = ExecutionNode(id=call.id, tool_call=call)
            
            # Add dependencies
            for dep_id in call.dependencies:
                node.dependencies.add(dep_id)
                
            nodes[call.id] = node
        
        # Build dependency graph
        for node in nodes.values():
            for dep_id in node.dependencies:
                if dep_id in nodes:
                    nodes[dep_id].dependents.add(node.id)
        
        # Calculate execution order using topological sort
        self._calculate_execution_order(nodes)
        
        return nodes

    def _calculate_execution_order(self, nodes: Dict[str, ExecutionNode]) -> None:
        """Calculate execution order using topological sort"""
        # Kahn's algorithm
        in_degree = {node_id: len(node.dependencies) for node_id, node in nodes.items()}
        queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
        order = 0
        
        while queue:
            current = queue.pop(0)
            nodes[current].execution_order = order
            order += 1
            
            for dependent in nodes[current].dependents:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)

    async def execute_graph(
        self,
        tool_calls: List[ToolCall],
        max_concurrent: int = 5
    ) -> Dict[str, ToolCall]:
        """Execute tool calls as a directed graph"""
        execution_graph = self.create_execution_graph(tool_calls)
        results = {}
        
        # Group by execution order
        order_groups = {}
        for node in execution_graph.values():
            order = node.execution_order
            if order not in order_groups:
                order_groups[order] = []
            order_groups[order].append(node)
        
        # Execute in order with concurrency within each group
        for order in sorted(order_groups.keys()):
            group_nodes = order_groups[order]
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def execute_node(node: ExecutionNode):
                async with semaphore:
                    call = node.tool_call
                    result = await self.execute_single_tool(
                        call.name,
                        call.arguments,
                        timeout=60
                    )
                    return node.id, result
            
            # Execute all nodes in this order group concurrently
            tasks = [execute_node(node) for node in group_nodes]
            group_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in group_results:
                if isinstance(result, Exception):
                    logger.error(f"Node execution failed: {result}")
                    continue
                    
                node_id, tool_result = result
                results[node_id] = tool_result
        
        return results

    async def intelligent_tool_selection(
        self,
        user_input: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[ToolCall]:
        """Use LLM to intelligently select and plan tool execution"""
        available_tools = await self.discover_tools()
        
        # Prepare tools information for LLM
        tools_info = []
        for server_name, tools in available_tools.items():
            for tool in tools:
                tool_info = {
                    "name": getattr(tool, 'name', str(tool)),
                    "description": getattr(tool, 'description', 'No description'),
                    "server": server_name
                }
                tools_info.append(tool_info)
        
        # Create system prompt for tool selection
        system_prompt = f"""
You are an intelligent tool execution planner. Given a user request, analyze available tools and create an optimal execution plan.

Available Tools:
{json.dumps(tools_info, indent=2)}

Context: {json.dumps(context or {}, indent=2)}

Please respond with a JSON array of tool calls in execution order:
[
    {{
        "name": "tool_name",
        "arguments": {{"arg1": "value1"}},
        "dependencies": [],
        "priority": 1
    }}
]

Consider:
1. Tool dependencies and execution order
2. Data flow between tools
3. Parallel execution opportunities
4. Error handling and fallbacks
"""

        messages = [LLMMessage(role="user", content=user_input)]
        
        try:
            response = await self.llm_manager.generate(
                messages=messages,
                system_prompt=system_prompt,
                temperature=0.1
            )
            
            # Parse LLM response
            tool_plan = json.loads(response.content)
            
            # Convert to ToolCall objects
            tool_calls = []
            for plan_item in tool_plan:
                tool_call = ToolCall(
                    id=str(uuid.uuid4()),
                    name=plan_item["name"],
                    arguments=plan_item["arguments"],
                    dependencies=plan_item.get("dependencies", []),
                    priority=plan_item.get("priority", 1)
                )
                tool_calls.append(tool_call)
            
            return tool_calls
            
        except Exception as e:
            logger.error(f"Intelligent tool selection failed: {e}")
            return []

    async def execute_with_intelligence(
        self,
        user_input: str,
        context: Optional[Dict[str, Any]] = None,
        max_concurrent: int = 3
    ) -> Dict[str, Any]:
        """Execute user request with intelligent planning"""
        # Plan tool execution
        tool_calls = await self.intelligent_tool_selection(user_input, context)
        
        if not tool_calls:
            return {"error": "No suitable tools found for the request"}
        
        # Execute planned tools
        results = await self.execute_graph(tool_calls, max_concurrent)
        
        # Analyze results and provide summary
        summary = await self._summarize_execution_results(user_input, results)
        
        return {
            "user_input": user_input,
            "tool_calls": len(tool_calls),
            "successful_calls": len([r for r in results.values() if r.status == ToolExecutionStatus.COMPLETED]),
            "failed_calls": len([r for r in results.values() if r.status == ToolExecutionStatus.FAILED]),
            "results": results,
            "summary": summary
        }

    async def _summarize_execution_results(
        self,
        user_input: str,
        results: Dict[str, ToolCall]
    ) -> str:
        """Summarize execution results using LLM"""
        results_text = []
        for call_id, tool_call in results.items():
            status = tool_call.status.value
            result_text = f"Tool: {tool_call.name}, Status: {status}"
            
            if tool_call.result:
                result_text += f", Result: {str(tool_call.result)[:200]}..."
            if tool_call.error:
                result_text += f", Error: {tool_call.error}"
                
            results_text.append(result_text)
        
        system_prompt = """
You are tasked with summarizing the results of tool execution for a user query.
Provide a concise, helpful summary that explains what was accomplished and any issues encountered.
"""
        
        content = f"""
User Query: {user_input}

Execution Results:
{chr(10).join(results_text)}

Please provide a clear summary of what was accomplished.
"""
        
        messages = [LLMMessage(role="user", content=content)]
        
        try:
            response = await self.llm_manager.generate(
                messages=messages,
                system_prompt=system_prompt,
                temperature=0.3
            )
            return response.content
        except Exception as e:
            logger.error(f"Failed to summarize results: {e}")
            return "Results summary unavailable"

    async def health_check_all(self) -> Dict[str, bool]:
        """Health check all servers"""
        results = {}
        for name, server in self.servers.items():
            results[name] = await server.health_check()
        return results

    async def cleanup(self) -> None:
        """Cleanup all servers"""
        for server in self.servers.values():
            await server.cleanup()
        logger.info("Cleaned up all MCP servers")