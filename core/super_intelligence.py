"""
Super Intelligence Processing Module
超级智能处理 - 集成所有智能功能的核心模块
"""
import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from loguru import logger

from .llm_client import <PERSON><PERSON>anager, LLMMessage
from .memory_manager import GraphitiMemoryManager, MemoryType, RetrievalContext
from .mcp_client import EnhancedMCPClient, Tool<PERSON>all
from .json_adapter import json_adapter, JSONResponse


class IntentType(Enum):
    QUERY = "query"
    OPERATION = "operation" 
    ANALYSIS = "analysis"
    CONVERSATION = "conversation"
    TASK_EXECUTION = "task_execution"
    MEMORY_RETRIEVAL = "memory_retrieval"


class ComplexityLevel(Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    EXPERT = "expert"


@dataclass
class AnalysisResult:
    intent_type: IntentType
    complexity: ComplexityLevel
    confidence: float
    estimated_success_rate: float
    key_concepts: List[str]
    required_tools: List[str]
    data_dependencies: List[str]
    risk_factors: List[str]
    optimization_suggestions: List[str]


@dataclass
class ExecutionStrategy:
    approach: str
    parallel_groups: List[List[str]]
    critical_path: List[str]
    execution_priority: List[int]
    fallback_plans: List[str]
    resource_requirements: Dict[str, Any]
    quality_checkpoints: List[str]
    success_threshold: float


@dataclass
class SuperIntelligenceContext:
    user_id: str
    session_id: str
    user_input: str
    current_cycle: int
    max_cycles: int
    execution_history: List[Dict[str, Any]]
    available_tools: Dict[str, Any]
    memory_context: List[Dict[str, Any]]
    previous_results: List[Dict[str, Any]]
    system_state: Dict[str, Any]


class SuperIntelligenceProcessor:
    """
    超级智能处理器 - 通过单一LLM实现完整的智能处理流水线
    
    功能包括：
    - 意图识别和复杂度分析
    - 任务规划和依赖分析
    - 执行策略制定
    - 质量预评估
    - 循环优化建议
    """
    
    def __init__(
        self,
        llm_manager: LLMManager,
        memory_manager: GraphitiMemoryManager,
        mcp_client: EnhancedMCPClient
    ):
        self.llm_manager = llm_manager
        self.memory_manager = memory_manager
        self.mcp_client = mcp_client
        
        # 超级提示词模板
        self.super_prompt_template = self._build_super_prompt_template()

    def _build_super_prompt_template(self) -> str:
        """构建超级提示词模板"""
        return """
你是一个超级智能任务处理系统，需要完成完整的任务分析和执行规划。

系统状态：
- 当前循环：{current_cycle}/{max_cycles}
- 历史执行：{execution_history}
- 可用工具：{available_tools}
- 知识图谱信息：{memory_context}
- 上次结果：{previous_results}
- 系统状态：{system_state}

用户输入：{user_input}

请分析用户请求并输出完整的执行方案JSON，包含以下所有部分：

1. **深度分析** (analysis):
   - intent_type: "query|operation|analysis|conversation|task_execution|memory_retrieval"
   - complexity: "simple|medium|complex|expert"
   - confidence: 0.0-1.0 (分析置信度)
   - estimated_success_rate: 0.0-1.0 (预估成功率)
   - key_concepts: ["概念1", "概念2"] (关键概念)
   - required_tools: ["工具1", "工具2"] (需要的工具)
   - data_dependencies: ["依赖1", "依赖2"] (数据依赖)
   - risk_factors: ["风险1", "风险2"] (风险因素)
   - optimization_suggestions: ["建议1", "建议2"] (优化建议)

2. **执行图谱** (execution_graph):
   - nodes: [
       {{
         "node_id": "n1",
         "tool_name": "工具名",
         "parameters": {{}},
         "success_criteria": "成功条件",
         "failure_handling": "失败处理方式",
         "estimated_duration": 30,
         "resource_cost": "low|medium|high"
       }}
     ]
   - edges: [
       {{"from": "n1", "to": "n2", "condition": "success", "data_flow": "output_field"}}
     ]
   - parallel_groups: [["n1", "n2"], ["n3"]] (可并行执行的节点组)
   - critical_path: ["n1", "n3"] (关键路径)
   - execution_priority: [1, 2, 3] (执行优先级)

3. **质量保证** (quality_assurance):
   - checkpoints: ["数据完整性", "格式正确性"] (质量检查点)
   - success_threshold: 0.8 (成功阈值)
   - improvement_strategy: "retry|supplement|accept|escalate" (改进策略)
   - validation_rules: ["规则1", "规则2"] (验证规则)

4. **智能优化** (optimization):
   - cycle_prediction: {{
       "likely_issues": ["可能的问题"],
       "prevention_actions": ["预防措施"],
       "fallback_plan": "备用方案"
     }}
   - resource_allocation: {{
       "estimated_duration": 120,
       "memory_requirement": "256MB",
       "api_calls": 5,
       "cost_estimate": "low|medium|high"
     }}
   - performance_tuning: {{
       "concurrency_level": 3,
       "timeout_settings": 60,
       "retry_strategy": "exponential_backoff"
     }}

5. **记忆指令** (memory_instructions):
   - should_remember: ["需要记住的模式或偏好"]
   - search_queries: ["搜索相关知识的查询"]
   - episode_content: "本次交互的内容摘要"
   - memory_type: "user_preference|task_pattern|execution_result|conversation|fact"

6. **循环控制** (loop_control):
   - continue_condition: "在什么条件下继续循环"
   - termination_criteria: "终止条件"
   - next_cycle_focus: "下一轮循环的重点"
   - learning_feedback: "从本轮学到的经验"

重要要求：
1. 基于历史执行经验和记忆上下文进行智能决策
2. 考虑工具的实际能力和限制
3. 预测可能的问题并制定预防措施
4. 优化并行执行和资源利用
5. 提供清晰的成功/失败判断标准
6. 确保输出格式严格符合JSON规范

请输出完整的JSON执行方案：
"""

    async def process_super_intelligence(
        self,
        context: SuperIntelligenceContext
    ) -> Dict[str, Any]:
        """
        执行超级智能处理
        
        Args:
            context: 包含所有上下文信息的对象
            
        Returns:
            完整的智能处理结果
        """
        try:
            logger.info(f"Starting super intelligence processing for user: {context.user_id}")
            
            # 1. 检索相关记忆上下文
            memory_context = await self._retrieve_memory_context(context)
            
            # 2. 获取可用工具信息
            available_tools = await self._get_available_tools()
            
            # 3. 使用JSON适配器构建结构化提示词
            context_data = {
                "user_input": context.user_input,
                "current_cycle": context.current_cycle,
                "max_cycles": context.max_cycles,
                "execution_history": context.execution_history[-3:],
                "available_tools": available_tools,
                "memory_context": memory_context,
                "previous_results": context.previous_results[-2:],
                "system_state": context.system_state
            }
            
            structured_prompt = json_adapter.adapt_super_intelligence_prompt(context_data)
            
            # 4. 调用LLM进行超级智能分析
            messages = [LLMMessage(role="user", content="请分析用户请求并输出JSON格式的执行方案")]
            
            response = await self.llm_manager.generate(
                messages=messages,
                system_prompt=structured_prompt,
                temperature=0.1,  # 使用较低的温度确保一致性
            )
            
            # 5. 使用JSON适配器解析LLM响应
            intelligence_result = json_adapter.parse_super_intelligence_response(response.content)
            
            # 6. 验证和增强结果
            enhanced_result = await self._enhance_intelligence_result(
                intelligence_result, context
            )
            
            # 7. 存储分析结果到记忆
            await self._store_analysis_to_memory(context, enhanced_result)
            
            logger.info("Super intelligence processing completed successfully")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Super intelligence processing failed: {e}")
            return self._create_error_fallback(context, str(e))

    async def _retrieve_memory_context(
        self, 
        context: SuperIntelligenceContext
    ) -> List[Dict[str, Any]]:
        """检索相关记忆上下文"""
        if not self.memory_manager:
            # 如果没有记忆管理器，返回空上下文
            return []
        
        try:
            # 构建多维度搜索
            search_contexts = [
                RetrievalContext(
                    query=f"user {context.user_id} preferences patterns",
                    memory_types=[MemoryType.USER_PREFERENCE, MemoryType.TASK_PATTERN],
                    max_results=5
                ),
                RetrievalContext(
                    query=context.user_input,
                    memory_types=[MemoryType.EXECUTION_RESULT, MemoryType.CONVERSATION],
                    max_results=5
                ),
                RetrievalContext(
                    query="successful execution patterns tools",
                    memory_types=[MemoryType.TASK_PATTERN],
                    max_results=3
                )
            ]
            
            all_memories = []
            for search_ctx in search_contexts:
                memories = await self.memory_manager.retrieve_memories(
                    search_ctx, context.user_id, context.session_id
                )
                all_memories.extend(memories)
            
            # 转换为JSON格式
            memory_data = []
            for memory in all_memories[:10]:  # 限制数量避免提示词过长
                memory_data.append({
                    "type": memory.memory_type.value,
                    "content": memory.content[:200],  # 限制长度
                    "relevance_score": memory.relevance_score,
                    "timestamp": memory.timestamp.isoformat()
                })
            
            return memory_data
            
        except Exception as e:
            logger.warning(f"Failed to retrieve memory context: {e}")
            return []

    async def _get_available_tools(self) -> Dict[str, Any]:
        """获取可用工具信息"""
        try:
            tools_by_server = await self.mcp_client.discover_tools()
            
            # 简化工具信息
            simplified_tools = {}
            for server_name, tools in tools_by_server.items():
                simplified_tools[server_name] = []
                for tool in tools:
                    tool_info = {
                        "name": getattr(tool, 'name', str(tool)),
                        "description": getattr(tool, 'description', 'No description')[:100]
                    }
                    simplified_tools[server_name].append(tool_info)
            
            return simplified_tools
            
        except Exception as e:
            logger.warning(f"Failed to get available tools: {e}")
            return {}

    async def _enhance_intelligence_result(
        self, 
        result: Dict[str, Any], 
        context: SuperIntelligenceContext
    ) -> Dict[str, Any]:
        """验证和增强智能分析结果"""
        # 确保必需的字段存在
        required_sections = [
            "analysis", "execution_graph", "quality_assurance", 
            "optimization", "memory_instructions", "loop_control"
        ]
        
        for section in required_sections:
            if section not in result:
                result[section] = {}
        
        # 添加执行元数据
        result["meta"] = {
            "processing_time": datetime.now(timezone.utc).isoformat(),
            "user_id": context.user_id,
            "session_id": context.session_id,
            "cycle": context.current_cycle,
            "context_hash": str(hash(context.user_input))
        }
        
        # 验证执行图谱的合理性
        if "execution_graph" in result:
            result["execution_graph"] = self._validate_execution_graph(
                result["execution_graph"]
            )
        
        return result

    def _validate_execution_graph(self, graph: Dict[str, Any]) -> Dict[str, Any]:
        """验证执行图谱的合理性"""
        # 确保必需的字段存在
        if "nodes" not in graph:
            graph["nodes"] = []
        if "edges" not in graph:
            graph["edges"] = []
        if "parallel_groups" not in graph:
            graph["parallel_groups"] = []
        if "critical_path" not in graph:
            graph["critical_path"] = []
        if "execution_priority" not in graph:
            graph["execution_priority"] = []
        
        # 验证节点ID的一致性
        node_ids = {node.get("node_id", "") for node in graph["nodes"]}
        
        # 移除无效的边
        valid_edges = []
        for edge in graph["edges"]:
            if (edge.get("from", "") in node_ids and 
                edge.get("to", "") in node_ids):
                valid_edges.append(edge)
        graph["edges"] = valid_edges
        
        return graph

    async def _store_analysis_to_memory(
        self, 
        context: SuperIntelligenceContext, 
        result: Dict[str, Any]
    ) -> None:
        """将分析结果存储到记忆"""
        try:
            # 存储执行模式
            pattern_content = {
                "user_input": context.user_input,
                "analysis_result": result.get("analysis", {}),
                "execution_strategy": result.get("execution_graph", {}),
                "cycle": context.current_cycle
            }
            
            await self.memory_manager.store_execution_pattern(
                user_id=context.user_id,
                session_id=context.session_id,
                task_description=context.user_input,
                execution_plan=pattern_content,
                results={"status": "planned"},
                success=True,
                metadata={"type": "super_intelligence_analysis"}
            )
            
        except Exception as e:
            logger.warning(f"Failed to store analysis to memory: {e}")

    def _create_fallback_plan(self, context: SuperIntelligenceContext) -> Dict[str, Any]:
        """创建备用执行计划"""
        return {
            "analysis": {
                "intent_type": "query",
                "complexity": "simple",
                "confidence": 0.5,
                "estimated_success_rate": 0.7,
                "key_concepts": [context.user_input[:50]],
                "required_tools": [],
                "data_dependencies": [],
                "risk_factors": ["LLM parsing failed"],
                "optimization_suggestions": ["Use simpler approach"]
            },
            "execution_graph": {
                "nodes": [
                    {
                        "node_id": "fallback_1",
                        "tool_name": "direct_response",
                        "parameters": {"input": context.user_input},
                        "success_criteria": "response_generated",
                        "failure_handling": "return_error"
                    }
                ],
                "edges": [],
                "parallel_groups": [["fallback_1"]],
                "critical_path": ["fallback_1"],
                "execution_priority": [1]
            },
            "quality_assurance": {
                "checkpoints": ["basic_response"],
                "success_threshold": 0.6,
                "improvement_strategy": "accept"
            },
            "optimization": {
                "cycle_prediction": {
                    "likely_issues": ["Simple fallback execution"],
                    "prevention_actions": ["Monitor for improvements"],
                    "fallback_plan": "Direct response"
                }
            },
            "memory_instructions": {
                "should_remember": ["Fallback execution used"],
                "search_queries": [],
                "episode_content": f"Fallback processing for: {context.user_input[:100]}"
            },
            "loop_control": {
                "continue_condition": "never",
                "termination_criteria": "immediate",
                "next_cycle_focus": "none",
                "learning_feedback": "Need better LLM parsing"
            }
        }

    def _create_error_fallback(self, context: SuperIntelligenceContext, error: str) -> Dict[str, Any]:
        """创建错误情况下的备用方案"""
        return {
            "error": True,
            "error_message": error,
            "analysis": {
                "intent_type": "query",
                "complexity": "simple",
                "confidence": 0.0,
                "estimated_success_rate": 0.0
            },
            "execution_graph": {
                "nodes": [],
                "edges": []
            },
            "recommendation": "Please try a simpler request or check system status"
        }

    async def extract_execution_plan(self, intelligence_result: Dict[str, Any]) -> List[ToolCall]:
        """从智能分析结果中提取执行计划"""
        tool_calls = []
        
        try:
            execution_graph = intelligence_result.get("execution_graph", {})
            nodes = execution_graph.get("nodes", [])
            
            # 按优先级排序节点
            priorities = execution_graph.get("execution_priority", [])
            if priorities and len(priorities) == len(nodes):
                sorted_nodes = [x for _, x in sorted(zip(priorities, nodes))]
            else:
                sorted_nodes = nodes
            
            # 构建依赖关系图
            edges = execution_graph.get("edges", [])
            dependencies = {}
            for edge in edges:
                to_node = edge.get("to", "")
                from_node = edge.get("from", "")
                if to_node not in dependencies:
                    dependencies[to_node] = []
                dependencies[to_node].append(from_node)
            
            # 创建ToolCall对象
            for node in sorted_nodes:
                node_id = node.get("node_id", str(uuid.uuid4()))
                tool_name = node.get("tool_name", "unknown")
                parameters = node.get("parameters", {})
                node_dependencies = dependencies.get(node_id, [])
                
                tool_call = ToolCall(
                    id=node_id,
                    name=tool_name,
                    arguments=parameters,
                    dependencies=node_dependencies,
                    priority=1
                )
                tool_calls.append(tool_call)
            
            return tool_calls
            
        except Exception as e:
            logger.error(f"Failed to extract execution plan: {e}")
            return []

    async def analyze_execution_quality(
        self, 
        intelligence_result: Dict[str, Any],
        execution_results: Any
    ) -> Dict[str, Any]:
        """分析执行质量"""
        try:
            quality_assurance = intelligence_result.get("quality_assurance", {})
            checkpoints = quality_assurance.get("checkpoints", [])
            success_threshold = quality_assurance.get("success_threshold", 0.8)
            
            # 处理不同类型的执行结果
            if isinstance(execution_results, bool):
                # 简单布尔结果
                success_rate = 1.0 if execution_results else 0.0
                total_tasks = 1
                successful_tasks = 1 if execution_results else 0
            elif isinstance(execution_results, dict):
                # 字典结果
                if "success" in execution_results:
                    success_rate = 1.0 if execution_results["success"] else 0.0
                    total_tasks = execution_results.get("total_nodes", 1)
                    successful_tasks = execution_results.get("completed_nodes", 0)
                else:
                    # 假设是节点结果字典
                    total_tasks = len(execution_results)
                    successful_tasks = sum(
                        1 for result in execution_results.values() 
                        if hasattr(result, 'status') and result.status.value == "completed"
                    )
                    success_rate = successful_tasks / total_tasks if total_tasks > 0 else 0.0
            else:
                # 其他类型，假设失败
                success_rate = 0.0
                total_tasks = 1
                successful_tasks = 0
            
            # 分析质量
            quality_analysis = {
                "overall_success_rate": success_rate,
                "meets_threshold": success_rate >= success_threshold,
                "successful_tasks": successful_tasks,
                "total_tasks": total_tasks,
                "quality_score": min(success_rate * 1.2, 1.0),  # 给予一些奖励
                "checkpoint_results": [],
                "recommendations": []
            }
            
            # 添加改进建议
            if success_rate < success_threshold:
                quality_analysis["recommendations"].extend([
                    "Consider simplifying the task breakdown",
                    "Review tool selection and parameters",
                    "Check for missing dependencies"
                ])
            
            # 分析具体的检查点
            for checkpoint in checkpoints:
                checkpoint_passed = success_rate > 0.5  # 简化的检查点评估
                quality_analysis["checkpoint_results"].append({
                    "checkpoint": checkpoint,
                    "passed": checkpoint_passed,
                    "details": f"Success rate: {success_rate:.2f}"
                })
            
            return quality_analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze execution quality: {e}")
            return {"error": str(e)}

    async def should_continue_loop(
        self,
        intelligence_result: Dict[str, Any],
        quality_analysis: Dict[str, Any],
        current_cycle: int,
        max_cycles: int
    ) -> Tuple[bool, str]:
        """
        决定是否应该继续循环优化
        
        Returns:
            Tuple[是否继续, 原因说明]
        """
        try:
            loop_control = intelligence_result.get("loop_control", {})
            
            # 检查最大循环数限制
            if current_cycle >= max_cycles:
                return False, "Reached maximum cycle limit"
            
            # 检查质量阈值
            quality_score = quality_analysis.get("quality_score", 0.0)
            meets_threshold = quality_analysis.get("meets_threshold", False)
            
            if meets_threshold and quality_score > 0.9:
                return False, "High quality achieved"
            
            # 检查循环控制条件
            continue_condition = loop_control.get("continue_condition", "never")
            termination_criteria = loop_control.get("termination_criteria", "immediate")
            
            if continue_condition == "never":
                return False, "Loop control set to never continue"
            
            if termination_criteria == "immediate":
                return False, "Immediate termination requested"
            
            # 检查是否有改进空间
            success_rate = quality_analysis.get("overall_success_rate", 0.0)
            if success_rate < 0.7 and current_cycle < max_cycles - 1:
                return True, "Low success rate, attempting optimization"
            
            return False, "No clear benefit from continuing"
            
        except Exception as e:
            logger.error(f"Failed to determine loop continuation: {e}")
            return False, f"Error in loop decision: {str(e)}"