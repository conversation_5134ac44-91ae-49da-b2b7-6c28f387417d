"""
Intelligent Loop Controller
智能循环控制器 - 基于预测模型的循环优化系统
"""
import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from loguru import logger

from .super_intelligence import SuperI<PERSON>lligenceProcessor, SuperIntelligenceContext
from .graph_execution_engine import GraphExecutionEngine, ExecutionStrategy
from .memory_manager import GraphitiMemoryManager, MemoryType, MemoryContext


class LoopDecision(Enum):
    CONTINUE = "continue"
    TERMINATE_SUCCESS = "terminate_success"
    TERMINATE_FAILURE = "terminate_failure"
    ESCALATE = "escalate"
    OPTIMIZE = "optimize"


class LoopStrategy(Enum):
    AGGRESSIVE = "aggressive"          # 激进优化，多次循环
    CONSERVATIVE = "conservative"      # 保守策略，少量循环
    ADAPTIVE = "adaptive"             # 自适应策略
    QUALITY_FOCUSED = "quality_focused"  # 质量优先
    SPEED_FOCUSED = "speed_focused"    # 速度优先


@dataclass
class LoopIteration:
    iteration: int
    start_time: datetime
    end_time: Optional[datetime] = None
    intelligence_result: Optional[Dict[str, Any]] = None
    execution_result: Optional[Dict[str, Any]] = None
    quality_score: float = 0.0
    improvement_score: float = 0.0
    resource_cost: float = 0.0
    decision: Optional[LoopDecision] = None
    decision_reason: str = ""
    learned_patterns: List[str] = field(default_factory=list)


@dataclass
class LoopMetrics:
    total_iterations: int = 0
    successful_iterations: int = 0
    failed_iterations: int = 0
    average_quality_score: float = 0.0
    total_execution_time: float = 0.0
    total_resource_cost: float = 0.0
    convergence_rate: float = 0.0
    efficiency_score: float = 0.0


class IntelligentLoopController:
    """
    智能循环控制器
    
    功能特性：
    - 预测性终止：提前预测是否能达到目标质量
    - 增量改进：每次循环只改进最关键的部分
    - 智能回退：检测到无效循环时智能回退到最佳状态
    - 资源感知调度：根据系统资源动态调整策略
    - 学习优化：从历史循环中学习最佳策略
    """
    
    def __init__(
        self,
        super_intelligence: SuperIntelligenceProcessor,
        execution_engine: GraphExecutionEngine,
        memory_manager: GraphitiMemoryManager,
        max_iterations: int = 5,
        quality_threshold: float = 0.85,
        improvement_threshold: float = 0.1,
        resource_limit: float = 1000.0
    ):
        self.super_intelligence = super_intelligence
        self.execution_engine = execution_engine
        self.memory_manager = memory_manager
        
        # 循环控制参数
        self.max_iterations = max_iterations
        self.quality_threshold = quality_threshold
        self.improvement_threshold = improvement_threshold
        self.resource_limit = resource_limit
        
        # 状态跟踪
        self.current_strategy = LoopStrategy.ADAPTIVE
        self.iterations_history: List[LoopIteration] = []
        self.best_iteration: Optional[LoopIteration] = None
        self.convergence_patience = 2
        self.early_stopping_patience = 3
        
        # 回调函数
        self.iteration_callback: Optional[Callable] = None
        self.decision_callback: Optional[Callable] = None

    def set_iteration_callback(self, callback: Callable[[LoopIteration], None]) -> None:
        """设置迭代回调函数"""
        self.iteration_callback = callback

    def set_decision_callback(self, callback: Callable[[LoopDecision, str], None]) -> None:
        """设置决策回调函数"""
        self.decision_callback = callback

    async def execute_intelligent_loop(
        self,
        user_input: str,
        user_id: str,
        session_id: str,
        strategy: LoopStrategy = LoopStrategy.ADAPTIVE,
        execution_strategy: ExecutionStrategy = ExecutionStrategy.HYBRID
    ) -> Dict[str, Any]:
        """
        执行智能循环控制
        
        Args:
            user_input: 用户输入
            user_id: 用户ID
            session_id: 会话ID
            strategy: 循环策略
            execution_strategy: 执行策略
            
        Returns:
            最终的执行结果
        """
        logger.info(f"Starting intelligent loop execution for user: {user_id}")
        
        # 初始化状态
        self.current_strategy = strategy
        self.iterations_history.clear()
        self.best_iteration = None
        
        # 初始化上下文
        context = SuperIntelligenceContext(
            user_id=user_id,
            session_id=session_id,
            user_input=user_input,
            current_cycle=0,
            max_cycles=self.max_iterations,
            execution_history=[],
            available_tools={},
            memory_context=[],
            previous_results=[],
            system_state={}
        )
        
        loop_start_time = time.time()
        
        try:
            # 主循环
            for iteration in range(self.max_iterations):
                logger.info(f"Starting iteration {iteration + 1}/{self.max_iterations}")
                
                # 创建迭代对象
                current_iteration = LoopIteration(
                    iteration=iteration,
                    start_time=datetime.now()
                )
                
                # 更新上下文
                context.current_cycle = iteration
                context.execution_history = self._extract_execution_history()
                context.previous_results = self._extract_previous_results()
                
                try:
                    # 1. 超级智能处理
                    intelligence_result = await self.super_intelligence.process_super_intelligence(context)
                    current_iteration.intelligence_result = intelligence_result
                    
                    # 2. 提取执行计划
                    tool_calls = await self.super_intelligence.extract_execution_plan(intelligence_result)
                    
                    if not tool_calls:
                        logger.warning(f"No tool calls generated in iteration {iteration}")
                        current_iteration.decision = LoopDecision.TERMINATE_FAILURE
                        current_iteration.decision_reason = "No executable plan generated"
                        break
                    
                    # 3. 执行有向图
                    execution_result = await self.execution_engine.execute_graph(
                        tool_calls, execution_strategy
                    )
                    current_iteration.execution_result = execution_result
                    
                    # 4. 分析执行质量
                    quality_analysis = await self.super_intelligence.analyze_execution_quality(
                        intelligence_result, execution_result
                    )
                    current_iteration.quality_score = quality_analysis.get("quality_score", 0.0)
                    
                    # 5. 计算改进分数
                    current_iteration.improvement_score = self._calculate_improvement_score(current_iteration)
                    
                    # 6. 计算资源成本
                    current_iteration.resource_cost = self._calculate_resource_cost(current_iteration)
                    
                    # 7. 学习模式提取
                    current_iteration.learned_patterns = await self._extract_learned_patterns(
                        current_iteration, context
                    )
                    
                    # 8. 决策是否继续循环
                    decision, reason = await self._make_loop_decision(current_iteration, context)
                    current_iteration.decision = decision
                    current_iteration.decision_reason = reason
                    
                    # 结束当前迭代
                    current_iteration.end_time = datetime.now()
                    self.iterations_history.append(current_iteration)
                    
                    # 更新最佳迭代
                    if (self.best_iteration is None or 
                        current_iteration.quality_score > self.best_iteration.quality_score):
                        self.best_iteration = current_iteration
                    
                    # 调用迭代回调
                    if self.iteration_callback:
                        self.iteration_callback(current_iteration)
                    
                    # 调用决策回调
                    if self.decision_callback:
                        self.decision_callback(decision, reason)
                    
                    # 存储迭代记忆
                    await self._store_iteration_memory(current_iteration, context)
                    
                    # 检查是否应该终止
                    if decision != LoopDecision.CONTINUE:
                        logger.info(f"Loop terminated: {reason}")
                        break
                        
                except Exception as e:
                    logger.error(f"Iteration {iteration} failed: {e}")
                    current_iteration.decision = LoopDecision.TERMINATE_FAILURE
                    current_iteration.decision_reason = f"Iteration error: {str(e)}"
                    current_iteration.end_time = datetime.now()
                    self.iterations_history.append(current_iteration)
                    break
            
            # 生成最终结果
            final_result = await self._generate_final_result(
                context, loop_start_time
            )
            
            # 存储循环总结到记忆
            await self._store_loop_summary(context, final_result)
            
            return final_result
            
        except Exception as e:
            logger.error(f"Intelligent loop execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "iterations": len(self.iterations_history),
                "execution_time": time.time() - loop_start_time
            }

    async def _make_loop_decision(
        self, 
        current_iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> Tuple[LoopDecision, str]:
        """做出循环决策"""
        try:
            # 1. 检查基本终止条件
            
            # 达到最大迭代数
            if current_iteration.iteration >= self.max_iterations - 1:
                return LoopDecision.TERMINATE_SUCCESS, "Reached maximum iterations"
            
            # 质量阈值达标
            if current_iteration.quality_score >= self.quality_threshold:
                return LoopDecision.TERMINATE_SUCCESS, f"Quality threshold achieved: {current_iteration.quality_score:.3f}"
            
            # 资源耗尽
            total_resource_cost = sum(iter.resource_cost for iter in self.iterations_history)
            if total_resource_cost >= self.resource_limit:
                return LoopDecision.TERMINATE_FAILURE, "Resource limit exceeded"
            
            # 执行失败
            if not current_iteration.execution_result or not current_iteration.execution_result.get("success", False):
                return LoopDecision.TERMINATE_FAILURE, "Execution failed"
            
            # 2. 基于策略的决策
            if self.current_strategy == LoopStrategy.AGGRESSIVE:
                return await self._aggressive_decision(current_iteration, context)
            elif self.current_strategy == LoopStrategy.CONSERVATIVE:
                return await self._conservative_decision(current_iteration, context)
            elif self.current_strategy == LoopStrategy.ADAPTIVE:
                return await self._adaptive_decision(current_iteration, context)
            elif self.current_strategy == LoopStrategy.QUALITY_FOCUSED:
                return await self._quality_focused_decision(current_iteration, context)
            elif self.current_strategy == LoopStrategy.SPEED_FOCUSED:
                return await self._speed_focused_decision(current_iteration, context)
            else:
                return await self._adaptive_decision(current_iteration, context)
                
        except Exception as e:
            logger.error(f"Loop decision error: {e}")
            return LoopDecision.TERMINATE_FAILURE, f"Decision error: {str(e)}"

    async def _aggressive_decision(
        self, 
        current_iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> Tuple[LoopDecision, str]:
        """激进策略决策"""
        # 激进策略：只要有改进就继续
        if current_iteration.improvement_score > 0.05:
            return LoopDecision.CONTINUE, "Aggressive: Continuing with any improvement"
        
        # 质量较低但还有机会
        if current_iteration.quality_score < 0.7 and current_iteration.iteration < 4:
            return LoopDecision.CONTINUE, "Aggressive: Quality low, continue optimization"
        
        return LoopDecision.TERMINATE_SUCCESS, "Aggressive: No significant improvement"

    async def _conservative_decision(
        self, 
        current_iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> Tuple[LoopDecision, str]:
        """保守策略决策"""
        # 保守策略：高质量或明显改进才继续
        if (current_iteration.quality_score >= 0.8 and 
            current_iteration.improvement_score > self.improvement_threshold):
            return LoopDecision.CONTINUE, "Conservative: High quality with improvement"
        
        # 第一次迭代质量不错就满足
        if current_iteration.iteration == 0 and current_iteration.quality_score >= 0.7:
            return LoopDecision.TERMINATE_SUCCESS, "Conservative: First iteration sufficient"
        
        return LoopDecision.TERMINATE_SUCCESS, "Conservative: Acceptable result achieved"

    async def _adaptive_decision(
        self, 
        current_iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> Tuple[LoopDecision, str]:
        """自适应策略决策"""
        # 分析历史趋势
        if len(self.iterations_history) >= 2:
            # 检查收敛性
            recent_improvements = [
                iter.improvement_score for iter in self.iterations_history[-2:]
            ]
            
            # 改进在下降，可能已收敛
            if all(imp < 0.05 for imp in recent_improvements):
                return LoopDecision.TERMINATE_SUCCESS, "Adaptive: Convergence detected"
            
            # 质量在波动，可能需要不同的方法
            recent_qualities = [
                iter.quality_score for iter in self.iterations_history[-2:]
            ]
            if max(recent_qualities) - min(recent_qualities) > 0.2:
                return LoopDecision.OPTIMIZE, "Adaptive: Quality fluctuation, need optimization"
        
        # 标准改进检查
        if current_iteration.improvement_score > self.improvement_threshold:
            return LoopDecision.CONTINUE, "Adaptive: Significant improvement detected"
        
        # 质量还不够，但有希望
        if current_iteration.quality_score < self.quality_threshold and current_iteration.iteration < 3:
            return LoopDecision.CONTINUE, "Adaptive: Quality below threshold, continue"
        
        return LoopDecision.TERMINATE_SUCCESS, "Adaptive: Optimal balance achieved"

    async def _quality_focused_decision(
        self, 
        current_iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> Tuple[LoopDecision, str]:
        """质量优先策略决策"""
        # 质量优先：只关注质量提升
        if current_iteration.quality_score < self.quality_threshold:
            if current_iteration.iteration < self.max_iterations - 1:
                return LoopDecision.CONTINUE, "Quality-focused: Below threshold, continue"
            else:
                return LoopDecision.ESCALATE, "Quality-focused: Threshold not met, escalate"
        
        return LoopDecision.TERMINATE_SUCCESS, "Quality-focused: Threshold achieved"

    async def _speed_focused_decision(
        self, 
        current_iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> Tuple[LoopDecision, str]:
        """速度优先策略决策"""
        # 速度优先：快速得到可接受的结果
        if current_iteration.quality_score >= 0.6:  # 降低质量要求
            return LoopDecision.TERMINATE_SUCCESS, "Speed-focused: Acceptable quality achieved quickly"
        
        # 最多2次迭代
        if current_iteration.iteration >= 1:
            return LoopDecision.TERMINATE_SUCCESS, "Speed-focused: Speed priority, terminate early"
        
        return LoopDecision.CONTINUE, "Speed-focused: One more quick iteration"

    def _calculate_improvement_score(self, current_iteration: LoopIteration) -> float:
        """计算改进分数"""
        if not self.iterations_history or len(self.iterations_history) == 0:
            return 0.0
        
        if len(self.iterations_history) == 1:
            # 第一次迭代，基准是0
            return current_iteration.quality_score
        
        # 与上一次迭代比较
        previous_quality = self.iterations_history[-2].quality_score
        improvement = current_iteration.quality_score - previous_quality
        
        # 归一化改进分数
        return max(0.0, min(1.0, improvement))

    def _calculate_resource_cost(self, iteration: LoopIteration) -> float:
        """计算资源成本"""
        base_cost = 10.0  # 基础成本
        
        # 执行时间成本
        if iteration.execution_result:
            exec_time = iteration.execution_result.get("execution_time", 0)
            time_cost = exec_time * 0.1
        else:
            time_cost = 0
        
        # 复杂度成本
        if iteration.intelligence_result:
            analysis = iteration.intelligence_result.get("analysis", {})
            complexity = analysis.get("complexity", "simple")
            complexity_cost = {"simple": 1, "medium": 2, "complex": 4, "expert": 8}.get(complexity, 2)
        else:
            complexity_cost = 1
        
        return base_cost + time_cost + complexity_cost

    async def _extract_learned_patterns(
        self, 
        iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> List[str]:
        """提取学习到的模式"""
        patterns = []
        
        try:
            # 从智能处理结果中提取模式
            if iteration.intelligence_result:
                analysis = iteration.intelligence_result.get("analysis", {})
                optimization = iteration.intelligence_result.get("optimization", {})
                
                # 成功的工具组合
                if iteration.execution_result and iteration.execution_result.get("success"):
                    required_tools = analysis.get("required_tools", [])
                    if required_tools:
                        patterns.append(f"Successful tool combination: {', '.join(required_tools)}")
                
                # 有效的优化建议
                suggestions = analysis.get("optimization_suggestions", [])
                if suggestions:
                    patterns.extend([f"Optimization: {s}" for s in suggestions[:2]])
                
                # 预防措施
                prevention_actions = optimization.get("cycle_prediction", {}).get("prevention_actions", [])
                if prevention_actions:
                    patterns.extend([f"Prevention: {a}" for a in prevention_actions[:2]])
            
            # 从执行结果中提取模式
            if iteration.execution_result:
                if iteration.execution_result.get("success"):
                    success_rate = iteration.execution_result.get("completed_nodes", 0) / max(1, iteration.execution_result.get("total_nodes", 1))
                    if success_rate > 0.8:
                        patterns.append(f"High success rate execution pattern: {success_rate:.2f}")
                
                # 关键路径模式
                critical_path = iteration.execution_result.get("critical_path", [])
                if critical_path:
                    patterns.append(f"Critical path pattern: {len(critical_path)} nodes")
            
        except Exception as e:
            logger.warning(f"Pattern extraction error: {e}")
        
        return patterns

    def _extract_execution_history(self) -> List[Dict[str, Any]]:
        """提取执行历史"""
        history = []
        for iter in self.iterations_history[-3:]:  # 最近3次
            if iter.execution_result:
                history.append({
                    "iteration": iter.iteration,
                    "success": iter.execution_result.get("success", False),
                    "quality_score": iter.quality_score,
                    "execution_time": iter.execution_result.get("execution_time", 0)
                })
        return history

    def _extract_previous_results(self) -> List[Dict[str, Any]]:
        """提取之前的结果"""
        results = []
        for iter in self.iterations_history[-2:]:  # 最近2次结果
            if iter.execution_result:
                results.append({
                    "quality_score": iter.quality_score,
                    "improvement_score": iter.improvement_score,
                    "learned_patterns": iter.learned_patterns
                })
        return results

    async def _store_iteration_memory(
        self, 
        iteration: LoopIteration, 
        context: SuperIntelligenceContext
    ) -> None:
        """存储迭代记忆"""
        if not self.memory_manager:
            # 如果没有记忆管理器，跳过存储
            return
            
        try:
            # 存储执行模式
            pattern_content = {
                "iteration": iteration.iteration,
                "quality_score": iteration.quality_score,
                "improvement_score": iteration.improvement_score,
                "decision": iteration.decision.value if iteration.decision else None,
                "decision_reason": iteration.decision_reason,
                "learned_patterns": iteration.learned_patterns,
                "strategy": self.current_strategy.value
            }
            
            success = iteration.decision in [LoopDecision.TERMINATE_SUCCESS, LoopDecision.CONTINUE]
            memory_type = MemoryType.TASK_PATTERN if success else MemoryType.ERROR_CASE
            
            await self.memory_manager.store_execution_pattern(
                user_id=context.user_id,
                session_id=context.session_id,
                task_description=f"Loop iteration {iteration.iteration}: {context.user_input}",
                execution_plan=pattern_content,
                results={"iteration_result": iteration.execution_result},
                success=success,
                metadata={"type": "loop_iteration", "strategy": self.current_strategy.value}
            )
            
        except Exception as e:
            logger.warning(f"Failed to store iteration memory: {e}")

    async def _store_loop_summary(
        self, 
        context: SuperIntelligenceContext, 
        final_result: Dict[str, Any]
    ) -> None:
        """存储循环总结"""
        if not self.memory_manager:
            # 如果没有记忆管理器，跳过存储
            return
            
        try:
            summary_content = {
                "total_iterations": len(self.iterations_history),
                "strategy_used": self.current_strategy.value,
                "final_quality": final_result.get("best_quality_score", 0.0),
                "total_execution_time": final_result.get("total_execution_time", 0.0),
                "success": final_result.get("success", False),
                "key_insights": final_result.get("key_insights", [])
            }
            
            memory_context = MemoryContext(
                user_id=context.user_id,
                session_id=context.session_id,
                memory_type=MemoryType.TASK_PATTERN,
                content=json.dumps(summary_content),
                metadata={"type": "loop_summary"},
                timestamp=datetime.now()
            )
            
            await self.memory_manager.store_memory(memory_context)
            
        except Exception as e:
            logger.warning(f"Failed to store loop summary: {e}")

    async def _generate_final_result(
        self, 
        context: SuperIntelligenceContext, 
        loop_start_time: float
    ) -> Dict[str, Any]:
        """生成最终结果"""
        # 计算循环指标
        metrics = self._calculate_loop_metrics()
        
        # 获取最佳结果
        best_result = None
        if self.best_iteration and self.best_iteration.execution_result:
            best_result = self.best_iteration.execution_result
        
        # 提取关键见解
        key_insights = []
        for iteration in self.iterations_history:
            key_insights.extend(iteration.learned_patterns)
        
        # 去重见解
        key_insights = list(set(key_insights))
        
        final_result = {
            "success": metrics.successful_iterations > 0,
            "total_iterations": metrics.total_iterations,
            "successful_iterations": metrics.successful_iterations,
            "best_quality_score": self.best_iteration.quality_score if self.best_iteration else 0.0,
            "average_quality_score": metrics.average_quality_score,
            "total_execution_time": time.time() - loop_start_time,
            "convergence_rate": metrics.convergence_rate,
            "efficiency_score": metrics.efficiency_score,
            "strategy_used": self.current_strategy.value,
            "best_result": best_result,
            "key_insights": key_insights[:10],  # 最多10个见解
            "iterations_summary": [
                {
                    "iteration": iter.iteration,
                    "quality_score": iter.quality_score,
                    "decision": iter.decision.value if iter.decision else None,
                    "decision_reason": iter.decision_reason
                }
                for iter in self.iterations_history
            ],
            "metrics": {
                "total_resource_cost": metrics.total_resource_cost,
                "average_iteration_time": metrics.total_execution_time / max(1, metrics.total_iterations),
                "quality_improvement_rate": self._calculate_quality_improvement_rate()
            }
        }
        
        return final_result

    def _calculate_loop_metrics(self) -> LoopMetrics:
        """计算循环指标"""
        if not self.iterations_history:
            return LoopMetrics()
        
        successful_iterations = sum(
            1 for iter in self.iterations_history 
            if iter.decision in [LoopDecision.TERMINATE_SUCCESS, LoopDecision.CONTINUE]
        )
        
        total_quality = sum(iter.quality_score for iter in self.iterations_history)
        average_quality = total_quality / len(self.iterations_history)
        
        total_time = sum(
            (iter.end_time - iter.start_time).total_seconds() 
            for iter in self.iterations_history 
            if iter.end_time
        )
        
        total_cost = sum(iter.resource_cost for iter in self.iterations_history)
        
        # 计算收敛率
        convergence_rate = 0.0
        if len(self.iterations_history) > 1:
            quality_changes = []
            for i in range(1, len(self.iterations_history)):
                change = abs(self.iterations_history[i].quality_score - 
                           self.iterations_history[i-1].quality_score)
                quality_changes.append(change)
            
            if quality_changes:
                convergence_rate = 1.0 - (sum(quality_changes) / len(quality_changes))
        
        # 计算效率分数
        efficiency_score = 0.0
        if total_time > 0 and total_cost > 0:
            efficiency_score = (average_quality * successful_iterations) / (total_time * total_cost) * 100
        
        return LoopMetrics(
            total_iterations=len(self.iterations_history),
            successful_iterations=successful_iterations,
            failed_iterations=len(self.iterations_history) - successful_iterations,
            average_quality_score=average_quality,
            total_execution_time=total_time,
            total_resource_cost=total_cost,
            convergence_rate=max(0.0, min(1.0, convergence_rate)),
            efficiency_score=max(0.0, min(100.0, efficiency_score))
        )

    def _calculate_quality_improvement_rate(self) -> float:
        """计算质量改进率"""
        if len(self.iterations_history) < 2:
            return 0.0
        
        first_quality = self.iterations_history[0].quality_score
        best_quality = max(iter.quality_score for iter in self.iterations_history)
        
        if first_quality == 0:
            return 0.0
        
        return (best_quality - first_quality) / first_quality