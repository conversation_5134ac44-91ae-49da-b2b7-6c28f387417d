"""
Graphiti Embedder Wrapper
"""
from typing import List
from graphiti_core.embedder import EmbedderClient
from .embedding_client import BaseEmbeddingClient

class GraphitiEmbedder(EmbedderClient):
    """
    A wrapper class to make our custom embedding clients compatible with Graphiti.
    """
    def __init__(self, embedding_client: BaseEmbeddingClient):
        self.embedding_client = embedding_client

    async def create(self, input_data):
        """
        Creates embeddings for input data.
        This is called by <PERSON><PERSON><PERSON><PERSON> during search operations.
        """
        if isinstance(input_data, str):
            # Single string input
            response = await self.embedding_client.encode([input_data])
            return response.embeddings[0] if response.embeddings else [0.0] * 1024
        elif isinstance(input_data, list):
            # List of strings
            response = await self.embedding_client.encode(input_data)
            return response.embeddings if response.embeddings else [[0.0] * 1024 for _ in input_data]
        else:
            # Fallback for other types
            return [0.0] * 1024

    async def create_batch(self, input_data_list: List[str]) -> List[List[float]]:
        """
        Creates embeddings for a batch of input strings.
        This is the method that <PERSON><PERSON><PERSON><PERSON> expects.
        """
        if not input_data_list:
            return []
        
        try:
            response = await self.embedding_client.encode(input_data_list)
            return response.embeddings
        except Exception as e:
            # Fallback: process one by one if batch fails
            embeddings = []
            for text in input_data_list:
                try:
                    single_response = await self.embedding_client.encode([text])
                    if single_response.embeddings:
                        embeddings.append(single_response.embeddings[0])
                    else:
                        # Fallback: zero vector
                        embeddings.append([0.0] * (self.embedding_client.dimension or 1024))
                except:
                    # Final fallback: zero vector
                    embeddings.append([0.0] * (self.embedding_client.dimension or 1024))
            return embeddings

    async def embed(self, texts: List[str]) -> List[List[float]]:
        """
        Embeds a list of texts using the wrapped embedding client.
        This method delegates to create_batch for consistency.
        """
        return await self.create_batch(texts)

    @property
    def dimension(self) -> int:
        """
        Returns the dimension of the embeddings.
        """
        return self.embedding_client.dimension or 1024
