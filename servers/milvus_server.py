"""
Milvus Vector Database MCP Server
"""
import asyncio
import json
import os
import uuid
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime

import numpy as np
from pymilvus import (
    connections, 
    Collection, 
    CollectionSchema, 
    FieldSchema, 
    DataType,
    utility
)
from mcp.server.fastmcp import FastMC<PERSON>, Context
from mcp.types import TextContent
from loguru import logger

from ..core.embedding_client import EmbeddingManager, EmbeddingClientFactory


@dataclass
class DocumentChunk:
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    created_at: Optional[datetime] = None


@dataclass
class SearchResult:
    id: str
    content: str
    metadata: Dict[str, Any]
    score: float


class MilvusVectorDB:
    """Milvus Vector Database Client"""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 19530,
        db_name: str = "mcp_rag",
        collection_name: str = "documents",
        dimension: int = 1536
    ):
        self.host = host
        self.port = port
        self.db_name = db_name
        self.collection_name = collection_name
        self.dimension = dimension
        self.collection: Optional[Collection] = None
        self._connected = False

    async def initialize(self) -> None:
        """Initialize Milvus connection and collection"""
        try:
            # Connect to Milvus
            connections.connect(
                alias="default",
                host=self.host,
                port=self.port
            )
            self._connected = True
            
            # Create collection if it doesn't exist
            if not utility.has_collection(self.collection_name):
                await self._create_collection()
            else:
                self.collection = Collection(self.collection_name)
                
            # Create index if it doesn't exist
            if not self.collection.has_index():
                await self._create_index()
                
            # Load collection
            self.collection.load()
            
            logger.info(f"Initialized Milvus DB: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Milvus: {e}")
            raise

    async def _create_collection(self) -> None:
        """Create Milvus collection with schema"""
        fields = [
            FieldSchema(
                name="id", 
                dtype=DataType.VARCHAR, 
                is_primary=True, 
                max_length=128
            ),
            FieldSchema(
                name="content", 
                dtype=DataType.VARCHAR, 
                max_length=65535
            ),
            FieldSchema(
                name="metadata", 
                dtype=DataType.VARCHAR, 
                max_length=65535
            ),
            FieldSchema(
                name="embedding", 
                dtype=DataType.FLOAT_VECTOR, 
                dim=self.dimension
            ),
            FieldSchema(
                name="created_at", 
                dtype=DataType.VARCHAR, 
                max_length=64
            )
        ]
        
        schema = CollectionSchema(
            fields=fields,
            description="RAG Document Collection"
        )
        
        self.collection = Collection(
            name=self.collection_name,
            schema=schema
        )

    async def _create_index(self) -> None:
        """Create vector index for efficient search"""
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        
        self.collection.create_index(
            field_name="embedding",
            index_params=index_params
        )

    async def insert_documents(self, documents: List[DocumentChunk]) -> List[str]:
        """Insert document chunks into collection"""
        if not self._connected:
            await self.initialize()
            
        entities = [
            [doc.id for doc in documents],
            [doc.content for doc in documents],
            [json.dumps(doc.metadata) for doc in documents],
            [doc.embedding for doc in documents],
            [(doc.created_at or datetime.now()).isoformat() for doc in documents]
        ]
        
        insert_result = self.collection.insert(entities)
        await self._flush()
        
        logger.info(f"Inserted {len(documents)} documents")
        return insert_result.primary_keys

    async def search_similar(
        self,
        query_embedding: List[float],
        top_k: int = 5,
        score_threshold: float = 0.0,
        metadata_filter: Optional[str] = None
    ) -> List[SearchResult]:
        """Search for similar documents"""
        if not self._connected:
            await self.initialize()
            
        search_params = {
            "metric_type": "COSINE",
            "params": {"nprobe": 10}
        }
        
        # Build expression for metadata filtering
        expr = None
        if metadata_filter:
            expr = metadata_filter
            
        results = self.collection.search(
            data=[query_embedding],
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            expr=expr,
            output_fields=["content", "metadata", "created_at"]
        )
        
        search_results = []
        for hits in results:
            for hit in hits:
                if hit.score >= score_threshold:
                    try:
                        metadata = json.loads(hit.entity.get("metadata", "{}"))
                    except:
                        metadata = {}
                        
                    result = SearchResult(
                        id=hit.id,
                        content=hit.entity.get("content", ""),
                        metadata=metadata,
                        score=float(hit.score)
                    )
                    search_results.append(result)
        
        return search_results

    async def delete_documents(self, document_ids: List[str]) -> int:
        """Delete documents by IDs"""
        if not self._connected:
            await self.initialize()
            
        expr = f"id in {document_ids}"
        delete_result = self.collection.delete(expr)
        await self._flush()
        
        logger.info(f"Deleted {delete_result.delete_count} documents")
        return delete_result.delete_count

    async def update_document(self, document: DocumentChunk) -> bool:
        """Update a document (delete and re-insert)"""
        # Delete existing
        await self.delete_documents([document.id])
        
        # Insert new
        inserted_ids = await self.insert_documents([document])
        return len(inserted_ids) > 0

    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        if not self._connected:
            await self.initialize()
            
        stats = self.collection.get_stats()
        return {
            "num_entities": stats.get("row_count", 0),
            "collection_name": self.collection_name,
            "dimension": self.dimension
        }

    async def _flush(self) -> None:
        """Flush data to persist changes"""
        self.collection.flush()

    async def cleanup(self) -> None:
        """Cleanup connection"""
        if self._connected:
            try:
                connections.disconnect("default")
                self._connected = False
                logger.info("Disconnected from Milvus")
            except Exception as e:
                logger.error(f"Error disconnecting from Milvus: {e}")


# Create MCP Server instance
mcp_app = FastMCP("Milvus Vector Database Server")

# Global instances
vector_db: Optional[MilvusVectorDB] = None
embedding_manager: Optional[EmbeddingManager] = None


@mcp_app.resource("milvus://collection/{collection_name}")
async def get_collection_info(collection_name: str) -> str:
    """Get collection information"""
    global vector_db
    
    if not vector_db:
        return json.dumps({"error": "Database not initialized"})
        
    try:
        stats = await vector_db.get_collection_stats()
        return json.dumps(stats, indent=2)
    except Exception as e:
        return json.dumps({"error": str(e)})


@mcp_app.tool()
async def initialize_milvus(
    host: str = "localhost",
    port: int = 19530,
    collection_name: str = "documents",
    dimension: int = 1536,
    ctx: Context = None
) -> str:
    """Initialize Milvus vector database connection"""
    global vector_db, embedding_manager
    
    try:
        # Initialize vector database
        vector_db = MilvusVectorDB(
            host=host,
            port=port,
            collection_name=collection_name,
            dimension=dimension
        )
        await vector_db.initialize()
        
        # Initialize embedding manager
        embedding_manager = EmbeddingManager()
        
        # Add default embedding client
        embedding_client = EmbeddingClientFactory.create_client(
            provider="sentence_transformers",
            model="all-MiniLM-L6-v2"
        )
        await embedding_manager.add_client("default", embedding_client)
        
        if ctx:
            await ctx.info(f"Initialized Milvus connection to {host}:{port}")
            
        return json.dumps({
            "status": "success",
            "message": f"Connected to Milvus at {host}:{port}",
            "collection": collection_name,
            "dimension": dimension
        })
        
    except Exception as e:
        error_msg = f"Failed to initialize Milvus: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return json.dumps({"status": "error", "error": error_msg})


@mcp_app.tool()
async def add_documents(
    documents: List[Dict[str, Any]],
    ctx: Context = None
) -> str:
    """Add documents to the vector database"""
    global vector_db, embedding_manager
    
    if not vector_db or not embedding_manager:
        return json.dumps({"error": "Database not initialized. Call initialize_milvus first."})
    
    try:
        document_chunks = []
        
        for i, doc in enumerate(documents):
            content = doc.get("content", "")
            metadata = doc.get("metadata", {})
            doc_id = doc.get("id", str(uuid.uuid4()))
            
            if ctx and i % 10 == 0:
                await ctx.report_progress(
                    progress=i,
                    total=len(documents),
                    message=f"Processing document {i+1}/{len(documents)}"
                )
            
            # Generate embedding
            embedding = await embedding_manager.encode_single(content)
            
            chunk = DocumentChunk(
                id=doc_id,
                content=content,
                metadata=metadata,
                embedding=embedding,
                created_at=datetime.now()
            )
            document_chunks.append(chunk)
        
        # Insert into database
        inserted_ids = await vector_db.insert_documents(document_chunks)
        
        if ctx:
            await ctx.info(f"Successfully added {len(inserted_ids)} documents")
            
        return json.dumps({
            "status": "success",
            "inserted_count": len(inserted_ids),
            "document_ids": inserted_ids
        })
        
    except Exception as e:
        error_msg = f"Failed to add documents: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return json.dumps({"status": "error", "error": error_msg})


@mcp_app.tool()
async def search_documents(
    query: str,
    top_k: int = 5,
    score_threshold: float = 0.0,
    metadata_filter: str = "",
    ctx: Context = None
) -> str:
    """Search for similar documents using semantic search"""
    global vector_db, embedding_manager
    
    if not vector_db or not embedding_manager:
        return json.dumps({"error": "Database not initialized. Call initialize_milvus first."})
    
    try:
        # Generate query embedding
        if ctx:
            await ctx.info(f"Generating embedding for query: {query[:100]}...")
            
        query_embedding = await embedding_manager.encode_single(query)
        
        # Search similar documents
        results = await vector_db.search_similar(
            query_embedding=query_embedding,
            top_k=top_k,
            score_threshold=score_threshold,
            metadata_filter=metadata_filter if metadata_filter else None
        )
        
        # Format results
        formatted_results = []
        for result in results:
            formatted_results.append({
                "id": result.id,
                "content": result.content,
                "metadata": result.metadata,
                "score": result.score
            })
        
        if ctx:
            await ctx.info(f"Found {len(results)} relevant documents")
            
        return json.dumps({
            "status": "success",
            "query": query,
            "results_count": len(formatted_results),
            "results": formatted_results
        })
        
    except Exception as e:
        error_msg = f"Failed to search documents: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return json.dumps({"status": "error", "error": error_msg})


@mcp_app.tool()
async def delete_documents_by_ids(
    document_ids: List[str],
    ctx: Context = None
) -> str:
    """Delete documents by their IDs"""
    global vector_db
    
    if not vector_db:
        return json.dumps({"error": "Database not initialized. Call initialize_milvus first."})
    
    try:
        deleted_count = await vector_db.delete_documents(document_ids)
        
        if ctx:
            await ctx.info(f"Deleted {deleted_count} documents")
            
        return json.dumps({
            "status": "success",
            "deleted_count": deleted_count,
            "requested_ids": document_ids
        })
        
    except Exception as e:
        error_msg = f"Failed to delete documents: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return json.dumps({"status": "error", "error": error_msg})


@mcp_app.tool()
async def get_database_stats(ctx: Context = None) -> str:
    """Get database statistics"""
    global vector_db
    
    if not vector_db:
        return json.dumps({"error": "Database not initialized. Call initialize_milvus first."})
    
    try:
        stats = await vector_db.get_collection_stats()
        
        if ctx:
            await ctx.info(f"Retrieved stats for collection: {stats.get('collection_name')}")
            
        return json.dumps({
            "status": "success",
            "stats": stats
        })
        
    except Exception as e:
        error_msg = f"Failed to get database stats: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return json.dumps({"status": "error", "error": error_msg})


@mcp_app.tool()
async def configure_embedding_client(
    provider: str,
    model: str,
    api_key: str = "",
    client_name: str = "default",
    ctx: Context = None
) -> str:
    """Configure embedding client for the vector database"""
    global embedding_manager
    
    if not embedding_manager:
        embedding_manager = EmbeddingManager()
    
    try:
        # Create embedding client
        embedding_client = EmbeddingClientFactory.create_client(
            provider=provider,
            model=model,
            api_key=api_key if api_key else None
        )
        
        # Add to manager
        await embedding_manager.add_client(client_name, embedding_client)
        
        if client_name == "default" or len(embedding_manager.clients) == 1:
            await embedding_manager.set_primary(client_name)
        
        if ctx:
            await ctx.info(f"Configured embedding client: {provider}/{model}")
            
        return json.dumps({
            "status": "success",
            "provider": provider,
            "model": model,
            "client_name": client_name,
            "dimension": embedding_client.dimension
        })
        
    except Exception as e:
        error_msg = f"Failed to configure embedding client: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return json.dumps({"status": "error", "error": error_msg})


if __name__ == "__main__":
    # Run the MCP server
    mcp_app.run(transport="stdio")