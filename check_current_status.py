#!/usr/bin/env python3
"""
检查当前Graphiti状态
"""
import asyncio

async def check_graphiti_status():
    """检查Graphiti数据库当前状态"""
    try:
        from neo4j import AsyncGraphDatabase
        
        driver = AsyncGraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '00000000'))
        
        async with driver.session() as session:
            print("🔍 当前数据库状态:")
            
            # 检查所有节点
            result = await session.run('MATCH (n) RETURN labels(n) as labels, count(n) as count')
            records = await result.data()
            
            total_entities = 0
            total_episodic = 0
            
            for record in records:
                labels = record['labels']
                count = record['count']
                print(f"  {labels}: {count} 个")
                if 'Entity' in labels:
                    total_entities += count
                elif 'Episodic' in labels:
                    total_episodic += count
            
            # 检查关系
            result = await session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count')
            records = await result.data()
            
            total_relations = 0
            print("\n  关系统计:")
            for record in records:
                rel_type = record['rel_type']
                count = record['count']
                print(f"    {rel_type}: {count} 个")
                if rel_type == 'RELATES_TO':
                    total_relations += count
            
            # 查看最近的Episodic内容
            print(f"\n📝 最近的Episodes内容:")
            result = await session.run('''
                MATCH (e:Episodic) 
                RETURN e.name as name, e.content as content, e.created_at as created_at
                ORDER BY e.created_at DESC 
                LIMIT 3
            ''')
            records = await result.data()
            
            for i, record in enumerate(records, 1):
                name = record.get('name', 'Unknown')
                content = record.get('content', '')
                created_at = record.get('created_at', '')
                print(f"  {i}. {name}")
                print(f"     内容: {content[:100]}...")
                print(f"     时间: {created_at}")
                print()
            
            # 如果有Entity节点，查看它们
            if total_entities > 0:
                print(f"📊 Entity节点详情:")
                result = await session.run('MATCH (n:Entity) RETURN n.name as name, properties(n) as props LIMIT 3')
                records = await result.data()
                
                for record in records:
                    name = record.get('name', 'Unknown')
                    props = record.get('props', {})
                    print(f"  实体: {name}")
                    print(f"  属性: {list(props.keys())}")
            
            # 总结
            print(f"\n📊 状态总结:")
            print(f"  Episodic节点: {total_episodic} 个")
            print(f"  Entity节点: {total_entities} 个") 
            print(f"  RELATES_TO关系: {total_relations} 个")
            
            if total_episodic > 0 and total_entities == 0:
                print("\n💡 分析: Episode创建成功，但实体提取过程遇到问题")
                print("   建议: 检查JSON验证错误的具体原因")
            elif total_entities > 0 and total_relations == 0:
                print("\n💡 分析: 实体创建成功，但关系建立失败")  
                print("   建议: 检查关系提取过程")
            elif total_entities > 0 and total_relations > 0:
                print("\n🎉 分析: 实体关系建立成功!")
            else:
                print("\n⚠️ 分析: 需要进一步调试")
        
        await driver.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_graphiti_status())