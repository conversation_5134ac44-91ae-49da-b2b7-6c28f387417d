#!/usr/bin/env python3
"""
测试优化后的Graphiti记忆管理系统
验证实体关系和对话管理功能
"""
import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv

async def test_optimized_memory_system():
    """测试优化后的记忆系统"""
    print("🧠 测试优化后的Graphiti记忆管理系统")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 初始化组件
        print("📋 步骤1: 初始化优化后的组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.memory_manager import GraphitiMemoryManager
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.1,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器 - 使用本地模型避免API问题
        embedding_manager = EmbeddingManager()
        local_client = EmbeddingClientFactory.create_client(
            provider="sentence_transformers",
            model="all-MiniLM-L6-v2"
        )
        await embedding_manager.add_client("local", local_client)
        
        # 记忆管理器
        memory_manager = GraphitiMemoryManager(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="00000000",
            llm_manager=llm_manager,
            embedding_client=embedding_manager.get_primary_client(),
            group_id="test_session"
        )
        
        await memory_manager.initialize()
        print("✅ 优化后的组件初始化完成")
        
        # 测试MCP工具功能
        print("\n📋 步骤2: 测试MCP工具功能...")
        
        # 清理图谱
        await memory_manager.clear_graph()
        print("  ✅ 图谱清理完成")
        
        # 添加记忆
        memory_uuid = await memory_manager.add_memory(
            memory_type="fact",
            content="苹果公司2023年Q4财报显示净利润率达到25%，ROE为30%",
            category="financial_data"
        )
        print(f"  ✅ 添加记忆: {memory_uuid}")
        
        # 等待处理
        await asyncio.sleep(3)
        
        # 搜索节点
        nodes = await memory_manager.search_nodes("苹果公司", max_results=5)
        print(f"  ✅ 搜索到 {len(nodes)} 个节点")
        
        # 搜索事实
        facts = await memory_manager.search_facts("财报 ROE", max_results=5)
        print(f"  ✅ 搜索到 {len(facts)} 个事实")
        
        # 测试对话管理功能
        print("\n📋 步骤3: 测试对话管理功能...")
        
        # 创建用户节点
        user_uuid = await memory_manager.create_user_node("张三")
        print(f"  ✅ 创建用户节点: {user_uuid}")
        
        # 添加对话
        await memory_manager.add_conversation_episode(
            user_name="张三",
            user_input="我想了解苹果公司的财务状况",
            assistant_response="苹果公司2023年Q4的财务表现非常出色，净利润率达到25%，ROE为30%，现金流充裕。"
        )
        print("  ✅ 添加对话记录")
        
        # 等待处理
        await asyncio.sleep(3)
        
        # 存储用户偏好
        await memory_manager.store_user_preference_advanced(
            user_name="张三",
            preference_type="investment_focus",
            preference_value="科技股和高ROE公司",
            category="investment"
        )
        print("  ✅ 存储用户偏好")
        
        # 等待处理
        await asyncio.sleep(3)
        
        # 搜索用户上下文
        context = await memory_manager.search_user_context(
            user_name="张三",
            query="苹果 财务 投资",
            num_results=5
        )
        print(f"  ✅ 搜索用户上下文: {len(context)} 条结果")
        for ctx in context[:2]:
            print(f"    - {ctx.get('fact', '')[:60]}...")
        
        # 构建对话上下文
        conversation_context = await memory_manager.build_conversation_context(
            user_name="张三",
            current_query="苹果公司投资建议"
        )
        print("  ✅ 构建对话上下文:")
        print(f"    {conversation_context[:150]}...")
        
        # 获取对话历史
        history = await memory_manager.get_user_conversation_history("张三", limit=5)
        print(f"  ✅ 获取对话历史: {len(history)} 条")
        
        # 分析用户模式
        patterns = await memory_manager.analyze_user_patterns("张三")
        print(f"  ✅ 用户模式分析:")
        print(f"    总交互数: {patterns.get('total_interactions', 0)}")
        print(f"    交互质量: {patterns.get('interaction_quality', 0.0):.2f}")
        
        # 测试系统状态
        print("\n📋 步骤4: 测试系统状态...")
        
        status = await memory_manager.get_status()
        print("  ✅ 系统状态:")
        for key, value in status.items():
            print(f"    {key}: {value}")
        
        # 获取Episodes
        episodes = await memory_manager.get_episodes(limit=5)
        print(f"  ✅ 获取Episodes: {len(episodes)} 条")
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        await memory_manager.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧠 优化后的Graphiti记忆管理系统测试")
    print("🎯 验证实体关系和对话管理功能")
    print("=" * 60)
    
    success = await test_optimized_memory_system()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    
    if success:
        print("🎉 优化后的记忆系统测试成功!")
        print("✅ MCP工具功能正常")
        print("✅ 对话管理功能正常")
        print("✅ 实体关系建立功能恢复")
        print("✅ 用户模式分析功能正常")
        print("\n🚀 系统已完全优化，可以投入使用！")
    else:
        print("❌ 测试失败")
        print("🔧 需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())