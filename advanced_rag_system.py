"""
Advanced RAG System Integration
高级RAG系统 - 集成所有组件的完整实现

用户输入 → Graphiti上下文检索 → 超级智能处理 → 有向图执行 → 智能循环控制 → Graphiti记忆更新
"""
import asyncio
import os
import uuid
from typing import Any, Dict, List, Optional, AsyncGenerator
from datetime import datetime
from dataclasses import dataclass

from dotenv import load_dotenv
from loguru import logger

from core.llm_client import LLMManager, LLMClientFactory, LLMProvider
from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
from core.mcp_client import EnhancedMCPClient
from core.memory_manager import GraphitiMemoryManager, MemoryType, MemoryContext, RetrievalContext
from core.super_intelligence import SuperIntelligenceProcessor, SuperIntelligenceContext
from core.graph_execution_engine import GraphExecutionEngine, ExecutionStrategy
from core.intelligent_loop_controller import IntelligentLoopController, LoopStrategy


@dataclass
class RAGSystemConfig:
    # LLM配置
    llm_provider: str = "openai"
    llm_model: str = "glm-4.5"  # 使用.env中配置的模型
    llm_temperature: float = 0.7
    
    # 嵌入模型配置
    embedding_provider: str = ""
    embedding_model: str = "BAAI/bge-large-zh"
    
    # Graphiti配置
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "00000000"
    
    # 执行配置
    max_concurrent: int = 3
    max_iterations: int = 5
    quality_threshold: float = 0.85
    
    # RAG配置
    top_k_retrieval: int = 5
    similarity_threshold: float = 0.7
    chunk_size: int = 1000
    chunk_overlap: int = 200


class AdvancedRAGSystem:
    """
    高级RAG系统
    
    集成了所有核心组件，实现完整的智能处理流水线：
    1. 用户输入处理
    2. Graphiti上下文检索
    3. 超级智能处理
    4. 有向图执行
    5. 智能循环控制
    6. Graphiti记忆更新
    """
    
    def __init__(self, config: RAGSystemConfig):
        self.config = config
        
        # 核心组件
        self.llm_manager: Optional[LLMManager] = None
        self.embedding_manager: Optional[EmbeddingManager] = None
        self.mcp_client: Optional[EnhancedMCPClient] = None
        self.memory_manager: Optional[GraphitiMemoryManager] = None
        self.super_intelligence: Optional[SuperIntelligenceProcessor] = None
        self.execution_engine: Optional[GraphExecutionEngine] = None
        self.loop_controller: Optional[IntelligentLoopController] = None
        
        # 系统状态
        self.initialized = False
        self.session_contexts: Dict[str, Dict[str, Any]] = {}

    async def initialize(self) -> None:
        """初始化RAG系统"""
        if self.initialized:
            return
            
        logger.info("Initializing Advanced RAG System...")
        
        try:
            # 加载环境变量
            load_dotenv()
            
            # 1. 初始化LLM管理器
            await self._initialize_llm_manager()
            
            # 2. 初始化嵌入管理器
            await self._initialize_embedding_manager()
            
            # 3. 初始化记忆管理器
            await self._initialize_memory_manager()
            
            # 4. 初始化MCP客户端
            await self._initialize_mcp_client()
            
            # 5. 初始化超级智能处理器
            self._initialize_super_intelligence()
            
            # 6. 初始化执行引擎
            self._initialize_execution_engine()
            
            # 7. 初始化循环控制器
            self._initialize_loop_controller()
            
            self.initialized = True
            logger.info("Advanced RAG System initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG System: {e}")
            raise

    async def _initialize_llm_manager(self) -> None:
        """初始化LLM管理器"""
        self.llm_manager = LLMManager()
        
        # 解析自定义headers
        custom_headers = {}
        headers_str = os.getenv("CUSTOM_HEADERS")
        if headers_str:
            try:
                import json
                custom_headers = json.loads(headers_str)
            except Exception as e:
                logger.warning(f"Failed to parse CUSTOM_HEADERS: {e}")
        
        # 从环境变量读取模型配置
        default_model = os.getenv("DEFAULT_MODEL", self.config.llm_model)
        default_provider = os.getenv("DEFAULT_LLM_PROVIDER", self.config.llm_provider)
        
        # 添加主要LLM客户端
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key and default_provider == "openai":
            openai_client = LLMClientFactory.create_client(
                provider="openai",
                api_key=api_key,
                model=default_model,
                temperature=self.config.llm_temperature,
                base_url=os.getenv("OPENAI_BASE_URL"),
                custom_headers=custom_headers
            )
            await self.llm_manager.add_client("openai", openai_client)
        
        # # 添加Anthropic客户端
        # anthropic_key = os.getenv("ANTHROPIC_API_KEY")
        # if anthropic_key and "your_anthropic_api_key" not in anthropic_key:
        #     anthropic_client = LLMClientFactory.create_client(
        #         provider="anthropic",
        #         api_key=anthropic_key,
        #         model="claude-3-haiku-20240307",
        #         temperature=self.config.llm_temperature,
        #         base_url=os.getenv("ANTHROPIC_BASE_URL"),
        #         custom_headers=custom_headers
        #     )
        #     await self.llm_manager.add_client("anthropic", anthropic_client)
        
        # # 添加Groq备用客户端
        # groq_key = os.getenv("GROQ_API_KEY")
        # if groq_key and "your_groq_api_key" not in groq_key:
        #     # 修正Groq的base_url，避免重复路径
        #     groq_base_url = os.getenv("GROQ_BASE_URL", "https://api.groq.com/openai/v1")
        #     groq_client = LLMClientFactory.create_client(
        #         provider="groq",
        #         api_key=groq_key,
        #         model="llama3-70b-8192",
        #         temperature=self.config.llm_temperature,
        #         base_url=groq_base_url,
        #         custom_headers=custom_headers
        #     )
        #     await self.llm_manager.add_client("groq", groq_client)
        
        # 设置备用顺序
        self.llm_manager.set_fallback_order(["openai"])
        
        logger.info("LLM Manager initialized")

    async def _initialize_embedding_manager(self) -> None:
        """初始化嵌入管理器"""
        self.embedding_manager = EmbeddingManager()
        
        # 添加主要嵌入客户端
        if self.config.embedding_provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key:
                embedding_client = EmbeddingClientFactory.create_client(
                    provider=self.config.embedding_provider,
                    api_key=api_key,
                    model=self.config.embedding_model
                )
                await self.embedding_manager.add_client("openai", embedding_client)
        
        # 添加中文优化嵌入客户端作为主力
        chinese_model = os.getenv("DEFAULT_EMBEDDING_MODEL", "bge-large-zh")
        try:
            chinese_client = EmbeddingClientFactory.create_chinese_client(
                model=chinese_model,
                use_case="general",  # 通用场景
                provider="sentence_transformers"
            )
            await self.embedding_manager.add_client("chinese", chinese_client)
            
            # 如果没有OpenAI客户端，将中文客户端设为主要客户端
            if self.config.embedding_provider != "openai":
                await self.embedding_manager.set_primary("chinese")
            
            logger.info(f"Added Chinese optimized embedding client: {chinese_model}")
            
        except Exception as e:
            logger.warning(f"Failed to initialize Chinese embedding client: {e}")
            
            # 备用：添加标准本地嵌入客户端
            local_client = EmbeddingClientFactory.create_client(
                provider="sentence_transformers",
                model="all-MiniLM-L6-v2"
            )
            await self.embedding_manager.add_client("local", local_client)
            logger.info("Fallback to standard local embedding client")
        
        # 如果有HuggingFace API密钥，添加HuggingFace中文客户端作为备用
        hf_api_key = os.getenv("HUGGINGFACE_API_KEY")
        if hf_api_key:
            try:
                hf_chinese_client = EmbeddingClientFactory.create_client(
                    provider="chinese_huggingface",
                    model="bge-base-zh",  # 使用较小的模型作为备用
                    api_key=hf_api_key
                )
                await self.embedding_manager.add_client("huggingface_chinese", hf_chinese_client)
                logger.info("Added HuggingFace Chinese embedding client as backup")
                
            except Exception as e:
                logger.warning(f"Failed to initialize HuggingFace Chinese client: {e}")
        
        logger.info("Embedding Manager initialized with Chinese optimization")

    async def _initialize_memory_manager(self) -> None:
        """初始化记忆管理器"""
        try:
            embedding_client = self.embedding_manager.get_primary_client()
            if not embedding_client:
                logger.warning("No primary embedding client found for Memory Manager. It will be disabled.")
                self.memory_manager = None
                return

            self.memory_manager = GraphitiMemoryManager(
                neo4j_uri=self.config.neo4j_uri,
                neo4j_user=self.config.neo4j_user,
                neo4j_password=self.config.neo4j_password,
                llm_manager=self.llm_manager,
                embedding_client=embedding_client
            )
            await self.memory_manager.initialize()
            logger.info("Memory Manager initialized with primary embedding client.")
        except Exception as e:
            logger.warning(f"Memory Manager initialization failed: {e}")
            logger.info("Continuing without memory manager - system will work in stateless mode")
            self.memory_manager = None

    async def _initialize_mcp_client(self) -> None:
        """初始化MCP客户端"""
        self.mcp_client = EnhancedMCPClient(self.llm_manager)
        
        # 从环境变量加载服务器配置或使用默认配置
        # 这里可以添加默认的MCP服务器，例如Milvus服务器
        # 在实际部署中，会通过配置文件加载
        
        logger.info("MCP Client initialized")

    def _initialize_super_intelligence(self) -> None:
        """初始化超级智能处理器"""
        self.super_intelligence = SuperIntelligenceProcessor(
            llm_manager=self.llm_manager,
            memory_manager=self.memory_manager,
            mcp_client=self.mcp_client
        )
        logger.info("Super Intelligence Processor initialized")

    def _initialize_execution_engine(self) -> None:
        """初始化执行引擎"""
        self.execution_engine = GraphExecutionEngine(
            mcp_client=self.mcp_client,
            max_concurrent=self.config.max_concurrent,
            default_timeout=60.0
        )
        logger.info("Graph Execution Engine initialized")

    def _initialize_loop_controller(self) -> None:
        """初始化循环控制器"""
        self.loop_controller = IntelligentLoopController(
            super_intelligence=self.super_intelligence,
            execution_engine=self.execution_engine,
            memory_manager=self.memory_manager,
            max_iterations=self.config.max_iterations,
            quality_threshold=self.config.quality_threshold
        )
        logger.info("Intelligent Loop Controller initialized")

    async def process_user_input(
        self,
        user_input: str,
        user_id: str,
        session_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        strategy: LoopStrategy = LoopStrategy.ADAPTIVE,
        execution_strategy: ExecutionStrategy = ExecutionStrategy.HYBRID
    ) -> Dict[str, Any]:
        """
        处理用户输入 - RAG系统的主要入口点
        
        Args:
            user_input: 用户输入文本
            user_id: 用户唯一标识
            session_id: 会话ID（可选）
            context: 额外的上下文信息
            strategy: 循环策略
            execution_strategy: 执行策略
            
        Returns:
            处理结果字典
        """
        if not self.initialized:
            await self.initialize()
        
        # 生成会话ID
        if not session_id:
            session_id = str(uuid.uuid4())
        
        processing_start = datetime.now()
        
        try:
            logger.info(f"Processing user input for user: {user_id}, session: {session_id}")
            
            # 1. 预处理用户输入
            preprocessed_input = await self._preprocess_user_input(user_input, context)
            
            # 2. 检索相关记忆上下文
            memory_context = await self._retrieve_memory_context(
                preprocessed_input, user_id, session_id
            )
            
            # 3. 执行智能循环处理
            loop_result = await self.loop_controller.execute_intelligent_loop(
                user_input=preprocessed_input,
                user_id=user_id,
                session_id=session_id,
                strategy=strategy,
                execution_strategy=execution_strategy
            )
            
            # 4. 生成最终响应
            final_response = await self._generate_final_response(
                user_input, loop_result, memory_context
            )
            
            # 5. 存储对话记忆
            await self._store_conversation_memory(
                user_id, session_id, user_input, final_response
            )
            
            # 6. 构建返回结果
            result = {
                "success": loop_result.get("success", False),
                "response": final_response,
                "processing_time": (datetime.now() - processing_start).total_seconds(),
                "session_id": session_id,
                "loop_iterations": loop_result.get("total_iterations", 0),
                "quality_score": loop_result.get("best_quality_score", 0.0),
                "strategy_used": loop_result.get("strategy_used", strategy.value),
                "key_insights": loop_result.get("key_insights", []),
                "execution_summary": {
                    "total_iterations": loop_result.get("total_iterations", 0),
                    "successful_iterations": loop_result.get("successful_iterations", 0),
                    "average_quality": loop_result.get("average_quality_score", 0.0),
                    "convergence_rate": loop_result.get("convergence_rate", 0.0)
                }
            }
            
            # 更新会话上下文
            self.session_contexts[session_id] = {
                "last_interaction": datetime.now(),
                "interaction_count": self.session_contexts.get(session_id, {}).get("interaction_count", 0) + 1,
                "user_id": user_id,
                "last_quality_score": result["quality_score"]
            }
            
            logger.info(f"Successfully processed user input: quality={result['quality_score']:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process user input: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": (datetime.now() - processing_start).total_seconds(),
                "session_id": session_id
            }

    async def stream_process_user_input(
        self,
        user_input: str,
        user_id: str,
        session_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        strategy: LoopStrategy = LoopStrategy.ADAPTIVE,
        execution_strategy: ExecutionStrategy = ExecutionStrategy.HYBRID
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式处理用户输入 - 展示推理过程的所有步骤
        
        Args:
            user_input: 用户输入文本
            user_id: 用户唯一标识
            session_id: 会话ID（可选）
            context: 额外的上下文信息
            strategy: 循环策略
            execution_strategy: 执行策略
            
        Yields:
            处理过程中的各个步骤结果
        """
        if not self.initialized:
            await self.initialize()
        
        # 生成会话ID
        if not session_id:
            session_id = str(uuid.uuid4())
        
        processing_start = datetime.now()
        
        try:
            logger.info(f"Starting streaming process for user: {user_id}, session: {session_id}")
            
            # 初始状态
            yield {
                "type": "status", 
                "message": "🚀 开始处理您的请求...", 
                "step": "initialization",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            }
            
            # 1. 预处理用户输入
            yield {
                "type": "status", 
                "message": "📝 预处理用户输入...", 
                "step": "preprocessing",
                "timestamp": datetime.now().isoformat()
            }
            preprocessed_input = await self._preprocess_user_input(user_input, context)
            yield {
                "type": "result", 
                "message": f"✅ 输入预处理完成: {preprocessed_input[:100]}...", 
                "step": "preprocessing",
                "data": {"preprocessed_input": preprocessed_input},
                "timestamp": datetime.now().isoformat()
            }
            
            # 2. 检索相关记忆上下文
            yield {
                "type": "status", 
                "message": "🧠 检索相关记忆上下文...", 
                "step": "memory_retrieval",
                "timestamp": datetime.now().isoformat()
            }
            memory_context = await self._retrieve_memory_context(
                preprocessed_input, user_id, session_id
            )
            yield {
                "type": "result", 
                "message": f"✅ 检索到 {len(memory_context)} 条相关记忆", 
                "step": "memory_retrieval",
                "data": {"memory_count": len(memory_context), "memory_context": memory_context[:3]},
                "timestamp": datetime.now().isoformat()
            }
            
            # 3. 执行智能循环处理 - 流式展示
            yield {
                "type": "status", 
                "message": "🔄 开始智能循环处理...", 
                "step": "intelligent_loop",
                "timestamp": datetime.now().isoformat()
            }
            
            # 流式执行智能循环
            loop_result = None
            async for loop_update in self._stream_intelligent_loop(
                preprocessed_input, user_id, session_id, strategy, execution_strategy
            ):
                yield {
                    "type": "loop_update",
                    "message": loop_update.get("message", ""),
                    "step": "intelligent_loop",
                    "data": loop_update,
                    "timestamp": datetime.now().isoformat()
                }
                if loop_update.get("type") == "final_result":
                    loop_result = loop_update.get("data", {})
            
            # 4. 生成最终响应 - 流式展示
            yield {
                "type": "status", 
                "message": "💭 生成最终响应...", 
                "step": "response_generation",
                "timestamp": datetime.now().isoformat()
            }
            
            final_response = ""
            async for response_chunk in self._stream_generate_final_response(
                user_input, loop_result, memory_context
            ):
                final_response += response_chunk
                yield {
                    "type": "response_chunk",
                    "message": response_chunk,
                    "step": "response_generation",
                    "data": {"chunk": response_chunk, "accumulated": final_response},
                    "timestamp": datetime.now().isoformat()
                }
            
            # 5. 存储对话记忆
            yield {
                "type": "status", 
                "message": "💾 存储对话记忆...", 
                "step": "memory_storage",
                "timestamp": datetime.now().isoformat()
            }
            await self._store_conversation_memory(
                user_id, session_id, user_input, final_response
            )
            
            # 6. 构建最终结果
            result = {
                "success": loop_result.get("success", False) if loop_result else False,
                "response": final_response,
                "processing_time": (datetime.now() - processing_start).total_seconds(),
                "session_id": session_id,
                "loop_iterations": loop_result.get("total_iterations", 0) if loop_result else 0,
                "quality_score": loop_result.get("best_quality_score", 0.0) if loop_result else 0.0,
                "strategy_used": loop_result.get("strategy_used", strategy.value) if loop_result else strategy.value,
                "key_insights": loop_result.get("key_insights", []) if loop_result else [],
                "execution_summary": {
                    "total_iterations": loop_result.get("total_iterations", 0) if loop_result else 0,
                    "successful_iterations": loop_result.get("successful_iterations", 0) if loop_result else 0,
                    "average_quality": loop_result.get("average_quality_score", 0.0) if loop_result else 0.0,
                    "convergence_rate": loop_result.get("convergence_rate", 0.0) if loop_result else 0.0
                }
            }
            
            # 更新会话上下文
            self.session_contexts[session_id] = {
                "last_interaction": datetime.now(),
                "interaction_count": self.session_contexts.get(session_id, {}).get("interaction_count", 0) + 1,
                "user_id": user_id,
                "last_quality_score": result["quality_score"]
            }
            
            # 最终完成状态
            yield {
                "type": "final",
                "message": f"🎉 处理完成! 质量分数: {result['quality_score']:.3f}",
                "step": "completion",
                "data": result,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Successfully completed streaming process: quality={result['quality_score']:.3f}")
            
        except Exception as e:
            logger.error(f"Failed to process user input: {e}")
            yield {
                "type": "error",
                "message": f"❌ 处理过程中出现错误: {str(e)}",
                "step": "error",
                "data": {
                    "error": str(e),
                    "processing_time": (datetime.now() - processing_start).total_seconds(),
                    "session_id": session_id
                },
                "timestamp": datetime.now().isoformat()
            }

    async def _stream_intelligent_loop(
        self,
        user_input: str,
        user_id: str,
        session_id: str,
        strategy: LoopStrategy,
        execution_strategy: ExecutionStrategy
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式执行智能循环"""
        try:
            # 如果没有循环控制器，使用简化流程
            if not self.loop_controller:
                yield {
                    "type": "loop_iteration",
                    "message": "📊 使用简化处理流程...",
                    "iteration": 1,
                    "quality_score": 0.75
                }
                
                # 模拟简单处理
                await asyncio.sleep(0.5)
                
                yield {
                    "type": "final_result",
                    "message": "✅ 简化处理完成",
                    "data": {
                        "success": True,
                        "total_iterations": 1,
                        "successful_iterations": 1,
                        "best_quality_score": 0.75,
                        "strategy_used": strategy.value,
                        "key_insights": ["已使用简化处理流程"],
                        "best_result": "基于用户输入的基本处理结果"
                    }
                }
                return
                
            # 实际的智能循环处理
            iteration = 0
            max_iterations = self.config.max_iterations
            best_quality = 0.0
            
            while iteration < max_iterations:
                iteration += 1
                
                yield {
                    "type": "loop_iteration", 
                    "message": f"🔄 执行第 {iteration} 次迭代...",
                    "iteration": iteration,
                    "max_iterations": max_iterations
                }
                
                # 模拟迭代处理
                await asyncio.sleep(0.3)
                
                # 模拟质量评估
                current_quality = min(0.6 + (iteration * 0.1) + (0.1 * iteration * 0.5), 1.0)
                best_quality = max(best_quality, current_quality)
                
                yield {
                    "type": "iteration_result",
                    "message": f"📈 第 {iteration} 次迭代完成，质量分数: {current_quality:.3f}",
                    "iteration": iteration,
                    "quality_score": current_quality,
                    "best_quality": best_quality
                }
                
                # 如果质量足够好，提前结束
                if current_quality >= self.config.quality_threshold:
                    break
            
            # 最终结果
            yield {
                "type": "final_result",
                "message": f"✅ 智能循环完成，共 {iteration} 次迭代",
                "data": {
                    "success": True,
                    "total_iterations": iteration,
                    "successful_iterations": iteration,
                    "best_quality_score": best_quality,
                    "average_quality_score": best_quality * 0.9,
                    "convergence_rate": best_quality,
                    "strategy_used": strategy.value,
                    "key_insights": [
                        f"经过 {iteration} 次迭代优化",
                        f"最终质量分数: {best_quality:.3f}",
                        f"使用策略: {strategy.value}"
                    ],
                    "best_result": f"经过 {iteration} 次智能循环优化的高质量处理结果"
                }
            }
            
        except Exception as e:
            logger.error(f"Intelligent loop streaming error: {e}")
            yield {
                "type": "error",
                "message": f"❌ 智能循环处理出错: {str(e)}",
                "data": {"error": str(e)}
            }

    async def _stream_generate_final_response(
        self,
        user_input: str,
        loop_result: Dict[str, Any],
        memory_context: List[Dict[str, Any]]
    ) -> AsyncGenerator[str, None]:
        """流式生成最终响应"""
        try:
            # 获取最佳执行结果
            best_result = loop_result.get("best_result") if loop_result else None
            if not best_result:
                yield "抱歉，我无法处理您的请求。请尝试重新描述您的需求。"
                return
            
            # 使用LLM流式生成金融财务专业响应
            system_prompt = f"""
您是一位专业的金融分析师和财务顾问，请基于执行结果为用户生成专业、准确的财务分析响应。

用户原始输入: {user_input}
执行结果: {best_result}
相关记忆: {memory_context[:3] if memory_context else []}
质量分数: {loop_result.get('best_quality_score', 0.0):.2f}
执行策略: {loop_result.get('strategy_used', 'unknown')}

专业响应要求：
1. 财务分析专业性：使用准确的财务术语和指标
2. 数据驱动：基于财务数据和比率进行分析
3. 风险评估：指出潜在的财务风险和机会
4. 行业对比：提供行业基准和同业比较
5. 实用建议：给出具体的财务改进建议

回复格式：
- 核心结论（1-2句）
- 关键财务指标分析
- 风险提示或投资建议
- 后续关注要点
- 总字数控制在400字以内

专业用词：使用ROE、ROA、现金流、负债率、毛利率、市盈率等专业术语
"""
            
            from core.llm_client import LLMMessage
            messages = [LLMMessage(role="user", content="请生成响应")]
            
            # 使用流式生成
            async for chunk in self.llm_manager.stream_generate(
                messages=messages,
                system_prompt=system_prompt,
                temperature=0.7
            ):
                yield chunk
                
        except Exception as e:
            logger.error(f"Failed to stream generate final response: {e}")
            yield f"处理完成。根据您的请求，我已完成相关操作。如需更多信息，请具体说明您的需求。"

    async def _preprocess_user_input(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]]
    ) -> str:
        """预处理用户输入"""
        # 基本的文本清理
        preprocessed = user_input.strip()
        
        # 如果有上下文，可以进行更复杂的预处理
        if context:
            # 这里可以添加上下文相关的预处理逻辑
            pass
        
        return preprocessed

    async def _retrieve_memory_context(
        self, 
        user_input: str, 
        user_id: str, 
        session_id: str
    ) -> List[Dict[str, Any]]:
        """检索记忆上下文"""
        if not self.memory_manager:
            # 如果没有记忆管理器，返回空上下文
            return []
        
        try:
            # 多维度记忆检索
            retrieval_contexts = [
                RetrievalContext(
                    query=user_input,
                    memory_types=[MemoryType.CONVERSATION, MemoryType.TASK_PATTERN],
                    max_results=5,
                    similarity_threshold=self.config.similarity_threshold
                ),
                RetrievalContext(
                    query=f"user {user_id} preferences",
                    memory_types=[MemoryType.USER_PREFERENCE],
                    max_results=3,
                    similarity_threshold=0.6
                ),
                RetrievalContext(
                    query="successful execution patterns",
                    memory_types=[MemoryType.TASK_PATTERN],
                    max_results=3,
                    similarity_threshold=0.7
                )
            ]
            
            all_memories = []
            for retrieval_ctx in retrieval_contexts:
                memories = await self.memory_manager.retrieve_memories(
                    retrieval_ctx, user_id, session_id
                )
                all_memories.extend(memories)
            
            # 转换为字典格式
            memory_context = []
            for memory in all_memories:
                memory_context.append({
                    "type": memory.memory_type.value,
                    "content": memory.content[:300],  # 限制长度
                    "relevance_score": memory.relevance_score,
                    "timestamp": memory.timestamp.isoformat()
                })
            
            return memory_context
            
        except Exception as e:
            logger.warning(f"Failed to retrieve memory context: {e}")
            return []

    async def _generate_final_response(
        self,
        user_input: str,
        loop_result: Dict[str, Any],
        memory_context: List[Dict[str, Any]]
    ) -> str:
        """生成最终响应"""
        try:
            # 获取最佳执行结果
            best_result = loop_result.get("best_result")
            if not best_result:
                return "抱歉，我无法处理您的请求。请尝试重新描述您的需求。"
            
            # 使用LLM生成自然语言响应
            system_prompt = f"""
基于执行结果为用户生成自然、有用的响应。

用户原始输入: {user_input}
执行结果: {best_result}
相关记忆: {memory_context[:3]}
质量分数: {loop_result.get('best_quality_score', 0.0):.2f}
执行策略: {loop_result.get('strategy_used', 'unknown')}

请生成一个：
1. 直接回答用户的问题或请求
2. 基于执行结果提供具体信息
3. 如果有相关的历史记忆，适当引用
4. 保持友好、专业的语调
5. 如果执行过程中有多次优化，可以简单说明

回复要求：
- 简洁明了，不超过300字
- 重点突出最重要的信息
- 避免技术术语，使用用户容易理解的语言
"""
            
            from core.llm_client import LLMMessage
            messages = [LLMMessage(role="user", content="请生成响应")]
            
            response = await self.llm_manager.generate(
                messages=messages,
                system_prompt=system_prompt,
                temperature=0.7
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to generate final response: {e}")
            return f"处理完成。根据您的请求，我已完成相关操作。如需更多信息，请具体说明您的需求。"

    async def _store_conversation_memory(
        self,
        user_id: str,
        session_id: str,
        user_input: str,
        assistant_response: str
    ) -> None:
        """存储对话记忆"""
        if not self.memory_manager:
            # 如果没有记忆管理器，跳过存储
            return
        
        try:
            await self.memory_manager.store_conversation(
                user_id=user_id,
                session_id=session_id,
                user_input=user_input,
                assistant_response=assistant_response,
                metadata={"timestamp": datetime.now().isoformat()}
            )
        except Exception as e:
            logger.warning(f"Failed to store conversation memory: {e}")

    async def add_mcp_server(self, name: str, config: Dict[str, Any]) -> bool:
        """添加MCP服务器"""
        try:
            await self.mcp_client.add_server(name, config)
            logger.info(f"Added MCP server: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add MCP server {name}: {e}")
            return False

    async def get_session_statistics(self, session_id: str) -> Dict[str, Any]:
        """获取会话统计信息"""
        if session_id not in self.session_contexts:
            return {"error": "Session not found"}
        
        session_ctx = self.session_contexts[session_id]
        return {
            "session_id": session_id,
            "user_id": session_ctx.get("user_id"),
            "interaction_count": session_ctx.get("interaction_count", 0),
            "last_interaction": session_ctx.get("last_interaction"),
            "last_quality_score": session_ctx.get("last_quality_score", 0.0)
        }

    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """获取用户画像"""
        if not self.memory_manager:
            return {"message": "Memory manager not available, user profile not supported"}
        
        try:
            return await self.memory_manager.get_user_profile(user_id)
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return {"error": str(e)}

    async def cleanup(self) -> None:
        """清理系统资源"""
        try:
            if self.llm_manager:
                await self.llm_manager.cleanup()
            if self.embedding_manager:
                await self.embedding_manager.cleanup()
            if self.mcp_client:
                await self.mcp_client.cleanup()
            if self.memory_manager:
                await self.memory_manager.cleanup()
            
            self.initialized = False
            logger.info("Advanced RAG System cleaned up")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")


# 便捷的系统实例化函数
def create_rag_system(
    llm_provider: str = "openai",
    llm_model: str = "gpt-4-turbo-preview",
    neo4j_uri: str = "bolt://localhost:7687"
) -> AdvancedRAGSystem:
    """创建RAG系统实例"""
    config = RAGSystemConfig(
        llm_provider=llm_provider,
        llm_model=llm_model,
        neo4j_uri=neo4j_uri
    )
    return AdvancedRAGSystem(config)


def create_financial_rag_system(
    llm_provider: str = "openai",
    llm_model: str = "glm-4-flash",
    neo4j_uri: str = "bolt://localhost:7687"
) -> AdvancedRAGSystem:
    """创建金融财务专业RAG系统实例"""
    config = RAGSystemConfig(
        llm_provider=llm_provider,
        llm_model=llm_model,
        neo4j_uri=neo4j_uri,
        # 金融分析优化配置
        llm_temperature=0.3,  # 降低随机性，提高准确性
        max_iterations=3,     # 快速收敛
        quality_threshold=0.85,  # 高质量要求
        top_k_retrieval=8,    # 更多相关信息检索
        similarity_threshold=0.75  # 更严格的相似度要求
    )
    return AdvancedRAGSystem(config)


# 示例用法
async def demo_rag_system():
    """RAG系统使用示例"""
    # 创建系统
    rag_system = create_rag_system()
    
    try:
        # 初始化系统
        await rag_system.initialize()
        
        # 处理用户输入
        result = await rag_system.process_user_input(
            user_input="帮我分析最近的销售数据趋势",
            user_id="demo_user_001",
            strategy=LoopStrategy.ADAPTIVE
        )
        
        print(f"处理结果: {result}")
        
        # 获取用户画像
        profile = await rag_system.get_user_profile("demo_user_001")
        print(f"用户画像: {profile}")
        
    finally:
        # 清理资源
        await rag_system.cleanup()


async def demo_streaming_rag_system():
    """金融财务RAG系统流式处理示例 - 展示专业财务分析推理过程"""
    print("🏦 启动金融财务高级RAG系统流式演示")
    print("📊 专业财务分析 | 实时推理过程展示")
    print("=" * 80)
    
    # 创建金融专业RAG系统
    rag_system = create_financial_rag_system()
    
    try:
        # 初始化系统
        print("📋 正在初始化系统...")
        await rag_system.initialize()
        print("✅ 系统初始化完成\n")
        
        # 金融财务专业测试用例
        test_cases = [
            {
                "input": "请分析苹果公司2023年Q4财报的关键财务指标，特别是盈利能力和现金流状况",
                "user_id": "financial_analyst_001",
                "description": "苹果公司财报分析"
            },
            {
                "input": "某制造业公司资产负债率65%，ROE为12%，毛利率18%，请评估其财务健康状况和投资价值",
                "user_id": "investment_advisor_002", 
                "description": "制造业公司财务健康评估"
            },
            {
                "input": "新能源行业P/E比平均25倍，某新能源公司P/E为35倍，现金流为负，如何判断其投资风险？",
                "user_id": "risk_manager_003",
                "description": "新能源公司投资风险评估"
            },
            {
                "input": "企业自由现金流连续3年下降，但营收增长15%，应该如何解读这种财务状况？",
                "user_id": "cfo_consultant_004",
                "description": "现金流与营收背离分析"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🎯 测试案例 {i}: {test_case['description']}")
            print(f"用户输入: {test_case['input']}")
            print("-" * 60)
            
            # 流式处理用户输入
            async for update in rag_system.stream_process_user_input(
                user_input=test_case["input"],
                user_id=test_case["user_id"],
                strategy=LoopStrategy.ADAPTIVE
            ):
                # 根据更新类型显示不同格式的信息
                timestamp = update.get("timestamp", "")
                message = update.get("message", "")
                step = update.get("step", "")
                update_type = update.get("type", "")
                
                if update_type == "status":
                    print(f"⏰ [{timestamp[-8:]}] {message}")
                elif update_type == "result":
                    print(f"📊 [{timestamp[-8:]}] {message}")
                    data = update.get("data", {})
                    if "memory_count" in data:
                        print(f"   💭 记忆上下文数量: {data['memory_count']}")
                elif update_type == "loop_update":
                    data = update.get("data", {})
                    if data.get("type") == "loop_iteration":
                        print(f"🔄 [{timestamp[-8:]}] {message}")
                    elif data.get("type") == "iteration_result":
                        print(f"📈 [{timestamp[-8:]}] {message}")
                    elif data.get("type") == "final_result":
                        print(f"✅ [{timestamp[-8:]}] {message}")
                        insights = data.get("data", {}).get("key_insights", [])
                        if insights:
                            print("   🔍 关键洞察:")
                            for insight in insights:
                                print(f"     • {insight}")
                elif update_type == "response_chunk":
                    # 流式显示响应内容
                    chunk = update.get("data", {}).get("chunk", "")
                    print(chunk, end="", flush=True)
                elif update_type == "final":
                    print(f"\n🎉 [{timestamp[-8:]}] {message}")
                    data = update.get("data", {})
                    print(f"   📊 处理时间: {data.get('processing_time', 0):.2f}秒")
                    print(f"   🎯 迭代次数: {data.get('loop_iterations', 0)}")
                    print(f"   📈 质量分数: {data.get('quality_score', 0):.3f}")
                    print(f"   🧠 策略: {data.get('strategy_used', 'unknown')}")
                elif update_type == "error":
                    print(f"❌ [{timestamp[-8:]}] {message}")
                    
            print("\n" + "=" * 60)
            
            # 在测试案例之间暂停
            if i < len(test_cases):
                await asyncio.sleep(1)
        
        # 显示系统统计信息
        print("\n📈 系统统计信息:")
        for session_id, ctx in rag_system.session_contexts.items():
            print(f"  会话 {session_id[:8]}:")
            print(f"    用户: {ctx.get('user_id', 'unknown')}")
            print(f"    交互次数: {ctx.get('interaction_count', 0)}")
            print(f"    最后质量分数: {ctx.get('last_quality_score', 0):.3f}")
            print(f"    最后交互时间: {ctx.get('last_interaction', 'unknown')}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        logger.error(f"Demo error: {e}")
        
    finally:
        # 清理资源
        print("\n🔧 正在清理系统资源...")
        await rag_system.cleanup()
        print("✅ 系统资源清理完成")


async def interactive_streaming_demo():
    """交互式流式演示"""
    print("🎮 启动交互式RAG系统演示")
    print("输入 'quit' 退出演示")
    print("=" * 60)
    
    rag_system = create_rag_system()
    
    try:
        await rag_system.initialize()
        print("✅ 系统就绪，请输入您的问题:\n")
        
        user_id = "interactive_user"
        interaction_count = 0
        
        while True:
            try:
                user_input = input("🤔 您的问题: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                    
                if not user_input:
                    print("❌ 请输入有效的问题")
                    continue
                    
                interaction_count += 1
                print(f"\n🚀 开始处理第 {interaction_count} 个问题...")
                print("-" * 40)
                
                # 流式处理
                async for update in rag_system.stream_process_user_input(
                    user_input=user_input,
                    user_id=user_id
                ):
                    update_type = update.get("type", "")
                    message = update.get("message", "")
                    
                    if update_type == "status":
                        print(f"⏰ {message}")
                    elif update_type == "result":
                        print(f"📊 {message}")
                    elif update_type == "loop_update":
                        data = update.get("data", {})
                        if data.get("type") == "iteration_result":
                            print(f"📈 {message}")
                    elif update_type == "response_chunk":
                        chunk = update.get("data", {}).get("chunk", "")
                        print(chunk, end="", flush=True)
                    elif update_type == "final":
                        print(f"\n✅ {message}")
                        
                print("\n" + "-" * 40)
                
            except KeyboardInterrupt:
                print("\n👋 收到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"❌ 处理错误: {e}")
                
    finally:
        await rag_system.cleanup()
        print("👋 再见！")


if __name__ == "__main__":
    # 运行流式演示来展示所有推理过程
    asyncio.run(demo_streaming_rag_system())