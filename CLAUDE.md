# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a RAG (Retrieval-Augmented Generation) system implementation that integrates Graphiti time-aware knowledge graphs with MCP (Model Context Protocol) clients. The repository demonstrates an advanced chatbot architecture using:

- **MCP Client Framework**: For tool discovery and execution
- **Graphiti Integration**: Time-aware knowledge graph for memory management
- **OpenAI/Groq LLM Integration**: For intelligent processing
- **Multi-modal Transport Support**: STDIO, HTTP, and SSE transports

## Core Architecture

### Main Components

1. **simple-chatbot.py**: The primary MCP client implementation
   - Multi-transport MCP client (STDIO, HTTP, SSE)
   - LLM integration with Groq API
   - Interactive command-line interface
   - Tool execution with retry mechanisms

2. **Documentation Files**:
   - `prd.md`: System requirements and architecture design
   - `graphiti.md`: Graphiti integration implementation guide
   - `记忆.md`: Memory management code snippets and examples
   - `mcp.txt`: MCP SDK usage examples and patterns

### Key Design Patterns

- **Server Management**: Async server lifecycle with proper cleanup
- **Tool Orchestration**: Dynamic tool discovery and execution
- **Memory Integration**: Graphiti-based temporal knowledge graphs
- **Transport Abstraction**: Unified interface for multiple transport types

## Development Commands

### Environment Setup
```bash
# Install dependencies from documentation
pip install openai graphiti-core asyncio python-dotenv

# For MCP SDK
pip install mcp

# For Graphiti with specific providers
pip install graphiti-core[openai]
pip install graphiti-core[anthropic]
pip install graphiti-core[groq]
```

### Running the Application
```bash
# Run the main chatbot
python simple-chatbot.py

# With custom configuration
LLM_API_KEY=your_key python simple-chatbot.py
```

### Configuration Files
- **servers_config.json**: MCP server configuration (if exists)
- **.env**: Environment variables for API keys and settings

## Required Environment Variables

```bash
# LLM Provider
LLM_API_KEY=your_api_key_here

# Graphiti/Neo4j (if using knowledge graphs)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Optional: Control concurrency
SEMAPHORE_LIMIT=10
```

## Implementation Patterns

### MCP Client Architecture
The codebase follows a multi-layered MCP client pattern:

1. **Configuration Layer**: Environment and JSON-based configuration
2. **Server Layer**: MCP server connection management with retry logic
3. **Tool Layer**: Tool discovery, validation, and execution
4. **Session Layer**: Interactive command-line interface
5. **LLM Layer**: Integration with language model providers

### Key Classes
- `Configuration`: Manages environment variables and server configs
- `Server`: Handles MCP server connections (STDIO/HTTP/SSE)
- `Tool`: Represents individual MCP tools with metadata
- `LLMClient`: Manages LLM API communication
- `ChatSession`: Orchestrates user interactions and tool execution

### Error Handling
- Comprehensive retry mechanisms for tool execution
- Graceful degradation when servers are unavailable
- Proper async resource cleanup with context managers

## Integration Approaches

### Graphiti Memory Management
Based on the documentation, the system can integrate Graphiti for:
- **Episode Management**: Store conversations and interactions
- **Entity Tracking**: Maintain user preferences and context
- **Temporal Queries**: Time-aware knowledge retrieval
- **Relationship Mapping**: Complex entity relationships

### Planned Enhancements (from PRD)
The repository shows intent to implement:
- Advanced RAG with Milvus vector database
- Directed graph execution engines
- Smart loop optimization
- Context-aware tool selection

## Testing and Quality

### Code Quality
- Type hints are used throughout the codebase
- Async/await patterns for all I/O operations
- Proper exception handling and logging
- Resource management with context managers

### Debugging
- Comprehensive logging with different levels
- Progress tracking for long-running operations
- Interactive mode for testing tool execution

## Usage Patterns

### Basic MCP Client Usage
```python
# Server initialization
server = Server(name, config)
await server.initialize()

# Tool execution
result = await server.execute_tool(tool_name, arguments)

# Cleanup
await server.cleanup()
```

### Interactive Commands
- `list`: Show available tools
- `call <tool> [args]`: Execute a tool
- `chat`: Enter conversational mode
- `quit`: Exit the application

## Dependencies and Compatibility

### Core Dependencies
- `asyncio`: Async programming foundation
- `httpx`: HTTP client for API calls
- `mcp`: Model Context Protocol SDK
- `python-dotenv`: Environment variable management

### Optional Dependencies
- `graphiti-core`: Knowledge graph integration
- `openai`: OpenAI API client
- Various provider-specific packages for LLM integration

The codebase is designed to be modular, allowing selective feature enablement based on available dependencies and configuration.