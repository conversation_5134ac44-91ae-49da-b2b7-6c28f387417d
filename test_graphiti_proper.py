#!/usr/bin/env python3
"""
正确的Graphiti测试 - 完全使用我们的自定义LLM配置
"""
import asyncio
import os
from datetime import datetime, timezone
from dotenv import load_dotenv

async def proper_graphiti_test():
    """正确配置的Graphiti测试"""
    print("🔧 正确配置的Graphiti测试")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 步骤1: 创建我们的LLM组件
        print("📋 步骤1: 创建自定义LLM组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.graphiti_llm import GraphitiLLM
        from core.graphiti_embedder import GraphitiEmbedder
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 未找到API密钥")
            return False
            
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.3,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器
        embedding_manager = EmbeddingManager()
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general"
        )
        await embedding_manager.add_client("chinese", chinese_client)
        
        print("✅ LLM组件创建完成")
        
        # 步骤2: 创建Graphiti包装器
        print("\n📋 步骤2: 创建Graphiti包装器...")
        graphiti_llm = GraphitiLLM(llm_manager)
        primary_embedder = embedding_manager.get_primary_client()
        graphiti_embedder = GraphitiEmbedder(primary_embedder)
        
        print("✅ Graphiti包装器创建完成")
        
        # 步骤3: 测试LLM包装器单独功能
        print("\n📋 步骤3: 测试LLM包装器...")
        try:
            test_messages = ["这是一个简单的测试消息"]
            llm_response = await graphiti_llm._generate_response(test_messages)
            print(f"  响应类型: {type(llm_response)}")
            if isinstance(llm_response, dict) and 'content' in llm_response:
                print(f"  响应内容: {llm_response['content'][:100]}...")
                print("  ✅ LLM包装器正常工作")
            else:
                print(f"  ❌ LLM响应格式异常: {llm_response}")
                return False
        except Exception as e:
            print(f"  ❌ LLM包装器测试失败: {e}")
            return False
        
        # 步骤4: 测试嵌入包装器
        print("\n📋 步骤4: 测试嵌入包装器...")
        try:
            test_text = "这是嵌入测试文本"
            embedding_result = await graphiti_embedder.embed(test_text)
            print(f"  嵌入维度: {len(embedding_result) if embedding_result else 'N/A'}")
            if embedding_result and len(embedding_result) > 0:
                print("  ✅ 嵌入包装器正常工作")
            else:
                print("  ❌ 嵌入包装器异常")
                return False
        except Exception as e:
            print(f"  ❌ 嵌入包装器测试失败: {e}")
            return False
        
        # 步骤5: 初始化Graphiti并传入我们的自定义客户端
        print("\n📋 步骤5: 正确初始化Graphiti...")
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # 关键：传入我们的自定义LLM和嵌入客户端，这样Graphiti就不会使用默认的OpenAI客户端
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j", 
            password="password",
            llm_client=graphiti_llm,        # 使用我们的包装器
            embedder=graphiti_embedder,    # 使用我们的嵌入器
            store_raw_episode_content=True
        )
        
        await client.build_indices_and_constraints()
        print("✅ Graphiti初始化成功，使用自定义LLM和嵌入器")
        
        # 步骤6: 测试Episode添加
        print("\n📋 步骤6: 测试Episode添加...")
        try:
            await client.add_episode(
                name="自定义LLM测试Episode",
                episode_body="这是使用自定义LLM和嵌入模型的Graphiti测试",
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="自定义LLM测试"
            )
            print("✅ Episode添加成功！")
        except Exception as episode_error:
            print(f"❌ Episode添加失败: {episode_error}")
            print(f"错误类型: {type(episode_error)}")
            import traceback
            traceback.print_exc()
            return False
        
        # 步骤7: 等待处理
        print("\n📋 步骤7: 等待Graphiti处理...")
        await asyncio.sleep(5)
        
        # 步骤8: 测试搜索
        print("\n📋 步骤8: 测试搜索功能...")
        try:
            from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            search_result = await client._search(
                "自定义LLM",
                NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            )
            print(f"搜索结果: {len(search_result.nodes)} 个节点")
            
            if search_result.nodes:
                print("✅ 搜索功能正常工作！")
                for i, node in enumerate(search_result.nodes[:2], 1):
                    print(f"  节点{i}: {node.uuid}")
                    print(f"  名称: {getattr(node, 'name', 'N/A')}")
            else:
                print("⚠️ 搜索未找到节点，可能需要更多时间处理")
        except Exception as search_error:
            print(f"❌ 搜索失败: {search_error}")
            import traceback
            traceback.print_exc()
        
        # 步骤9: 测试多Episode和用户搜索
        print("\n📋 步骤9: 测试复杂记忆场景...")
        try:
            # 添加用户Episode
            await client.add_episode(
                name="用户张三",
                episode_body="用户张三开始使用财务分析系统，偏好分析科技股票",
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="用户初始化"
            )
            
            # 添加对话Episode  
            await client.add_episode(
                name="财务对话",
                episode_body="张三问：苹果公司的财务状况如何？助手回答：苹果公司现金流充裕，ROE约30%",
                source=EpisodeType.message,
                reference_time=datetime.now(timezone.utc),
                source_description="财务对话"
            )
            
            print("✅ 复杂Episode添加成功")
            
            # 等待处理
            await asyncio.sleep(5)
            
            # 测试用户搜索
            user_search = await client._search("张三", NODE_HYBRID_SEARCH_EPISODE_MENTIONS)
            print(f"用户搜索结果: {len(user_search.nodes)} 个节点")
            
            if user_search.nodes:
                user_node_uuid = user_search.nodes[0].uuid
                print(f"找到用户节点: {user_node_uuid}")
                
                # 以用户为中心搜索
                edge_results = await client.search(
                    query="财务分析",
                    center_node_uuid=user_node_uuid,
                    num_results=3
                )
                print(f"用户中心搜索结果: {len(edge_results)} 条边")
                
                for edge in edge_results[:2]:
                    if hasattr(edge, 'fact'):
                        print(f"  记忆: {edge.fact[:60]}...")
                
                print("✅ 复杂记忆场景测试成功！")
            else:
                print("⚠️ 未找到用户节点")
        
        except Exception as complex_error:
            print(f"❌ 复杂记忆测试失败: {complex_error}")
            import traceback
            traceback.print_exc()
        
        print("\n🎉 Graphiti测试完全成功！自定义LLM集成正常工作")
        
        # 清理资源
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 整体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await proper_graphiti_test()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 自定义LLM的Graphiti测试完全成功！")
        print("✅ 确认问题已解决：")
        print("  - 自定义LLM包装器正常工作")
        print("  - 嵌入模型正常工作") 
        print("  - Episode添加成功")
        print("  - 搜索功能正常")
        print("  - 复杂记忆场景工作正常")
    else:
        print("❌ 测试失败，需要进一步调试")
        print("\n🔧 下一步：")
        print("1. 检查错误日志中的具体问题")
        print("2. 确认所有依赖包版本兼容")
        print("3. 验证Neo4j数据库状态")

if __name__ == "__main__":
    asyncio.run(main())