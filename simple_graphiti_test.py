
import asyncio
import os
import uuid
from datetime import datetime, timezone

from dotenv import load_dotenv
from graphiti_core import Graphiti
from graphiti_core.edges import EntityEdge
from graphiti_core.nodes import EpisodeType
from langchain_openai import ChatOpenAI

# Load environment variables from .env file
load_dotenv()

# --- Configuration ---
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4-turbo") # Use a model that's likely available

if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY is not set in the environment or .env file.")

# --- Helper Function ---
def edges_to_facts_string(entities: list[EntityEdge]) -> str:
    """Converts a list of graph edges to a formatted string of facts."""
    if not entities:
        return "No relevant facts found in memory."
    return "--- Relevant Facts from Memory ---" + "\n".join(
        f"- {edge.fact}" for edge in entities)

async def main():
    """Main function to run the interactive Graphiti memory chat test."""
    print("Initializing Graphiti client...")
    try:
        client = Graphiti(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
        await client.build_indices_and_constraints()
        print("Graphiti client initialized and indices are set up.")
    except Exception as e:
        print(f"Error connecting to Neo4j or setting up indices: {e}")
        print("Please ensure your Neo4j database is running and credentials in .env are correct.")
        return

    # --- LLM Initialization ---
    llm = ChatOpenAI(model=OPENAI_MODEL, temperature=0.7)

    # --- User and Session Setup ---
    user_name = "test_user"
    session_id = str(uuid.uuid4())
    print(f"Setting up user '{user_name}' for session '{session_id}'...")

    # Create an initial episode for the user
    await client.add_episode(
        name="User Session Start",
        episode_body=f"User '{user_name}' started a new chat session.",
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        source_description="System",
    )

    # Find the user's node to center memory searches
    try:
        nl = await client.search_nodes(user_name)
        if not nl.nodes:
            print(f"Could not find the node for user '{user_name}'. Memory may not be centered.")
            user_node_uuid = None
        else:
            user_node_uuid = nl.nodes[0].uuid
            print(f"Found user node. UUID: {user_node_uuid}")
    except Exception as e:
        print(f"Error finding user node: {e}")
        user_node_uuid = None


    print("\n--- Interactive Chat with Graphiti Memory ---")
    print("Type 'quit' to exit.")

    while True:
        try:
            user_input = input("You: ")
            if user_input.lower() == "quit":
                print("Exiting chat.")
                break

            # 1. Retrieve context from Graphiti memory
            print("Searching memory...")
            edge_results = await client.search(
                user_input,
                center_node_uuid=user_node_uuid, # type: ignore
                num_results=5 # Retrieve top 5 relevant facts
            )
            facts = edges_to_facts_string(edge_results)
            print(facts)

            # 2. Generate response using LLM with context
            system_prompt = (
                "You are a helpful assistant. Use the provided facts from your memory "
                "to answer the user's question. If the facts are not relevant, "
                "answer the question based on your general knowledge."
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": facts},
                {"role": "user", "content": user_input},
            ]

            print("Generating response...")
            response = await llm.ainvoke(messages)
            ai_response_content = response.content

            print(f"AI: {ai_response_content}")

            # 3. Store the new interaction in Graphiti memory
            print("Saving interaction to memory...")
            await client.add_episode(
                name="Conversation Turn",
                episode_body=f"User: {user_input}\nAI: {ai_response_content}",
                source=EpisodeType.message,
                reference_time=datetime.now(timezone.utc),
                source_description=f"Session: {session_id}",
            )
            print("Interaction saved.")

        except KeyboardInterrupt:
            print("\nExiting chat.")
            break
        except Exception as e:
            print(f"\nAn error occurred: {e}")
            continue

if __name__ == "__main__":
    asyncio.run(main())
