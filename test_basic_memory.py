#!/usr/bin/env python3
"""
基础Graphiti记忆管理系统测试
专注于验证核心功能
"""
import asyncio
import os
from dotenv import load_dotenv

async def test_basic_memory():
    """测试基础记忆功能"""
    print("🧠 基础Graphiti记忆管理系统测试")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 初始化组件
        print("📋 步骤1: 初始化组件...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.memory_manager import GraphitiMemoryManager
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.1,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器
        embedding_manager = EmbeddingManager()
        local_client = EmbeddingClientFactory.create_client(
            provider="sentence_transformers",
            model="all-MiniLM-L6-v2"
        )
        await embedding_manager.add_client("local", local_client)
        
        # 记忆管理器
        memory_manager = GraphitiMemoryManager(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="00000000",
            llm_manager=llm_manager,
            embedding_client=embedding_manager.get_primary_client(),
            group_id="basic_test"
        )
        
        await memory_manager.initialize()
        print("✅ 组件初始化完成")
        
        # 测试系统状态
        print("\n📋 步骤2: 检查系统状态...")
        status = await memory_manager.get_status()
        print("  系统状态:")
        for key, value in status.items():
            print(f"    {key}: {value}")
        
        # 清理图谱
        print("\n📋 步骤3: 清理图谱...")
        success = await memory_manager.clear_graph()
        print(f"  图谱清理: {'成功' if success else '失败'}")
        
        # 添加简单记忆
        print("\n📋 步骤4: 添加记忆...")
        try:
            memory_uuid = await memory_manager.add_memory(
                memory_type="fact",
                content="苹果公司是一家美国科技公司，主要生产iPhone和Mac电脑",
                category="company_info"
            )
            print(f"  ✅ 记忆添加成功: {memory_uuid}")
        except Exception as e:
            print(f"  ❌ 记忆添加失败: {e}")
            return False
        
        # 等待处理
        print("  ⏳ 等待记忆处理...")
        await asyncio.sleep(10)
        
        # 尝试使用原始方法添加对话
        print("\n📋 步骤5: 测试对话存储...")
        try:
            conversation_uuid = await memory_manager.store_conversation(
                user_id="test_user",
                session_id="test_session",
                user_input="苹果公司的主要产品是什么？",
                assistant_response="苹果公司的主要产品包括iPhone智能手机、Mac电脑、iPad平板电脑等。"
            )
            print(f"  ✅ 对话存储成功: {conversation_uuid}")
        except Exception as e:
            print(f"  ❌ 对话存储失败: {e}")
        
        # 等待处理
        await asyncio.sleep(5)
        
        # 测试检索
        print("\n📋 步骤6: 测试记忆检索...")
        try:
            from core.memory_manager import RetrievalContext, MemoryType
            
            retrieval_context = RetrievalContext(
                query="苹果公司 iPhone",
                memory_types=[MemoryType.FACT, MemoryType.CONVERSATION],
                max_results=5
            )
            
            memories = await memory_manager.retrieve_memories(
                retrieval_context=retrieval_context,
                user_id="test_user"
            )
            
            print(f"  ✅ 检索到 {len(memories)} 条记忆")
            for memory in memories[:2]:
                print(f"    - {memory.content[:60]}...")
                
        except Exception as e:
            print(f"  ❌ 记忆检索失败: {e}")
        
        # 测试Episodes获取
        print("\n📋 步骤7: 获取Episodes...")
        try:
            episodes = await memory_manager.get_episodes(limit=5)
            print(f"  ✅ 获取到 {len(episodes)} 个Episodes")
            for ep in episodes[:2]:
                print(f"    - {ep.get('name', 'Unknown')}")
        except Exception as e:
            print(f"  ❌ Episodes获取失败: {e}")
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        await memory_manager.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧠 Graphiti基础功能测试")
    print("🎯 验证核心记忆管理功能")
    print("=" * 60)
    
    success = await test_basic_memory()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    
    if success:
        print("🎉 基础功能测试成功!")
        print("✅ 记忆添加功能正常")
        print("✅ 对话存储功能正常") 
        print("✅ 记忆检索功能正常")
        print("✅ 系统状态检查正常")
    else:
        print("❌ 基础功能测试失败")
        print("🔧 需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())