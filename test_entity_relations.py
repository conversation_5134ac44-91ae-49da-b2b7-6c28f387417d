#!/usr/bin/env python3
"""
专门测试实体关系建立
"""
import asyncio
import os
from dotenv import load_dotenv

async def test_entity_relations():
    """测试实体关系建立"""
    print("🔗 测试实体关系建立")
    print("=" * 50)
    
    load_dotenv()
    
    try:
        # 初始化
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.memory_manager import GraphitiMemoryManager
        
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.1,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        embedding_manager = EmbeddingManager()
        local_client = EmbeddingClientFactory.create_client(
            provider="sentence_transformers",
            model="all-MiniLM-L6-v2"
        )
        await embedding_manager.add_client("local", local_client)
        
        memory_manager = GraphitiMemoryManager(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="00000000",
            llm_manager=llm_manager,
            embedding_client=embedding_manager.get_primary_client(),
            group_id="entity_test"
        )
        
        await memory_manager.initialize()
        print("✅ 初始化完成")
        
        # 清理图谱
        await memory_manager.clear_graph()
        print("✅ 图谱清理完成")
        
        # 添加包含明确实体关系的记忆
        print("\n🔗 添加具有明确实体关系的记忆...")
        
        # 记忆1: 公司和CEO关系
        await memory_manager.add_memory(
            memory_type="fact",
            content="蒂姆·库克是苹果公司的CEO，他领导着这家科技巨头",
            category="corporate_leadership"
        )
        print("  ✅ 添加记忆1: CEO关系")
        
        # 记忆2: 公司和产品关系
        await memory_manager.add_memory(
            memory_type="fact", 
            content="苹果公司生产iPhone手机，这是该公司最重要的产品",
            category="products"
        )
        print("  ✅ 添加记忆2: 产品关系")
        
        # 记忆3: 人物投资关系
        await memory_manager.add_memory(
            memory_type="fact",
            content="投资者张三购买了苹果公司的股票，他看好苹果的未来发展",
            category="investments"
        )
        print("  ✅ 添加记忆3: 投资关系")
        
        # 等待处理
        print("\n⏳ 等待实体提取和关系建立...")
        await asyncio.sleep(15)  # 给足够时间进行处理
        
        # 检查数据库状态
        print("\n📊 检查数据库状态...")
        from neo4j import AsyncGraphDatabase
        
        driver = AsyncGraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '00000000'))
        
        async with driver.session() as session:
            # 检查节点数量
            result = await session.run('MATCH (n) RETURN labels(n) as labels, count(n) as count')
            records = await result.data()
            
            print("  节点统计:")
            total_entities = 0
            for record in records:
                labels = record['labels']
                count = record['count']
                print(f"    {labels}: {count}")
                if 'Entity' in labels:
                    total_entities += count
            
            # 检查关系数量
            result = await session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count')
            records = await result.data()
            
            print("  关系统计:")
            total_relations = 0
            for record in records:
                rel_type = record['rel_type']
                count = record['count']
                print(f"    {rel_type}: {count}")
                if rel_type == 'RELATES_TO':
                    total_relations += count
            
            # 查看具体实体
            if total_entities > 0:
                result = await session.run('MATCH (n:Entity) RETURN n.name as name LIMIT 5')
                records = await result.data()
                print("  发现的实体:")
                for record in records:
                    name = record.get('name', 'Unknown')
                    print(f"    - {name}")
                    
            # 查看具体关系
            if total_relations > 0:
                result = await session.run('''
                    MATCH (a:Entity)-[r:RELATES_TO]->(b:Entity) 
                    RETURN a.name as source, r.fact as relation, b.name as target 
                    LIMIT 3
                ''')
                records = await result.data()
                print("  发现的关系:")
                for record in records:
                    source = record.get('source', 'Unknown')
                    relation = record.get('relation', 'Unknown')
                    target = record.get('target', 'Unknown')
                    print(f"    {source} -> {target}: {relation[:50]}...")
        
        await driver.close()
        
        # 使用Graphiti API搜索
        print("\n🔍 使用Graphiti API搜索...")
        
        # 搜索节点
        nodes = await memory_manager.search_nodes("苹果公司", max_results=5)
        print(f"  搜索'苹果公司'节点: {len(nodes)} 个")
        
        # 搜索关系
        facts = await memory_manager.search_facts("CEO 领导", max_results=5)
        print(f"  搜索'CEO 领导'关系: {len(facts)} 个")
        
        for fact in facts[:2]:
            print(f"    - {fact.get('fact', '')[:60]}...")
        
        # 评估结果
        print(f"\n📊 实体关系建立评估:")
        print(f"  实体数量: {total_entities}")
        print(f"  关系数量: {total_relations}")
        
        if total_entities >= 3 and total_relations >= 1:
            print("🎉 实体关系建立成功!")
            success = True
        elif total_entities >= 2:
            print("⚠️ 实体创建成功，但关系建立不完整")
            success = False
        else:
            print("❌ 实体关系建立失败")
            success = False
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        await memory_manager.cleanup()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_entity_relations()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 实体关系测试成功!")
        print("✅ 实体提取功能正常")
        print("✅ 关系建立功能正常")
    else:
        print("❌ 实体关系测试需要改进")
        print("🔧 建议检查LLM响应格式")

if __name__ == "__main__":
    asyncio.run(main())