#!/usr/bin/env python3
"""
修复后的Graphiti测试 - 确保使用自定义LLM客户端
"""
import asyncio
import os
from datetime import datetime, timezone
from dotenv import load_dotenv


async def test_graphiti_with_custom_llm():
    """使用完全自定义的LLM客户端测试Graphiti"""
    print("🔧 测试Graphiti with 完全自定义LLM客户端")
    print("=" * 60)
    
    load_dotenv()
    
    try:
        # 步骤1: 创建我们的LLM和嵌入客户端
        print("📋 步骤1: 创建自定义LLM客户端...")
        from core.llm_client import LLMManager, LLMClientFactory
        from core.embedding_client import EmbeddingManager, EmbeddingClientFactory
        from core.graphiti_llm import GraphitiLLM
        from core.graphiti_embedder import GraphitiEmbedder
        
        # LLM管理器
        llm_manager = LLMManager()
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ 未找到API密钥")
            return False
            
        openai_client = LLMClientFactory.create_client(
            provider="openai",
            api_key=api_key,
            model="glm-4-flash",
            temperature=0.3,
            base_url=os.getenv("OPENAI_BASE_URL"),
        )
        await llm_manager.add_client("openai", openai_client)
        
        # 嵌入管理器 
        embedding_manager = EmbeddingManager()
        chinese_client = EmbeddingClientFactory.create_chinese_client(
            model="bge-large-zh",
            use_case="general"
        )
        await embedding_manager.add_client("chinese", chinese_client)
        
        print("✅ 自定义客户端创建完成")
        
        # 步骤2: 创建Graphiti包装器
        print("\n📋 步骤2: 创建Graphiti包装器...")
        graphiti_llm = GraphitiLLM(llm_manager)
        
        # 修复：使用正确的方法获取嵌入客户端
        primary_embedder = embedding_manager.get_primary_client()  # 使用get_primary_client()
        graphiti_embedder = GraphitiEmbedder(primary_embedder)
        
        print("✅ Graphiti包装器创建完成")
        
        # 步骤3: 测试LLM包装器
        print("\n📋 步骤3: 测试LLM包装器...")
        try:
            test_messages = ["测试LLM响应"]
            llm_response = await graphiti_llm._generate_response(test_messages)
            print(f"  LLM响应类型: {type(llm_response)}")
            if isinstance(llm_response, dict) and 'content' in llm_response:
                print(f"  响应内容: {llm_response['content'][:50]}...")
                print("  ✅ LLM包装器正常工作")
            else:
                print(f"  ⚠️ LLM响应格式异常: {llm_response}")
        except Exception as e:
            print(f"  ❌ LLM包装器测试失败: {e}")
            return False
        
        # 步骤4: 使用自定义客户端初始化Graphiti
        print("\n📋 步骤4: 使用自定义客户端初始化Graphiti...")
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        # 关键：必须传入我们的自定义LLM和嵌入客户端
        client = Graphiti(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="password",
            llm_client=graphiti_llm,         # 使用我们的包装器
            embedder=graphiti_embedder,     # 使用我们的嵌入器
            store_raw_episode_content=True
        )
        
        await client.build_indices_and_constraints()
        print("✅ Graphiti初始化成功，使用自定义LLM")
        
        # 步骤5: 测试Episode添加
        print("\n📋 步骤5: 测试Episode添加...")
        try:
            await client.add_episode(
                name="自定义LLM测试Episode",
                episode_body="测试使用自定义LLM客户端的Graphiti功能",
                source=EpisodeType.text,
                reference_time=datetime.now(timezone.utc),
                source_description="自定义LLM测试"
            )
            print("✅ Episode添加成功！")
        except Exception as e:
            print(f"❌ Episode添加失败: {e}")
            return False
        
        # 步骤6: 等待处理并测试搜索
        print("\n📋 步骤6: 等待处理...")
        await asyncio.sleep(5)
        
        print("📋 步骤7: 测试搜索...")
        try:
            from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            search_result = await client._search(
                "自定义LLM",
                NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            )
            print(f"搜索结果: {len(search_result.nodes)} 个节点")
            
            if search_result.nodes:
                print("✅ 搜索功能正常工作！")
                for i, node in enumerate(search_result.nodes[:2], 1):
                    print(f"  节点{i}: {node.uuid}")
            else:
                print("⚠️ 搜索未找到节点")
                
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return False
        
        # 步骤8: 测试多Episode添加
        print("\n📋 步骤8: 测试多个Episode...")
        episodes = [
            {
                "name": "用户张三",
                "body": "用户张三开始使用财务分析系统",
                "desc": "用户初始化"
            },
            {
                "name": "财务对话1",
                "body": "张三: 苹果公司财务状况如何？\n助手: 苹果现金流充裕，ROE约30%",
                "desc": "财务对话"
            }
        ]
        
        for i, ep in enumerate(episodes, 1):
            try:
                await client.add_episode(
                    name=ep["name"],
                    episode_body=ep["body"],
                    source=EpisodeType.text,
                    reference_time=datetime.now(timezone.utc),
                    source_description=ep["desc"]
                )
                print(f"  Episode {i} 添加成功: {ep['name']}")
            except Exception as e:
                print(f"  Episode {i} 添加失败: {e}")
        
        # 步骤9: 等待并搜索
        print("\n⏳ 等待处理...")
        await asyncio.sleep(5)
        
        print("📋 步骤9: 测试用户搜索...")
        try:
            user_search = await client._search(
                "张三",
                NODE_HYBRID_SEARCH_EPISODE_MENTIONS
            )
            print(f"用户搜索结果: {len(user_search.nodes)} 个节点")
            
            if user_search.nodes:
                user_node_uuid = user_search.nodes[0].uuid
                print(f"找到用户节点: {user_node_uuid}")
                
                # 以用户为中心搜索
                print("测试以用户为中心的搜索...")
                edge_results = await client.search(
                    query="财务",
                    center_node_uuid=user_node_uuid,
                    num_results=3
                )
                print(f"相关记忆: {len(edge_results)} 条")
                
                for edge in edge_results[:2]:
                    if hasattr(edge, 'fact'):
                        print(f"  记忆: {edge.fact[:60]}...")
                
                print("✅ 用户中心搜索成功！")
            else:
                print("⚠️ 未找到用户节点")
                
        except Exception as e:
            print(f"❌ 用户搜索失败: {e}")
        
        print("\n🎉 自定义LLM的Graphiti测试完全成功！")
        
        # 清理
        await llm_manager.cleanup()
        await embedding_manager.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🧠 Graphiti记忆功能修复测试")
    print("🎯 确保使用自定义LLM客户端")
    print("=" * 60)
    
    success = await test_graphiti_with_custom_llm()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！Graphiti记忆功能正常工作")
        print("✅ 确认问题已修复：")
        print("  - Episode添加成功")
        print("  - 搜索功能正常") 
        print("  - 用户节点创建成功")
        print("  - 以用户为中心的搜索工作")
    else:
        print("❌ 测试失败，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())